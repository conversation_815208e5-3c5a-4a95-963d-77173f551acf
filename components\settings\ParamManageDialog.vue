<template>
	<!-- <div class="param-manage-dialog-container"> -->
  <el-dialog
    draggable
    v-model="dialogVisible"
    title="管理参数"
    width="840px"
    :align-center="true"
    :destroy-on-close="true"
  >
    <div class="param-manage-content">
      
      <el-table :data="paramsList" style="width: 100%" border empty-text="暂无数据">
        <el-table-column prop="name" label="参数名">
          <template #default="scope">
            <el-input v-model="scope.row.name" placeholder="参数名" />
          </template>
        </el-table-column>
        <el-table-column prop="type" label="参数类型">
          <template #default="scope">
            <el-select v-model="scope.row.type" placeholder="请选择类型">
              <el-option label="文本" value="文本" />
              <el-option label="数字" value="数字" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="example" label="参数示例">
          <template #default="scope">
            <el-input v-model="scope.row.example" placeholder="请输入示例值" />
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 参数使用说明 -->
      <div class="param-usage">
        <div class="usage-content">
          <div class="usage-image">
            <img :src="getParamUsageImageUrl()" alt="参数使用说明" />
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="confirmDialog">确认</el-button>
      </span>
    </template>
  </el-dialog>
	<!-- </div> -->
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { getMediaUrl } from '@/utils/mediaUtils';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  content: {
    type: Object,
    default: () => ({})
  },
  // 添加所有参数列表属性
  allParams: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:visible', 'confirm']);

const dialogVisible = ref(false);
const paramsList = ref([]);

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    // 在打开对话框时提取参数列表
    extractParams();
  }
});

// 监听对话框关闭
watch(() => dialogVisible.value, (newVal) => {
  if (!newVal) {
    emit('update:visible', false);
  }
});

// 从内容中提取参数 - 增强版，从DOM中提取所有参数
const extractParams = () => {
  paramsList.value = [];
  
  try {
    console.log('开始从文档中收集所有参数...');
    
    // 使用Set存储所有参数ID，防止重复
    const allParamIds = new Set();
    
    // 1. 首先从全局参数管理器获取已使用的参数ID
    if (window.GLOBAL_PARAM_MANAGER && typeof window.GLOBAL_PARAM_MANAGER.getUsedIds === 'function') {
      try {
        const usedIds = window.GLOBAL_PARAM_MANAGER.getUsedIds();
        console.log('从全局参数管理器获取的参数ID:', usedIds);
        usedIds.forEach(id => allParamIds.add(id.toString()));
      } catch (error) {
        console.error('从全局参数管理器获取参数ID失败:', error);
      }
    }
    
    // 2. 从内容中搜索所有参数
    const findParamsInElement = (element) => {
      if (!element) return;
      
      // 查找所有输入框和按钮中的参数
      const paramElements = element.querySelectorAll('input.j-btn[data-param-id], span.j-btn[data-param-id], .param-input, [data-param-id]');
      paramElements.forEach(elem => {
        try {
          // 尝试从data-param-id属性获取参数ID
          let paramId = elem.getAttribute('data-param-id');
          
          if (!paramId) {
            // 从按钮值或内容中提取
            const paramValue = elem.value || elem.textContent || '';
            const match = paramValue.match(/{#param(\d+)#}/);
            if (match && match[1]) {
              paramId = match[1];
            }
          }
          
          if (paramId) {
            allParamIds.add(paramId.toString());
          }
        } catch (e) {
          console.warn('处理参数元素时出错:', e);
        }
      });
      
      // 在editable-link-url和其他输入区域中搜索参数文本
      const editableElements = element.querySelectorAll('.editable-link-url, [contenteditable="true"]');
      editableElements.forEach(el => {
        try {
          const text = el.innerHTML || el.textContent || '';
          const regex = /{#param(\d+)#}/g;
          let match;
          
          while ((match = regex.exec(text)) !== null) {
            if (match[1]) {
              allParamIds.add(match[1]);
            }
          }
        } catch (e) {
          console.warn('搜索可编辑元素中的参数时出错:', e);
        }
      });
    };
    
    // 3. 从DOM的不同部分收集参数
    // 从编辑区域收集
    findParamsInElement(document.querySelector('.middle-section'));
    
    // 从右侧设置面板收集
    findParamsInElement(document.querySelector('.right-panel'));
    
    // 从点击事件设置区域收集
    findParamsInElement(document.querySelector('.click-event-settings'));
    
    // 从按钮设置区域收集
    findParamsInElement(document.querySelector('.button-settings'));
    
    // 4. 从内容缓存中查找参数
    if (window.TEMPLATE_CONTENTS) {
      const regex = /{#param(\d+)#}/g;
      try {
        window.TEMPLATE_CONTENTS.forEach(content => {
          if (!content) return;
          
          // 检查内容对象的所有属性
          Object.entries(content).forEach(([key, value]) => {
            if (typeof value === 'string' && value.includes('{#param')) {
              let match;
              while ((match = regex.exec(value)) !== null) {
                if (match[1]) {
                  allParamIds.add(match[1]);
                }
              }
            } else if (key === 'actionUrl' || key === 'actionPath' || key === 'content') {
              // 检查嵌套对象中的常见参数位置
              if (typeof value === 'string' && value.includes('{#param')) {
                let match;
                while ((match = regex.exec(value)) !== null) {
                  if (match[1]) {
                    allParamIds.add(match[1]);
                  }
                }
              }
            }
          });
        });
      } catch (e) {
        console.warn('从TEMPLATE_CONTENTS收集参数失败:', e);
      }
    }
    
    // 5. 从点击事件缓存中查找参数
    if (window.CLICK_EVENT_CACHE) {
      const regex = /{#param(\d+)#}/g;
      try {
        Object.entries(window.CLICK_EVENT_CACHE).forEach(([key, data]) => {
          if (!data) return;
          
          // 检查actionUrl和actionPath属性
          ['actionUrl', 'actionPath'].forEach(prop => {
            if (data[prop] && typeof data[prop] === 'string') {
              let match;
              while ((match = regex.exec(data[prop])) !== null) {
                if (match[1]) {
                  allParamIds.add(match[1]);
                }
              }
            }
          });
        });
      } catch (e) {
        console.warn('从CLICK_EVENT_CACHE收集参数失败:', e);
      }
    }
    
    // 检查是否有实际参数被收集
    if (allParamIds.size === 0) {
      console.log('未找到任何参数，参数列表为空');
      return;
    }
    
    // 6. 将收集到的所有参数ID转换为参数对象
    console.log('收集到的所有参数ID:', Array.from(allParamIds));
    
    // 获取现有的TEMPLATE_PARAMS用于获取参数名称
    const existingParams = window.TEMPLATE_PARAMS || [];
    
    // 按ID排序并转换为参数对象
    paramsList.value = Array.from(allParamIds)
      .map(id => parseInt(id))
      .filter(id => !isNaN(id))
      .sort((a, b) => a - b)
      .map(id => {
        // 查找是否有现有参数信息
        const existingParam = existingParams.find(p => p.id.toString() === id.toString());
        if (existingParam) {
          return {
            id: id.toString(),
            name: existingParam.name || `param${id}`,
            type: existingParam.type || '文本',
            example: existingParam.example || ''
          };
        }
        // 否则创建新参数
        return {
          id: id.toString(),
          name: `param${id}`,
          type: '文本',
          example: ''
        };
      });
    
    console.log('最终收集到的参数列表:', paramsList.value);
  } catch (error) {
    console.error('收集参数失败:', error);
    
    // 如果收集失败，尝试使用一个简单的方法
    const simpleParams = new Set();
    
    try {
      // 在整个文档中查找参数元素
      document.querySelectorAll('input.j-btn[data-param-id], span.j-btn[data-param-id]').forEach(el => {
        const paramId = el.getAttribute('data-param-id');
        if (paramId) {
          simpleParams.add(paramId);
        }
      });
      
      // 查找所有HTML中的参数格式文本
      const allText = document.body.innerHTML;
      const regex = /{#param(\d+)#}/g;
      let match;
      while ((match = regex.exec(allText)) !== null) {
        if (match[1]) {
          simpleParams.add(match[1]);
        }
      }
      
      // 转换为参数列表
      paramsList.value = Array.from(simpleParams)
        .map(id => parseInt(id))
        .filter(id => !isNaN(id))
        .sort((a, b) => a - b)
        .map(id => ({
          id: id.toString(),
          name: `param${id}`,
          type: '文本',
          example: ''
        }));
      
      console.log('使用简单方法收集到的参数:', paramsList.value);
    } catch (e) {
      console.error('简单参数收集也失败:', e);
      paramsList.value = [];
    }
  }
};

// 获取参数使用说明图片URL
const getParamUsageImageUrl = () => {
  return getMediaUrl('/aim_files/aim_defult/param-usage.png');
};

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};

// 确认更改
const confirmDialog = () => {
  // 发出确认事件
  emit('confirm', paramsList.value);
  
  // 更新全局参数
  try {
    // 更新全局参数列表
    window.TEMPLATE_PARAMS = [...paramsList.value];
    
    // 广播参数更新事件，通知其他组件
    if (window.PARAM_EVENT_BUS) {
      window.PARAM_EVENT_BUS.emit('params-updated', paramsList.value);
    }
    
    // 触发DOM中的自定义事件，用于通知所有相关组件
    const event = new CustomEvent('template-params-updated', { 
      detail: { params: paramsList.value } 
    });
    document.dispatchEvent(event);
    
    console.log('参数更新完成，已广播更新事件');
  } catch (err) {
    console.error('广播参数更新时出错:', err);
  }
  
  // 关闭对话框
  dialogVisible.value = false;
};
</script>

<style scoped lang="scss">
.param-manage-dialog-container{
	:deep(.el-dialog){
		background: #fff !important;
		margin: 20px auto !important;
		padding: 20px !important;
  }
	:deep(.el-dialog__header){
		padding: 0 !important;
		box-shadow: none !important;
	}
}
.param-manage-content {
	max-height: 534px;
	overflow: auto;
  position: relative;
	margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.param-usage {
  margin-top: 20px;
}

.usage-content {
  display: flex;
  justify-content: center;
}

.usage-image {
	width: 100%;
  overflow: hidden;
}

.usage-image img {
  width: 100%;
  height: auto;
  object-fit: contain;
}
</style> 