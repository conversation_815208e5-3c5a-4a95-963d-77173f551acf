<template>
  <el-dialog
    v-model="dialogVisible"
    title="发送短链"
    width="760px"
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="170px"
      class="send-link-form"
    >
      <el-form-item label="发送对象：" prop="sendType">
        <el-radio-group v-model="form.sendType">
          <el-radio :value="1">手动输入</el-radio>
          <el-radio :value="2">文件导入</el-radio>
          <el-radio :value="3">选择人群</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="form.sendType === 1" label="手机号码：" prop="phones">
        <el-input 
          type="textarea" 
          v-model="form.phones" 
          placeholder="多个手机号码以逗号格式隔开"
          :rows="4"
        />
      </el-form-item>

      <el-form-item v-if="form.sendType === 2" label="上传文件：" prop="file">
        <div class="file-upload-box">
          <div class="file-input">
            <span v-if="form.file">{{ form.file.name }}</span>
            <span v-else class="placeholder">请选择文件</span>
            <el-icon v-if="form.file || form.filePath" class="clear-icon" @click.stop="clearFile"><Close /></el-icon>
          </div>
          <el-button class="upload-btn" type="primary" @click="triggerFileInput">
            上传文件
          </el-button>
          <input 
            ref="fileInputRef" 
            type="file" 
            style="display: none;" 
            accept=".xlsx,.xls,.csv" 
            @change="handleFileChange" 
          />
        </div>
      </el-form-item>
      <el-form-item v-if="form.sendType === 3" label="选择人群：" prop="crowdId">
        <div class="crowd-selector">
          <!-- 搜索框 -->
          <el-input
            v-model="searchKeyword"
            placeholder="请输入关键词搜索"
            @input="handleSearch"
          >
          </el-input>
          <!-- 多选列表 -->
          <div class="checkbox-group">
            <span class="selected-count" v-if="segmentList.length > 0">共计 {{ selectedPhoneCount }} 个手机号码</span>
            <div v-if="isSegmentLoading">加载中...</div>
            <!-- 无数据提示 -->
            <div v-else-if="segmentList.length === 0 && !isSegmentLoading">暂无搜索结果</div>
            <el-checkbox-group v-model="selectedSegments" v-else>
              <el-checkbox
                v-for="segment in segmentList"
                :key="segment.id"
                :value="segment.segmentId"
              >
                {{ segment.segmentName }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="渠道类型：" prop="channelType" >
        <el-select v-model="form.channelType" placeholder="请选择渠道类型">
          <el-option
            v-for="channel in channelTypes"
            :key="channel"
            :label="CHANNEL_TYPE_MAP[channel] || channel"
            :value="channel"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="号码解析能力校验：">
        <el-switch
          v-model="form.isAimAbility"
          active-value="1"
          inactive-value="0"
          active-text="开启"
          inactive-text="关闭"
        />
      </el-form-item>
      <el-form-item label="短链内容：" prop="selectedShortLink">
        <div v-if="templateShortLinks.length > 0">
          <el-radio-group v-model="form.selectedShortLink">
            <div v-for="link in templateShortLinks" :key="link.aimUrl" class="short-link-item">
              <el-radio :value="link.aimUrl">
                <div class="short-link-info">
                  <div class="short-link-wrapper" @mouseenter="showCopyIcon = link.aimUrl" @mouseleave="showCopyIcon = ''">
                    <span class="short-link-url">{{ link.aimUrl }}</span>
                    <el-icon 
                      v-if="showCopyIcon === link.aimUrl" 
                      class="copy-icon" 
                      @click="copyLink(link.aimUrl)"
                    >
                      <DocumentCopy />
                    </el-icon>
                  </div>
                </div>
              </el-radio>
            </div>
          </el-radio-group>
        </div>
        <div v-else class="no-links">
          当前模板没有可用短链，请先生成短链
        </div>
      </el-form-item>

      <el-form-item label="短信内容：" prop="msgContent">
        <el-input 
          type="textarea" 
          v-model="form.msgContent" 
          placeholder="请输入短信内容"
          :rows="4"
        />
      </el-form-item>

  
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="debouncedHandleConfirm" :loading="isLoading" >发送</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import api from "@/api/index";
import { copyTextToClipboard } from '@/utils/copyUtils';
import debounce from 'lodash/debounce';
const dialogVisible = ref(false);
const formRef = ref(null);
const fileInputRef = ref(null);
const templateData = ref(null);
const templateShortLinks = ref([]);
const channelTypes = ref([]);
const showCopyIcon = ref(''); 
const isLoading = ref(false); // 定义 loading 状态

const form = reactive({
  sendType: 1,
  phones: '',
  file: null,
  filePath: '', 
  channelType: '',
  selectedShortLink: '',
  msgContent: '',
  appKey: '',
  templateId: '',
  tplId: '',
  isAimAbility: 0
});
// 添加渠道类型映射
const CHANNEL_TYPE_MAP = {
  'AIM_SH_UNICOM': '阅信+上海联通'
};
const rules = {
  sendType: [
    { required: true, message: '请选择发送对象类型', trigger: 'change' }
  ],
  phones: [
    { 
      required: true, 
      validator: (rule, value, callback) => {
        if (form.sendType === 1 && (!value || value.trim() === '')) {
          callback(new Error('请输入手机号码'));
        } else if (form.sendType === 1) {
          // 检查是否包含中文逗号
          if (value.includes('，')) {
            callback(new Error('请输入英文逗号进行分割'));
            return;
          }
          // 分割手机号码
          const phoneList = value.split(',').filter(item => item.trim());
          if (phoneList.length > 10) {
            callback(new Error('最多支持输入10条数据'));
          } else {
            // 手机号码正则表达式，以中国手机号码为例
            const phoneRegex = /^1[3-9]\d{9}$/;
            for (const phone of phoneList) {
              if (!phoneRegex.test(phone.trim())) {
                callback(new Error('请输入正确的手机号码格式'));
                return;
              }
            }
            callback();
          }
        } else {
          callback();
        }
      }, 
      trigger: 'blur' 
    }
  ],
  file: [
    { 
      required: true, 
      validator: (rule, value, callback) => {
        if (form.sendType === 2 && !form.file) {
          callback(new Error('请上传文件'));
        } else {
          callback();
        }
      }, 
      trigger: 'change' 
    }
  ],
  channelType: [
    { required: true, message: '请选择渠道类型', trigger: 'change' }
  ],
  selectedShortLink: [
    { required: true, message: '请选择短链内容', trigger: 'change' }
  ],
  msgContent: [
    { required: true, message: '请输入短信内容', trigger: 'blur' }
  ]
};

// 复制短链函数
const copyLink = (link) => {
  copyTextToClipboard(link)
    .then(() => {
      ElMessage.success('复制成功');
    })
    .catch(() => {
      ElMessage.error('复制失败，请手动复制');
    });
};

// 触发文件选择器
const triggerFileInput = () => {
  fileInputRef.value.click();
};

const handleFileChange = async (event) => {
  const file = event.target.files[0];
  if (!file) return;
  
  // 检查文件大小（5MB = 5 * 1024 * 1024 bytes）
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    ElMessage.error('文件大小不能超过5MB!');
    event.target.value = ''; // 清空选择
    return;
  }
  
  // 检查文件类型
  const fileExtension = file.name.split('.').pop().toLowerCase();
  const allowedExtensions = ['xlsx', 'xls', 'csv'];
  if (!allowedExtensions.includes(fileExtension)) {
    ElMessage.error('请上传Excel格式文件xls, xlsx!');
    event.target.value = ''; // 清空选择
    return;
  }
  
  form.file = file;

  try {
    // 上传文件获取 filePath
    const uploadFormData = new FormData();
    uploadFormData.append('file', file);
    uploadFormData.append('appKey', form.appKey);
    const uploadRes = await api.uploadPhoneFile(uploadFormData);
    if (uploadRes.code !== 0) {
      ElMessage.error(uploadRes.msg || '文件上传失败');
      form.file = null;
      event.target.value = ''; // 清空选择
      return;
    }
    // 从 uploadRes.data.path 中获取文件路径
    form.filePath = uploadRes.data.path; 
  } catch (error) {
    console.error('文件上传失败:', error);
    ElMessage.error('文件上传失败，请重试');
    form.file = null;
    event.target.value = ''; // 清空选择
  }
};

// 清除已选择的文件
const clearFile = () => {
  form.file = null;
  if (fileInputRef.value) {
    fileInputRef.value.value = '';
  }
};

// 打开对话框
const open = async (template) => {
  if (!template) {
    ElMessage.error('模板数据不完整');
    return;
  }

  dialogVisible.value = true;
  templateData.value = template;
  
  // 重置表单
  Object.assign(form, {
    sendType: 1,
    phones: '',
    file: null,
    channelType: '',
    selectedShortLink: '',
    msgContent: '',
    appKey: '',
    templateId: '',
    tplId: ''
  });

  // 如果有fileInputRef，重置文件输入
  if (fileInputRef.value) {
    fileInputRef.value.value = '';
  }

  try {
    // 直接使用传入的模板数据，而不是重新请求
    // 设置表单基本信息
    form.appKey = template.appKey;
    form.templateId = template.templateId;
    
    // 提取渠道类型
    if (template.channels && template.channels.length > 0) {
      channelTypes.value = [...new Set(template.channels.map(channel => channel.channelType))];
      
      // 默认选择第一个渠道类型
      if (channelTypes.value.length > 0) {
        form.channelType = channelTypes.value[0];
      }
      
      // 从渠道中提取tplId
      const firstChannel = template.channels[0];
      if (firstChannel) {
        form.tplId = firstChannel.tplId || '';
      }
      
      // 提取短链数据
      templateShortLinks.value = [];
      template.channels.forEach(channel => {
				if (channel.urls) {
					// urls可能是数组或字符串，需要处理
					let urlsList = [];
					try {
							if (typeof channel.urls === 'string') {
							try {
									// 先尝试解析为 JSON
									urlsList = JSON.parse(channel.urls);
							} catch {
									// 解析失败，按逗号分隔处理
									urlsList = channel.urls.split(',').map(url => ({
									aimUrl: url.trim(),
									aimCode: ''
									}));
							}
							} else if (Array.isArray(channel.urls)) {
							urlsList = channel.urls;
							}
					} catch (e) {
							console.error('解析URLs失败:', e);
					}
          urlsList.forEach(url => {
            // 强制转换 aimUrl 为字符串，并过滤空值
            const aimUrl = typeof url.aimUrl === 'string' ? url.aimUrl.trim() : JSON.stringify(url.aimUrl).trim();
            if (aimUrl) {
              templateShortLinks.value.push({
                aimUrl: aimUrl, // 确保为字符串
                aimCode: url.aimCode || '',
                channelType: channel.channelType
              });
            }
          });
				}
			});
      
      // 默认选中第一个短链
      if (templateShortLinks.value.length > 0) {
        form.selectedShortLink = templateShortLinks.value[0].aimUrl;
      }
    }
  } catch (error) {
    console.error('处理模板数据失败:', error);
    ElMessage.error('处理模板数据失败，请重试');
  }
};

// 监听渠道类型变化，过滤相关短链
watch(() => form.channelType, (newChannelType) => {
  if (!newChannelType) return;
  
  // 根据渠道类型过滤短链
  const filteredLinks = templateShortLinks.value.filter(link => link.channelType === newChannelType);
  
  // 如果过滤后没有短链，但总列表有，则不过滤
  if (filteredLinks.length === 0 && templateShortLinks.value.length > 0) {
    return;
  }
  
  // 如果当前选中的短链不在过滤结果中，则选择第一个
  const currentLinkExists = filteredLinks.some(link => link.aimUrl === form.selectedShortLink);
  if (!currentLinkExists && filteredLinks.length > 0) {
    form.selectedShortLink = filteredLinks[0].aimUrl;
  }
});

// 确认发送
const handleConfirm = async () => {
  if (!formRef.value || isLoading.value) return;

  try {
    isLoading.value = true; // 开始请求，开启 loading
    const validateResult = await formRef.value.validate();
    if (!validateResult) {
      // 表单验证失败，不继续执行
      return;
    }
    // 检查 selectedShortLink 是否为有效字符串
    if (typeof form.selectedShortLink !== 'string' || form.selectedShortLink.trim() === '') {
      ElMessage.error('请选择有效的短链');
      return;
    }
    // 从选中的短链中提取 aimCode
    const selectedLink = templateShortLinks.value.find(link => link.aimUrl === form.selectedShortLink);
    if (!selectedLink) {
      ElMessage.error('请选择有效的短链');
      return;
    }

    // 确认提示
    let phoneCount;
    if (form.sendType === 1) {
      phoneCount = form.phones.split(',').filter(p => p.trim()).length;
    } else if (form.sendType === 3) {
      phoneCount = selectedSegments.value.length;
    } else {
      phoneCount = '文件中的所有';
    }
      
    const confirmMsg = `确定要发送短信吗？`;
    
    await ElMessageBox.confirm(confirmMsg, '发送确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    if (form.sendType === 1) {
      // 手动输入发送
      const params = {
        appKey: form.appKey,
        sendType: 1, // 立即发送
        // datpretime: '',
        phones: form.phones,
        aimUrl: selectedLink.aimUrl,
        aimCode: selectedLink.aimCode,
        msgContent: form.msgContent,
        templateId: form.templateId,
        tplId: form.tplId,
        channelType: form.channelType,
        isAimAbility: form.isAimAbility
      };
      console.log('发送请求参数:', params);
      const res = await api.sendTemplate(params);
      if (res.code === 0) {
        ElMessage.success(res.msg || '短信发送成功');
        dialogVisible.value = false;
      }
    } else if (form.sendType === 2) {
      // 文件导入发送
      if (!form.file) {
        ElMessage.error('请选择有效的文件');
        return;
      }

      const formData = new FormData();
      formData.append('filePath', form.filePath); 
      formData.append('appKey', form.appKey);
      formData.append('sendType', 1); // 立即发送
      formData.append('aimUrl', selectedLink.aimUrl);
      formData.append('aimCode', selectedLink.aimCode);
      formData.append('msgContent', form.msgContent);
      formData.append('templateId', form.templateId);
      formData.append('tplId', form.tplId);
      formData.append('channelType', form.channelType);
      formData.append('isAimAbility', form.isAimAbility); 

      const res = await api.sendTemplateByFile(formData);
      if (res.code === 0) {
        ElMessage.success(res.msg || '文件上传成功，短信发送处理中');
        dialogVisible.value = false;
      }
    } else if (form.sendType === 3) {
      // 选择人群发送
      const selectedSegmentsData = segmentList.value
        .filter(segment => selectedSegments.value.includes(segment.segmentId))
        .map(segment => ({
          segmentId: segment.segmentId,
          segmentName: segment.segmentName
        }));
      const selectedSegmentIds = selectedSegmentsData.map(item => item.segmentId).join(',');
      const selectedSegmentNames = selectedSegmentsData.map(item => item.segmentName).join(',');
      
      const params = {
        appKey: form.appKey,
        sendType: 1,
        aimUrl: selectedLink.aimUrl,
        aimCode: selectedLink.aimCode,
        msgContent: form.msgContent,
        templateId: form.templateId,
        tplId: form.tplId,
        channelType: form.channelType,
        isAimAbility: form.isAimAbility,
        segmentIds: selectedSegmentIds,
        segmentNames: selectedSegmentNames,
        phonesCount: selectedPhoneCount.value,
      };
      
      const res = await api.sendTemplateBySegment(params);
      if (res.code === 0) {
        ElMessage.success(res.msg || '人群短信发送处理中');
        dialogVisible.value = false;
        handleCancel();
      }
    }
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消发送
      return;
    }
    console.error('请求捕获到错误:', error);
  } finally {
    isLoading.value = false; // 请求结束，关闭 loading
  }
};
// 使用 lodash 的防抖函数创建防抖后的函数，设置延迟时间为 2000 毫秒
const debouncedHandleConfirm = debounce(handleConfirm, 1000);
// 取消
const handleCancel = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  
  // 清空文件输入和缓存
  if (fileInputRef.value) {
    fileInputRef.value.value = '';
  }
  form.file = null;
  loadedSegmentIds.value.clear(); // 清空已加载缓存
  segmentCountMap.value.clear(); // 清空号码数缓存

  selectedSegments.value = [];       // 清空已选中的人群ID
  selectedCrowdList.value = [];      // 清空已选中的人群详细信息
  segmentList.value = [];            // 清空当前显示的人群列表
  searchKeyword.value = '';          // 清空搜索关键词
};
// 监听对话框关闭事件
watch(dialogVisible, (newVal) => {
  if (!newVal) { // 当对话框关闭时
    handleCancel();
  }
});

const searchKeyword = ref('');
const segmentList = ref([]);
const selectedSegments = ref([]);
const isSegmentLoading = ref(false);
const segmentCountMap = ref(new Map()); //缓存每个 segmentId 对应的号码数

const loadedSegmentIds = ref(new Set()); 
// 获取选中人群的号码数
const fetchSegmentCounts = async (segmentIds) => {
  for (const segmentId of segmentIds) {
    // 跳过已加载过的人群ID
    if (loadedSegmentIds.value.has(segmentId)) {
      continue;
    }
    try {
      const res = await api.querySegmentCount({ segmentId }); 
      if (res.code === 0) {
        const count = Number(res.data.segmentCount);
        segmentCountMap.value.set(segmentId, count);
        loadedSegmentIds.value.add(segmentId); // 标记为已加载
      } else {
        ElMessage.error(`获取人群 ${segmentId} 号码数失败：${res.msg || '接口异常'}`);
      }
    } catch (error) {
      ElMessage.error(`获取人群 ${segmentId} 号码数请求失败`);
    }
  }
};

// 存储已选中的人群详细信息（包含segmentId和segmentName）
const selectedCrowdList = ref([]);

// 监听选中人群变化（保持 deep: true）
watch(selectedSegments, (newIds) => {
   // 更新已选中的人群详细信息（从当前segmentList中提取）
   selectedCrowdList.value = segmentList.value.filter(seg => newIds.includes(seg.segmentId));
  fetchSegmentCounts(newIds); // 仅处理未缓存的ID
}, { deep: true });


// 号码总和计算（保持不变）
const selectedPhoneCount = computed(() => {
  return selectedSegments.value.reduce((sum, segmentId) => {
    return sum + (segmentCountMap.value.get(segmentId) || 0);
  }, 0);
});

// 监听 sendType 变化，仅清空数据不自动请求
watch(() => form.sendType, (newVal) => {
  if (newVal === 3) {
     // 仅清空搜索词和列表，保留已选数据
     searchKeyword.value = ''; 
    segmentList.value = [];
  }
});
// 获取人群数据
const fetchSegmentInfos = async () => {
  if (form.sendType !== 3) return;
  isSegmentLoading.value = true;
  try {
    const keyword = searchKeyword.value.trim();
    let newSegments = [];
    
    // 仅当有关键词时调用接口查询
    if (keyword) { 
      const res = await api.querySegmentInfos({
        segmentName: keyword, 
      });
      if (res.code !== 0) {
        ElMessage.error(res.msg || '获取人群数据失败');
        return; // 接口异常时不更新newSegments
      }
      newSegments = res.data || [];
    }

    // 合并已选数据（无论是否调用接口）
    const mergedSegments = [
      ...selectedCrowdList.value,  // 已选数据始终优先
      ...newSegments.filter(seg => 
        !selectedCrowdList.value.some(sc => sc.segmentId === seg.segmentId)
      )
    ];
    segmentList.value = mergedSegments;
  } catch (error) {
    ElMessage.error('网络请求失败，请稍后重试');
  } finally {
    isSegmentLoading.value = false;
  }
};

// 搜索处理（保持防抖）
const handleSearch = debounce(() => {
  fetchSegmentInfos();
}, 600); // 延迟500毫秒

defineExpose({
  open
});
</script>

<style scoped lang="scss">
.send-link-form {
  :deep(.el-textarea__inner),:deep(.el-input__inner){
    &::placeholder {
      font-size: 13px;
      color: #ccc;
      font-weight: normal;
    }
  }
  
  .el-input, .el-textarea, .el-select {
    width: 460px;
  }
  
  .file-upload-box {
    display: flex;
    align-items: center;
    width: 460px;
    height: 38px;
    
    .file-input {
      flex: 1;
      padding: 0 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      border: 1px solid #DCDFE6;
      border-radius: 4px;
      height: 100%;
      line-height: 36px;
      margin-right: 10px;
      color: #606266;
      position: relative;
      
      .placeholder {
        color: #C0C4CC;
      }
      
      .clear-icon {
        position: absolute;
        right: 5px;
        top: 50%;
        transform: translateY(-50%);
        color: #C0C4CC;
        cursor: pointer;
        
        &:hover {
          color: #909399;
        }
      }
    }
    
    .upload-btn {
      flex-shrink: 0;
    }
  }
  
  .short-link-item {
    // margin-bottom: 10px;
    width: 220px;
    .short-link-info {
      display: flex;
      flex-direction: column;
      
      .short-link-wrapper {
        display: flex;
        align-items: center;
        position: relative;
        padding-right: 16px; 
        
        .short-link-url {
          white-space: normal;
          word-break: break-all;
          overflow-wrap: break-word;
          max-width: 260px;
          line-height: 16px;
        }
        
        .copy-icon {
          position: absolute;
          right: -8px;
          cursor: pointer;
          color: #909399;
          opacity: 0.8;
          transition: opacity 0.3s;
          
          &:hover {
            opacity: 1;
            color: #409EFF;
          }
        }
      }
    }
  }
  
  .no-links {
    color: #f56c6c;
    line-height: 32px;
  }
}

.dialog-footer {
  text-align: right;
}

.crowd-selector {
  width: 460px;
  // border: 1px solid #dcdfe6;
  padding: 12px;
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  border-radius: 4px;
  .el-input{
    width: 436px;
  }
  .search-btn{
    background: #409eff;
    color: #fff;
    border-radius: 0 4px 4px 0;

  }
  .el-button:hover{
    background-color: #79bbff;
    border-color: #79bbff ;
    color: #fff;
    outline: none;
  }
}

.checkbox-group {
  margin-top: 14px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  .selected-count{
    margin-left: 2px;
  }
  .el-checkbox{
    height: 14px;
  }
}

.el-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 180px;
  overflow: auto;
  .el-checkbox{
    margin-bottom: 10px;
  }
}
</style>
