import request from '@/utils/request';
import FormData from 'form-data';

// 定义模板相关的接口
const templateApi = {
    // 获取模板分类接口
    // getMediaDirList: (data) => {
    //     return request.post('/mediaDir/mediaDirList',data);
    // },
    // 获取模板分类接口
    getMediaDirList: (data) => {
        const formData = convertToFormData(data);
        return request.post('/mediaDir/mediaDirList', formData);
    },
    // 添加模板分类接口
    updateDir: (data) => {
        const formData = convertToFormData(data);
        return request.post('/mediaDir/updateDir', formData);
    },
    // 删除模板分类接口
    deleteDir: (data) => {
        const formData = convertToFormData(data);
        return request.post('/mediaDir/deleteDir', formData);
    },

    // 模板列表接口 
    getTemBasicList: (data) => {
        const formData = convertToFormData(data);
        return request.post('/aimTem/getTemBasicList', formData);
    },
    // 获取模板详情接口
    getTemplate: (data) => {
        const formData = convertToFormData(data);
        return request.post('/aimTem/getTemplateById', formData);
    },
    // 新增模板接口
    addTemplate: (data) => {
        const formData = convertToFormData(data);
        return request.post('/aimTem/addTemplate ', formData);
    },
    // 修改模板接口
    updateTemplate: (data) => {
        const formData = convertToFormData(data);
        return request.post('/aimTem/updateTemplate', formData);
    },
    // 删除模板接口
    delTemplate: (data) => {
        const formData = convertToFormData(data);
        return request.post('/aimTem/delTemplateById', formData);
    },
    // 发送模板接口
    sendTemplate: (data) => {
        const formData = convertToFormData(data);
        return request.post('/aim/send/sendTemplateByPhone', formData);
    },
    // 发送文件模板接口
    sendTemplateByFile: (data) => {
        // const formData = convertToFormData(data);
        return request.post('/aim/send/sendTemplateByFile', data);
    },
    //上传号码文件
    uploadPhoneFile: (data) => {
        // const formData = convertToFormData(data);
        return request.post('/aim/send/upload', data);
    },
    // 模板启用/禁用接口
    setTemplate: (data) => {
        const formData = convertToFormData(data);
        return request.post('/aimTem/setTemplateStateById', formData);
    },
    // 生成短链接口
    applyAimUrl: (data) => {
        const formData = convertToFormData(data);
        return request.post('/aimTem/applyAimUrl', formData);
    },
    // 审核模板接口
    auditTemplate: (data) => {
        const formData = convertToFormData(data);
        return request.post('/aimTem/auditTemplate', formData);
    },
    // 获取媒体列表接口
    getMediaList: (data) => {
        const formData = convertToFormData(data);
        return request.post('/aimTem/getMediaList', formData);
    },
    //搜索人群接口
    querySegmentInfos: (data) => {
        const formData = convertToFormData(data);
        return request.post('/label/querySegmentInfos', formData);
    },
    //选择人群接口
    querySegmentCount: (data) => {
        const formData = convertToFormData(data);
        return request.post('/label/querySegmentCount ', formData);
    },
    //人群发送接口
    sendTemplateBySegment: (data) => {
        const formData = convertToFormData(data);
        return request.post('/aim/send/sendTemplateBySegment', formData);
    }

};
// 转换数据为FormData的辅助函数
function convertToFormData(obj) {
    const formData = new FormData();
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        // 处理数组类型
        if (Array.isArray(obj[key])) {
          formData.append(key, JSON.stringify(obj[key]));
        } else {
          formData.append(key, obj[key]);
        }
      }
    }
    return formData;
  }


export default {
    ...templateApi,
};