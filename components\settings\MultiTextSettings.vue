<template>
  <div class="multi-text-settings">
    <div class="settings-content">
      <!-- 图文对数量设置 -->
      <div class="setting-group">
        <h4>标准图文对数量</h4>
        <div class="pair-count-options">
          <el-radio-group v-model="pairCount" @change="handlePairCountChange">
            <el-radio :value="1">1对</el-radio>
            <el-radio :value="2">2对</el-radio>
            <el-radio :value="3">3对</el-radio>
          </el-radio-group>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, inject, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  content: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['update:content', 'change', 'pair-count-change']);

// 注入多图文设置
const multiTextSettings = inject('multiTextSettings', null);

// 响应式数据
const pairCount = ref(1);

// 监听注入的设置变化
watch(() => multiTextSettings?.pairCount, (newValue) => {
  console.log('MultiTextSettings - 监听到设置变化:', newValue, '当前值:', pairCount.value);
  if (newValue !== undefined && newValue !== pairCount.value) {
    pairCount.value = newValue;
    console.log('MultiTextSettings - 更新本地状态为:', pairCount.value);
  }
}, { immediate: true, deep: true });

// 监听标签页切换等外部重置操作
watch(pairCount, (newValue, oldValue) => {
  console.log('MultiTextSettings - pairCount变化:', oldValue, '->', newValue);
  
  // 如果本地状态变化且与注入的设置不一致，则同步到注入的设置
  if (multiTextSettings && multiTextSettings.pairCount !== newValue) {
    multiTextSettings.pairCount = newValue;
    console.log('MultiTextSettings - 同步到注入的设置:', newValue);
  }
});

// 监听全局事件总线的重置事件
onMounted(() => {
  // 监听全局重置事件
  if (window.PARAM_EVENT_BUS) {
    window.PARAM_EVENT_BUS.on('multitext-settings-reset', (data) => {
      console.log('MultiTextSettings - 收到重置事件:', data);
      if (data.pairCount !== undefined && data.pairCount !== pairCount.value) {
        pairCount.value = data.pairCount;
        console.log('MultiTextSettings - 通过事件重置为:', data.pairCount);
      }
    });
  }
});

// 组件销毁时清理事件监听
onUnmounted(() => {
  if (window.PARAM_EVENT_BUS) {
    window.PARAM_EVENT_BUS.off('multitext-settings-reset');
  }
});

// 处理图文对数量变化
const handlePairCountChange = (value) => {
  console.log('MultiTextSettings - 图文对数量变化:', value);
  
  pairCount.value = value;
  
  // 发出变化事件
  emit('change', {
    pairCount: value
  });
  
  emit('pair-count-change', value);
};

// 组件挂载时初始化
onMounted(() => {
  console.log('MultiTextSettings - 组件挂载，多图文设置:', multiTextSettings);
  
  // 从注入的设置中获取初始值
  if (multiTextSettings?.pairCount !== undefined) {
    pairCount.value = multiTextSettings.pairCount;
    console.log('MultiTextSettings - 初始化pairCount为:', pairCount.value);
  } else {
    // 如果没有设置，则使用默认值1
    pairCount.value = 1;
    if (multiTextSettings) {
      multiTextSettings.pairCount = 1;
    }
    console.log('MultiTextSettings - 使用默认值初始化pairCount为:', pairCount.value);
  }
});

// 暴露方法给父组件
defineExpose({
  pairCount,
  handlePairCountChange
});
</script>

<style scoped lang="scss">
.multi-text-settings {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.settings-content {
  padding: 20px;
}

.setting-group {
  margin-bottom: 24px;
}

.setting-group:last-child {
  margin-bottom: 0;
}

.setting-group h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.pair-count-options {
  margin-bottom: 12px;
}


:deep(.el-radio) {
  margin-right: 20px;
  margin-bottom: 0;
}

:deep(.el-radio__label) {
  font-size: 14px;
  color: #606266;
}

:deep(.el-radio.is-checked .el-radio__label) {
  color: #409eff;
}
</style> 