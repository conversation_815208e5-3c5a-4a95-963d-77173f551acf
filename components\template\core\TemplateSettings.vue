<template>
  <div class="right-section" :class="{ visible: content }" v-show="content">
    <div class="setting-container">
      <div class="setting-header">
        <h3 class="setting-title">{{ getSettingTitle() }}</h3>
        <el-icon class="close-icon" @click="handleClose"><Close /></el-icon>
      </div>
      <div class="setting-content">
        <div :class="{'image-setting': content && (content.type === 'image' || content.type === 'background' || content.type === 'video'), 'text-setting': content && content.type === 'text', 'button-setting': content && content.type === 'button'}">

          <div v-if="content && (content.type === 'image' || content.type === 'background' || content.type === 'video')">
            <ImageSettings 
              :image-content="content" 
              :template="template" 
              :app-key="appKey"
              @update:imageContent="emit('update:content', $event)"
              @insert-param="handleParamInsert"
              @manage-param="handleParamManage"
            />
            <!-- 只有非轮播图且非视频内容才显示通用的点击事件设置 -->
            <ClickEventSettings 
              v-if="!isCarouselContent && content.type !== 'video'"
              :content="content"
              @update:content="emit('update:content', $event)"
              placeholder="打开浏览器"
              @insert-param="handleParamInsert"
              @manage-param="handleParamManage"
            />
          </div>

          <div v-if="content && content.type === 'text'">
            <div v-if="isNotificationTemplate && !content.isNotificationSettings">
              <!-- 通知类模板的文本内容不显示任何设置 -->
            </div>
            <!-- 通知类模板使用专门的设置组件 -->
            <NotificationSettings
              v-else-if="isNotificationTemplate"
              :template-data="template"
              @insert-param="handleParamInsert"
              @manage-param="handleParamManage"
              @param-count-change="handleParamCountChange"
              @button-count-change="handleButtonCountChange"
            />
            <!-- 其他模板使用原有的参数设置 -->
            <ParamSettings
              v-else
              @insert-param="handleParamInsert"
              @manage-param="handleParamManage"
              data-source="text-setting"
            />
          </div>

          <!-- 一般通知类的设置面板 -->
          <div v-if="content && content.isNotificationSettings">
            <NotificationSettings
              :template-data="template"
              @insert-param="handleParamInsert"
              @manage-param="handleParamManage"
              @param-count-change="handleParamCountChange"
              @button-count-change="handleButtonCountChange"
            />
          </div>

          <!-- 多图文模板的设置面板 -->
          <div v-if="content && content.isMultiTextSettings">
            <MultiTextSettings
              :content="content"
              @change="handleMultiTextSettingsChange"
              @pair-count-change="handlePairCountChange"
            />
          </div>

          <!-- 长文本模板的设置面板 -->
          <div v-if="content && content.isLongTextSettings">
            <LongTextSettings
              :content="content"
              @change="handleLongTextSettingsChange"
              @settings-change="handleLongTextSettingsChange"
            />
          </div>

          <!-- 横滑设置面板 -->
          <div v-else-if="content && content.isHorizontalSwipeSettings">
            <HorizontalSwipeSettings
              :content="content"
              :template="template"
              :app-key="appKey"
              @change="handleHorizontalSwipeSettingsChange"
              @settings-change="handleHorizontalSwipeSettingsChange"
              @update-card-image="handleUpdateCardImage"
              @update-card-content="handleUpdateCardContent"
              @add-card="handleAddCard"
              @remove-card="handleRemoveCard"
              @insert-param="handleParamInsert"
              @manage-param="handleParamManage"
              @card-selected="handleCardSelected"
            />
          </div>

          <!-- 电商设置面板 -->
          <div v-else-if="content && content.isEcommerceSettings">
            <EcommerceSettings 
              :content="content"
              :app-key="appKey"
              @update:content="emit('update:content', $event)"
            />
          </div>

          <!-- 多商品设置面板 -->
          <div v-else-if="content && content.isMultiProductSettings">
            <MultiProductSettings
              :content="content"
              :template="template"
              :app-key="appKey"
              @update:content="emit('update:content', $event)"
              @input="emit('input', $event)"
              @param-insert="handleParamInsert"
              @param-manage="handleParamManage"
              @settings-change="emit('settings-change', $event)"
            />
          </div>

          <!-- 单卡券设置面板 -->
          <div v-else-if="content && content.isCardVoucherSettings">
            <CardVoucherSettings
              :content="content"
              :contents="selectedTemplateContents"
              :template="template"
              :app-key="appKey"
              @update:content="emit('update:content', $event)"
              @input="emit('input', $event)"
              @param-insert="handleParamInsert"
              @param-manage="handleParamManage"
              @update="emit('settings-change', $event)"
              @contents-change="emit('contents-change', $event)"
            />
          </div>

          <!-- 券+商品设置面板 -->
          <div v-else-if="content && content.isCouponProductSettings">
            <CouponProductSettings
              :content="content"
              :template="template"
              :app-key="appKey" 
              @update:content="emit('update:content', $event)"
              @input="emit('input', $event)"
              @param-insert="handleParamInsert"
              @param-manage="handleParamManage"
              @settings-change="emit('settings-change', $event)"
            />
          </div>

          <div v-if="content && content.type === 'button'">
            <div class="setting-group">
              <div class="setting-item">
                <div class="setting-label">按钮名称（最多15位）</div>
                <RichParamInput
                  v-model="content.content"
                  :maxLength="15"
                  :showParamButtons="false"
                  placeholder="请输入按钮名称"
                  componentType="button"
                  @update:modelValue="handleButtonContentChange"
                  @param-inserted="handleButtonParamInserted"
                />
              </div>
              <div class="setting-item">
                <ParamOperations 
                  source="button-setting" 
                  component="button-settings"
                  @insert-param="handleParamInsert" 
                  @manage-param="handleParamManage"
                />
              </div>
            </div>
            <ClickEventSettings 
              :content="content"
              @update:content="emit('update:content', $event)"
              @insert-param="handleParamInsert"
              @manage-param="handleParamManage"
            />
          </div>
        </div>
        <FactoryList v-if="template" :factory-list="getFactoryList(template)" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, inject, nextTick, watch, onBeforeUnmount, watchEffect } from 'vue';
import { Close } from '@element-plus/icons-vue';
import * as paramUtils from '@/utils/paramUtils';
import templateFactory from '@/factories/TemplateFactory.js';

// 导入所有设置组件
import EcommerceSettings from '@/components/settings/EcommerceSettings.vue';
import NotificationSettings from '@/components/settings/NotificationSettings.vue';
import MultiTextSettings from '@/components/settings/MultiTextSettings.vue';
import LongTextSettings from '@/components/settings/LongTextSettings.vue';
import ImageSettings from '@/components/settings/ImageSettings.vue';
import ClickEventSettings from '@/components/settings/ClickEventSettings.vue';
import ParamSettings from '@/components/settings/ParamSettings.vue';
import ParamOperations from '@/components/common/ParamOperations.vue';
import FactoryList from '@/components/settings/FactoryList.vue';
import RichParamInput from '@/components/richtext/RichParamInput.vue';
import HorizontalSwipeSettings from '@/components/settings/HorizontalSwipeSettings.vue';
import MultiProductSettings from '@/components/settings/MultiProductSettings.vue';
import CardVoucherSettings from '@/components/settings/CardVoucherSettings.vue';

const props = defineProps({
  content: {
    type: Object,
    default: null
  },
  template: {
    type: Object,
    default: null
  },
  appKey: {
    type: String,
    default: ''
  },
  buttonContent: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['close', 'param-insert', 'param-manage', 'update:content', 'param-count-change', 'button-count-change', 'pair-count-change', 'settings-change', 'input', 'focus', 'blur', 'update-card-image', 'update-card-content', 'add-card', 'remove-card', 'card-selected', 'update-contents', 'contents-change']);

// 当前按钮数据的独立状态
const currentButtonData = ref({});

// 注入当前模板信息（在setup函数中）
const currentTemplate = inject('currentTemplate', null);

// 注入共享数据
const templateContents = inject('templateContents', ref([]));
const selectedTemplateContents = inject('selectedTemplateContents', ref([]));

// 添加调试信息
watch(() => selectedTemplateContents.value, (newContents) => {
  console.log('TemplateSettings - selectedTemplateContents 变化:', newContents);
}, { immediate: true, deep: true });

watch(() => props.content, (newContent) => {
  console.log('TemplateSettings - content 变化:', newContent);
  if (newContent && newContent.isCardVoucherSettings) {
    console.log('TemplateSettings - 检测到单卡券设置面板');
    console.log('TemplateSettings - 当前selectedTemplateContents:', selectedTemplateContents.value);
  }
}, { immediate: true });

const isRedPacketTemplate = computed(() => {
  return window.TEMPLATE_IS_REDPACKET === true;
});

// 判断是否是通知类模板
const isNotificationTemplate = computed(() => {
  return props.template?.value?.cardId?.includes('notification') ||
         props.template?.value?.tplType === 'notification';
});

// 判断是否是多图文模板
const isMultiTextTemplate = computed(() => {
  if (!props.template) return false;
  
  const templateData = props.template.value || props.template;
  // 使用简化的多图文模板判断逻辑
  return templateData.cardId === 'com.hbm.multitext' || 
         templateData.tplType === 'multitext';
});

// 判断是否是长文本模板
const isLongTextTemplate = computed(() => {
  if (!props.template) return false;
  
  const templateData = props.template.value || props.template;
  // 使用简化的长文本模板判断逻辑
  return templateData.cardId === 'com.hbm.longtext' || 
         templateData.tplType === 'longtext';
});

// 判断是否是单卡券模板
const isCardVoucherTemplate = computed(() => {
  if (!props.template) return false;
  
  const templateData = props.template.value || props.template;
  // 使用TemplateFactory的精确检测方法
  const templateType = templateFactory.detectTemplateType(templateData);
  if (templateType === 'cardvoucher') {
    return true;
  }
  
  // 检查cardId
  if (templateData.cardId === 'com.hbm.cardVoucher') {
    return true;
  }
  
  return false;
});

// 判断是否是轮播图内容
const isCarouselContent = computed(() => {
  console.log('TemplateSettings - 判断是否是轮播图内容:', {
    模板: props.template,
    内容: props.content
  });
  
  if (!props.template) {
    console.log('TemplateSettings - 没有模板数据');
    return false;
  }
  
  // 支持两种传参方式：template.value 或直接 template
  const templateData = props.template.value || props.template;
  console.log('TemplateSettings - 模板数据:', templateData);
  
  // 首先检查模板是否是轮播图模板
  const isCarouselTemplate = templateFactory.isCarouselTemplate(templateData);
  console.log('TemplateSettings - 轮播图模板判断结果:', isCarouselTemplate);
  
  // 如果是轮播图模板，则所有图片内容都被视为轮播图内容
  if (isCarouselTemplate && props.content && 
      (props.content.type === 'image' || props.content.type === 'background' || props.content.type === 'video')) {
    console.log('TemplateSettings - 是轮播图模板且内容是图片类型，返回true');
    return true;
  }
  
  // 检查内容本身是否标记为轮播图
  if (props.content && (props.content.isCarousel || props.content.carouselImages)) {
    console.log('TemplateSettings - 内容本身标记为轮播图，返回true');
    return true;
  }
  
  console.log('TemplateSettings - 不是轮播图内容，返回false');
  return false;
});

// 获取设置面板标题
const getSettingTitle = () => {
  if (!props.content) return '图片设置';
  
  // 如果是电商设置面板
  if (props.content.isEcommerceSettings) {
    return '电商设置';
  }
  
  // 如果是多商品设置面板
  if (props.content.isMultiProductSettings) {
    return '电商(多商品)设置';
  }
  
  // 如果是券+商品设置面板
  if (props.content.isCouponProductSettings) {
    return '券商品设置';
  }
  
  // 如果是单卡券设置面板
  if (props.content.isCardVoucherSettings) {
    return '单卡券设置';
  }
  
  // 如果是一般通知类的设置面板
  if (props.content.isNotificationSettings) {
    return '设置';
  }
  
  // 如果是多图文模板的设置面板
  if (props.content.isMultiTextSettings) {
    return '多图文设置';
  }
  
  // 如果是长文本模板的设置面板
  if (props.content.isLongTextSettings) {
    return '长文本设置';
  }
  
  // 如果是横滑设置面板
  if (props.content.isHorizontalSwipeSettings) {
    return '横滑设置';
  }
  
  switch (props.content.type) {
    case 'image': return '图片设置';
    // case 'background': return '图片设置';
    case 'video': return '视频设置';
    case 'text': return '文本设置';
    case 'button': return '按钮设置';
    default: return '图片设置';
  }
};

// 获取工厂列表
const getFactoryList = (template) => {
  if (!template?.factoryInfos) return [];
  
  // 处理factoryInfos可能是字符串或数组的情况
  let factoryArray = [];
  if (Array.isArray(template.factoryInfos)) {
    factoryArray = template.factoryInfos;
  } else if (typeof template.factoryInfos === 'string') {
    factoryArray = template.factoryInfos.split(',').map(code => code.trim());
  } else {
    return []; // 不是有效格式则返回空数组
  }
  
  return factoryArray.map(code => ({
    code: code,
    name: code  // 这里的name会被FactoryList组件内部的FACTORY_MAP处理
  }));
};

// 处理内容更新
const handleContentUpdate = (updatedContent) => {
  emit('update:content', updatedContent);
};

// 关闭面板
const handleClose = () => {
  emit('close');
};

// 插入参数到按钮名称编辑器
const insertParamToButtonName = async (paramInfo) => {
  // 现在使用RichParamInput组件，不需要复杂的处理逻辑
  console.log('参数插入由RichParamInput组件处理:', paramInfo);
  return true;
};

// 处理按钮参数插入事件
const handleButtonParamInserted = (paramInfo) => {
  console.log('按钮参数插入事件:', paramInfo);
  // 触发上层组件的参数插入事件
  emit('param-insert', paramInfo);
};

// 简化的参数插入处理  
const handleParamInsert = (paramInfo) => {
  console.log('处理参数插入:', paramInfo);
  
  // 检查是否来自按钮设置面板
  if (paramInfo && paramInfo.source === 'button-setting' && props.content && props.content.type === 'button') {
    console.log('通过按钮设置面板插入参数成功');
    return;
  }
  
  // 触发上层组件的事件
  emit('param-insert', paramInfo);
};

// 处理参数管理
const handleParamManage = () => {
  emit('param-manage');
};

// 处理通知设置变更
const handleNotificationSettingsChange = (settings) => {
  console.log('通知设置变更:', settings);
  // 这里可以添加具体的处理逻辑
  // 比如更新模板的参数配置等
};

// 处理参数对数量变化
const handleParamCountChange = (count) => {
  console.log('参数对数量变化:', count);
  // 向父组件发出事件
  emit('param-count-change', count);
};

// 处理按钮数量变化
const handleButtonCountChange = (count) => {
  console.log('按钮数量变化:', count);
  // 向父组件发出事件
  emit('button-count-change', count);
};

// 处理多图文设置变更
const handleMultiTextSettingsChange = (settings) => {
  console.log('多图文设置变更:', settings);
  // 向父组件发出事件，传递图文对数量变化
  if (settings.pairCount) {
    emit('pair-count-change', settings.pairCount);
  }
};

// 处理图文对数量变化
const handlePairCountChange = (count) => {
  console.log('图文对数量变化:', count);
  // 向父组件发出事件
  emit('pair-count-change', count);
};

// 处理长文本设置变更
const handleLongTextSettingsChange = (settings) => {
  console.log('长文本设置变更:', settings);
  // 向父组件发出事件
  emit('settings-change', settings);
};

// 处理横滑设置变更
const handleHorizontalSwipeSettingsChange = (settings) => {
  console.log('横滑设置变更:', settings);
  // 向父组件发出事件
  emit('settings-change', settings);
};

// 处理横滑卡片图片更新
const handleUpdateCardImage = (imageUpdateData) => {
  console.log('TemplateSettings - 处理卡片图片更新:', imageUpdateData);
  // 向父组件传递图片更新事件
  emit('update-card-image', imageUpdateData);
};

// 处理横滑卡片内容更新
const handleUpdateCardContent = (contentUpdateData) => {
  console.log('TemplateSettings - 处理卡片内容更新:', contentUpdateData);
  // 向父组件传递内容更新事件
  emit('update-card-content', contentUpdateData);
};

// 处理添加横滑卡片
const handleAddCard = (cardData) => {
  console.log('TemplateSettings - 处理添加卡片:', cardData);
  // 向父组件传递添加卡片事件
  emit('add-card', cardData);
};

// 处理删除横滑卡片
const handleRemoveCard = (cardData) => {
  console.log('TemplateSettings - 处理删除卡片:', cardData);
  // 向父组件传递删除卡片事件
  emit('remove-card', cardData);
};

// 处理横滑卡片选择
const handleCardSelected = (selectedCard) => {
  console.log('TemplateSettings - 处理卡片选择:', selectedCard);
  // 向父组件传递卡片选择事件
  emit('card-selected', selectedCard);
};

// 简化的按钮名称处理
const handleButtonContentChange = (newContent) => {
  if (!props.content) return;
  
  console.log('按钮内容变化:', newContent);
  
  // 直接更新按钮内容
  props.content.content = newContent;
  
  // 触发内容更新事件
  emit('update:content', {
    ...props.content,
    content: newContent
  });
};

// 组件销毁时清理计时器
onBeforeUnmount(() => {
  // 清理任何可能存在的计时器
  if (window.buttonInputTimer) {
    clearTimeout(window.buttonInputTimer);
    window.buttonInputTimer = null;
  }
});

// 暴露方法给父组件
defineExpose({
  insertParamToButtonName
});
</script>

<style scoped lang="scss">
.right-section {
  width: 410px;
  height: 100%;
  background-color: #fff;
  border-left: 1px solid #e6e6e6;
  position: absolute;
  right: -410px;
  top: 0;
  transition: right 0.3s;
  z-index: 10;

  &.visible {
    right: 0;
  }
}

.setting-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.setting-header {
  padding: 10px 14px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .setting-title {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
  }
  
  .close-icon {
    cursor: pointer;
    font-size: 18px;
    color: #909399;
    
    &:hover {
      color: #409eff;
    }
  }
}

.setting-content {
  flex: 1;
  overflow-y: auto;
}

.setting-group {
  margin-bottom: 20px;
  
  .setting-item {
    padding: 14px 14px 0;
    .setting-label {
      font-size: 14px;
      color: #606266;
      margin-bottom: 8px;
    }
  }
}

// 不同类型内容的设置样式
.image-setting, .text-setting, .button-setting {
  margin-bottom: 20px;
}
</style> 