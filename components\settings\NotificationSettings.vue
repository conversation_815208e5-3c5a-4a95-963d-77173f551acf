<template>
  <div class="notification-settings">
    <!-- <div class="settings-header">
      <h3>通知类设置</h3>
      <el-button 
        type="text" 
        @click="$emit('close')"
        class="close-btn"
      >
        <el-icon><Close /></el-icon>
      </el-button>
    </div> -->

    <div class="settings-content">
      <!-- 参数对数量设置 -->
      <div class="setting-group">
        <h4>参数对数量</h4>
        <div class="param-count-options">
          <el-radio-group v-model="paramCount" @change="handleParamCountChange">
            <el-radio :value="2">2对</el-radio>
            <el-radio :value="3">3对</el-radio>
            <el-radio :value="4">4对</el-radio>
            <el-radio :value="5">5对</el-radio>
          </el-radio-group>
          <div class="second-row">
            <el-radio-group v-model="paramCount" @change="handleParamCountChange">
              <el-radio :value="6">6对</el-radio>
              <el-radio :value="7">7对</el-radio>
              <el-radio :value="8">8对</el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>

      <!-- 按钮数量设置 -->
      <div class="setting-group">
        <h4>按钮数量</h4>
        <div class="button-count-options">
          <el-radio-group v-model="buttonCount" @change="handleButtonCountChange">
            <el-radio :value="1">1个</el-radio>
            <el-radio :value="2">2个</el-radio>
          </el-radio-group>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, inject, reactive, onMounted } from 'vue';
import { Close } from '@element-plus/icons-vue';

// 定义事件
const emit = defineEmits(['close', 'paramCountChange', 'buttonCountChange']);

// 定义props
const props = defineProps({
  templateData: {
    type: Object,
    default: null
  }
});

// 组件状态
const paramCount = ref(2); // 默认显示2对额外参数（不包括第一对固定参数）
const buttonCount = ref(2); // 默认显示2个按钮

// 注入设置数据，使用稳定的写法
const notificationSettings = inject('notificationSettings', null);

// 监听注入的设置变化，同步到本地状态
watch(() => notificationSettings?.maxVisibleParams, (newValue) => {
  console.log('NotificationSettings - 监听到参数数量变化:', newValue, '当前值:', paramCount.value);
  if (newValue !== undefined && newValue !== paramCount.value) {
    paramCount.value = newValue;
    console.log('NotificationSettings - 更新本地参数数量为:', paramCount.value);
  }
}, { immediate: true, deep: true });

watch(() => notificationSettings?.maxVisibleButtons, (newValue) => {
  console.log('NotificationSettings - 监听到按钮数量变化:', newValue, '当前值:', buttonCount.value);
  if (newValue !== undefined && newValue !== buttonCount.value) {
    buttonCount.value = newValue;
    console.log('NotificationSettings - 更新本地按钮数量为:', buttonCount.value);
  }
}, { immediate: true, deep: true });

// 监听本地状态变化，同步到注入的设置
watch(paramCount, (newValue, oldValue) => {
  console.log('NotificationSettings - paramCount变化:', oldValue, '->', newValue);
  
  // 如果本地状态变化且与注入的设置不一致，则同步到注入的设置
  if (notificationSettings && notificationSettings.maxVisibleParams !== newValue) {
    notificationSettings.maxVisibleParams = newValue;
    console.log('NotificationSettings - 同步参数数量到注入的设置:', newValue);
  }
});

watch(buttonCount, (newValue, oldValue) => {
  console.log('NotificationSettings - buttonCount变化:', oldValue, '->', newValue);
  
  // 如果本地状态变化且与注入的设置不一致，则同步到注入的设置
  if (notificationSettings && notificationSettings.maxVisibleButtons !== newValue) {
    notificationSettings.maxVisibleButtons = newValue;
    console.log('NotificationSettings - 同步按钮数量到注入的设置:', newValue);
  }
});

// 监听模板变化
watch(() => props.templateData, (newTemplate) => {
  if (newTemplate && newTemplate.tplType === '通知类') {
    // 检查是否已经有用户设置的值
    const hasUserSettings = notificationSettings && 
                           (notificationSettings.maxVisibleParams !== undefined || 
                            notificationSettings.maxVisibleButtons !== undefined);
    
    console.log('NotificationSettings - 模板变化监听器，是否有用户设置:', hasUserSettings);
    
    // 只有在没有用户设置的情况下才设置默认值
    if (!hasUserSettings) {
      // 根据模板类型设置默认值
      if (newTemplate.templateName && newTemplate.templateName.includes('增强')) {
        // 增强通知类：第一对固定显示（不计入设置），默认再显示2对参数
        // 所以设置为2对时，实际显示3对（第一对固定 + 2对可配置）
        paramCount.value = 2; 
        buttonCount.value = 2; // 增强通知类默认2个按钮
      } else {
        paramCount.value = 2; // 一般通知类默认2对额外参数（第一对固定显示）
        buttonCount.value = 2; // 一般通知类默认2个按钮
      }
      
      // 如果有注入的设置，同步更新
      if (notificationSettings) {
        notificationSettings.maxVisibleParams = paramCount.value;
        notificationSettings.maxVisibleButtons = buttonCount.value;
      }
      
      console.log('NotificationSettings - 模板变化监听器，设置默认值:', {
        paramCount: paramCount.value,
        buttonCount: buttonCount.value
      });
    } else {
      console.log('NotificationSettings - 模板变化监听器，保持用户设置不变');
    }
  }
}, { immediate: true });

// 处理参数对数量变化
const handleParamCountChange = (value) => {
  console.log('NotificationSettings - 参数对数量变化:', value);
  
  paramCount.value = value;
  
  // 发出变化事件
  emit('paramCountChange', value);
};

// 处理按钮数量变化
const handleButtonCountChange = (value) => {
  console.log('NotificationSettings - 按钮数量变化:', value);
  
  buttonCount.value = value;
  
  // 发出变化事件
  emit('buttonCountChange', value);
};

// 组件挂载时初始化
onMounted(() => {
  console.log('NotificationSettings - 组件挂载，通知设置:', notificationSettings);
  console.log('NotificationSettings - 当前注入的maxVisibleParams:', notificationSettings?.maxVisibleParams);
  console.log('NotificationSettings - 当前注入的maxVisibleButtons:', notificationSettings?.maxVisibleButtons);
  
  // 检查注入的设置是否已经有用户设置的值
  const hasUserSettings = notificationSettings && 
                         (notificationSettings.maxVisibleParams !== undefined || 
                          notificationSettings.maxVisibleButtons !== undefined);
  
  console.log('NotificationSettings - 是否有用户设置:', hasUserSettings);
  
  // 如果注入的设置中已经有值，说明用户已经设置过，直接使用这些值
  if (hasUserSettings) {
    if (notificationSettings.maxVisibleParams !== undefined) {
      paramCount.value = notificationSettings.maxVisibleParams;
      console.log('NotificationSettings - 使用用户设置的参数数量:', paramCount.value);
    }
    if (notificationSettings.maxVisibleButtons !== undefined) {
      buttonCount.value = notificationSettings.maxVisibleButtons;
      console.log('NotificationSettings - 使用用户设置的按钮数量:', buttonCount.value);
    }
  } else {
    // 只有在注入的设置完全不存在时才使用默认值
    console.log('NotificationSettings - 没有用户设置，使用默认值');
    paramCount.value = 2;
    buttonCount.value = 2;
    
    if (notificationSettings) {
      notificationSettings.maxVisibleParams = 2;
      notificationSettings.maxVisibleButtons = 2;
    }
    console.log('NotificationSettings - 使用默认值初始化参数数量为:', paramCount.value);
    console.log('NotificationSettings - 使用默认值初始化按钮数量为:', buttonCount.value);
  }
});

// 暴露方法给父组件
defineExpose({
  paramCount,
  buttonCount,
  handleParamCountChange,
  handleButtonCountChange
});
</script>

<style scoped lang="scss">
.notification-settings {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

.settings-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.close-btn {
  padding: 4px;
  color: #909399;
}

.close-btn:hover {
  color: #409eff;
}

.settings-content {
  padding: 20px;
}

.setting-group {
  margin-bottom: 24px;
}

.setting-group:last-child {
  margin-bottom: 0;
}

.setting-group h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.param-count-description {
  margin-bottom: 8px;
}

.description-text {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.param-count-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.second-row {
  margin-left: 0;
}

.button-count-options {
  display: flex;
  gap: 16px;
}

:deep(.el-radio) {
  margin-right: 20px;
  margin-bottom: 0;
}

:deep(.el-radio__label) {
  font-size: 14px;
  color: #606266;
}

:deep(.el-radio.is-checked .el-radio__label) {
  color: #409eff;
}
</style> 