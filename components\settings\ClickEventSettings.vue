<template>
  <div class="setting-group">
    <div class="group-title click-event-settings-button" @click="isExpanded = !isExpanded">
      <div>
        <i class="required-icon">*</i>点击事件配置
      </div>
      <el-icon class="expand-icon" :class="{ 'is-expanded': isExpanded }">
        <ArrowDown />
      </el-icon>
    </div>
    <div class="setting-content" v-show="isExpanded">
      <div class="setting-item">
        <div class="setting-label">事件类型</div>
        <div class="select-with-tooltip">
          <el-select 
            v-model="currentActionType" 
            :placeholder="placeholder"
            :popper-class="'action-type-select'"
            class="action-type-selector"
            >
            <el-option 
              v-for="action in displayedActions" 
              :key="action.value"
              :label="action.label"
              :value="action.value"
            />
          </el-select>
          <el-tooltip
            :content="getActionTooltip"
            :show-after="100"
            :hide-after="300"
            max-width="300"
            effect="light"
            placement="bottom-end"
            popper-class="custom-tooltip"
          >
            <el-icon class="help-icon"><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
      </div>

      <!-- 打开浏览器 -->
      <template v-if="currentActionType === CLICK_EVENT_TYPES.OPEN_BROWSER">
        <div class="setting-item-link">
          <div class="setting-label"><i class="required-icon">*</i>链接</div>
          <ClickEventEditor
            v-model="actionUrl"
            input-type="contenteditable"
            :event-type="currentActionType"
            :placeholder="currentPlaceholder"
            component-type="click-event"
            :panel-id="getPanelId()"
            @param-inserted="handleContentChange"
            @manage-param="$emit('manage-param')"
            ref="clickEventEditor"
          />
        </div>
      </template>
      
      <!-- 打开链接 -->
      <template v-if="currentActionType === CLICK_EVENT_TYPES.OPEN_URL">
        <div class="setting-item-link">
          <p class="tips">跳转小程序请使用'打开链接'</p>
          <div class="setting-label"><i class="required-icon">*</i>链接</div>
          <ClickEventEditor
            v-model="actionUrl"
            input-type="contenteditable"
            :event-type="currentActionType"
            :placeholder="currentPlaceholder"
            component-type="click-event-url"
            :panel-id="getPanelId()"
            @param-inserted="handleContentChange"
            @manage-param="$emit('manage-param')"
            ref="clickEventEditor"
          />
        </div>
      </template>
      
      <!-- 打开APP -->
      <template v-if="currentActionType === CLICK_EVENT_TYPES.OPEN_APP">
        <div class="setting-item-link">
          <div class="setting-label"><i class="required-icon">*</i>链接</div>
          <ClickEventEditor
            v-model="actionUrl"
            input-type="contenteditable"
            :event-type="currentActionType"
            :placeholder="currentPlaceholder"
            component-type="click-event-app"
            :panel-id="getPanelId()"
            @param-inserted="handleContentChange"
            @manage-param="$emit('manage-param')"
            ref="clickEventEditor"
          />
        </div>

        <div class="setting-item-link">
          <div class="setting-label"><i class="required-icon">*</i>APK包名</div>
          <el-input 
            v-model="packageNameModel"
            placeholder="请输入，字符长度<=50"
            maxlength="50"
            show-word-limit
          />
        </div>

        <div class="setting-item">
          <div class="setting-label">兜底类型</div>
          <el-select 
            v-model="floorTypeModel" 
            placeholder="打开应用市场"
            class="setting-select"
            >
            <el-option label="打开应用市场" value="0" />
            <el-option label="直接打开H5" value="1" />
            <el-option label="打开浏览器" value="2" />
            <el-option label="打开快应用" value="3" />
          </el-select>
        </div>

        <!-- 华为应用市场APPID输入框 - 暂时注释掉 -->
        <!--
        <div class="setting-item-link">
          <div class="setting-label">华为应用市场APPID(选填):</div>
          <el-input 
            v-model="hwAppId"
            :placeholder="`请输入在华为应用市场appid`"
          />
          <div class="setting-desc">用于未安装app的用户去下载的情况</div>
        </div>
        -->

        <!-- 荣耀应用市场APPID输入框 - 暂时注释掉 -->
        <!--
        <div class="setting-item-link">
          <div class="setting-label">荣耀应用市场APPID(选填):</div>
          <el-input 
            v-model="ryAppId"
            :placeholder="`请输入在荣耀应用市场appid`"
          />
          <div class="setting-desc">用于未安装app的用户去下载的情况</div>
        </div>
        -->
      </template>

      <!-- 打开快应用 -->
      <template v-if="currentActionType === CLICK_EVENT_TYPES.OPEN_QUICK">
        <div class="setting-item-link">
          <div class="setting-label"><i class="required-icon">*</i>链接</div>
          <ClickEventEditor
            v-model="actionUrl"
            input-type="contenteditable"
            :event-type="currentActionType"
            :placeholder="currentPlaceholder"
            component-type="click-event-quick"
            :panel-id="getPanelId()"
            @param-inserted="handleContentChange"
            @manage-param="$emit('manage-param')"
            ref="clickEventEditor"
          />
        </div>
      </template>

      <!-- 拨打电话 -->
      <template v-if="currentActionType === CLICK_EVENT_TYPES.DIAL_PHONE">
        <div class="setting-item-link">
          <div class="setting-label"><i class="required-icon">*</i>请输入需要拨打的号码</div>
          <ClickEventEditor
            v-model="actionUrl"
            input-type="contenteditable"
            :event-type="currentActionType"
            :placeholder="currentPlaceholder"
            component-type="click-event-phone"
            :panel-id="getPanelId()"
            @param-inserted="handleContentChange"
            @manage-param="$emit('manage-param')"
            ref="clickEventEditor"
          />
          <div class="setting-desc">电话号码设置为参数时，参数示例要按照正确的格式填写</div>
        </div>
      </template>

      <!-- 复制内容 -->
      <template v-if="currentActionType === CLICK_EVENT_TYPES.COPY_PARAMETER">
        <div class="setting-item">
          <div class="setting-label">复制类型</div>
          <el-radio-group v-model="copyType" class="copy-type-radio" @change="handleCopyTypeChange">
            <el-radio :value="'1'">复制参数</el-radio>
            <el-radio :value="'2'">固定内容</el-radio>
          </el-radio-group>
        </div>
        
        <div class="setting-item-link" v-if="copyType === '1'">
          <div class="setting-label">请选择需要复制内容:</div>
          <div class="param-select-empty" v-if="availableParams.length === 0">
            无可选复制参数
          </div>
          <el-radio-group v-else v-model="selectedParamId" class="param-radio-list">
            <el-radio
              v-for="param in availableParams"
              :key="param.id"
              :value="param.id"
              @change="handleParamChange"
            >
              {{ param.name }}
            </el-radio>
          </el-radio-group>
        </div>
        
        <div class="setting-item-link" v-else>
          <div class="setting-label">请输入要复制的内容</div>
          <el-input
            v-model="fixedContent"
            placeholder="最长长度30个字符 (中文占两个字符，英文占一个)"
            maxlength="30"
            show-word-limit
            @input="handleFixedContentInput"
          />
        </div>
      </template>
      
      <!-- 发送短信 -->
      <template v-if="currentActionType === CLICK_EVENT_TYPES.OPEN_SMS">
        <div class="setting-item-link">
          <div class="setting-label"><i class="required-icon">*</i>请输入需要发送短信的号码:</div>
          <ClickEventEditor
            v-model="actionUrl"
            input-type="contenteditable"
            :event-type="currentActionType"
            component-type="click-event-sms"
            :panel-id="getPanelId()"
            @param-inserted="handleContentChange"
            @manage-param="$emit('manage-param')"
            ref="clickEventEditor"
          />
          <div class="setting-desc">将调用系统发送短信功能打开以填写的号码，格式如: 131****4321, 1069****1234 <br>电话号码设置为参数时，参数示例要按照正确的格式填写</div>
        </div>
        
        <div class="setting-item-link">
          <div class="setting-label"><i class="required-icon">*</i>请输入需要发送的短信正文:</div>
          <ClickEventEditor
            v-model="actionPath"
            input-type="contenteditable"
            :event-type="currentActionType"
            component-type="click-event-sms"
            :panel-id="getPanelId()"
            @param-inserted="handleSmsContentChange"
            @manage-param="$emit('manage-param')"
            ref="clickEventEditor"
          />
          <div class="setting-desc">将自动在短信框输入需要发送的短信正文，例如: 2000积分兑换流量</div>
        </div>
      </template>
      
      <!-- 跳转邮箱 -->
      <template v-if="currentActionType === CLICK_EVENT_TYPES.OPEN_EMAIL">
        <div class="setting-item-link">
          <div class="setting-label"><i class="required-icon">*</i>请输入需要发送的邮箱地址:</div>
          <el-input
            v-model="emailAddressModel"
            placeholder="请输入邮箱地址"
            maxlength="100"
            show-word-limit
          />
        </div>
        
        <div class="setting-item-link">
          <div class="setting-label"><i class="required-icon">*</i>请输入邮件标题:</div>
          <el-input
            v-model="emailSubjectModel"
            placeholder="最长长度100个字符 (中文占两个字符，英文占一个)"
            maxlength="100"
            show-word-limit
          />
        </div>
        
        <div class="setting-item-link">
          <div class="setting-label"><i class="required-icon">*</i>请输入邮件正文:</div>
          <el-input
            v-model="emailBodyModel"
            type="textarea"
            :rows="3"
            placeholder="最长长度500个字符 (中文占两个字符，英文占一个)"
            maxlength="500"
            show-word-limit
          />
        </div>
      </template>
      
      <!-- 跳转日程 -->
      <template v-if="currentActionType === CLICK_EVENT_TYPES.OPEN_SCHEDULE">
        <div class="setting-item-link">
          <div class="setting-label"><i class="required-icon">*</i>请输入日程标题:</div>
          <el-input
            v-model="scheduleTitleModel"
            type="textarea"
            :rows="3"
            placeholder="最长长度100个字符 (中文占两个字符，英文占一个)"
            maxlength="100"
            show-word-limit
          />
        </div>

        <div class="setting-item-link">
          <div class="setting-label"><i class="required-icon">*</i>请输入日程描述内容:</div>
          <el-input
            v-model="scheduleContentModel"
            type="textarea"
            :rows="3"
            placeholder="最长长度100个字符 (中文占两个字符，英文占一个)"
            maxlength="100"
            show-word-limit
          />
        </div>

        <div class="setting-item">
          <div class="setting-label setting-date-label"><i class="required-icon">*</i>开始时间:</div>
          <el-date-picker
            v-model="scheduleStartTimeStringModel"
            type="datetime"
            placeholder="选择日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </div>

        <div class="setting-item">
          <div class="setting-label setting-date-label"><i class="required-icon">*</i>结束时间:</div>
          <el-date-picker
            v-model="scheduleEndTimeStringModel"
            type="datetime"
            placeholder="选择日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </div>
      </template>
      
      <!-- 打开弹窗 -->
      <template v-if="currentActionType === CLICK_EVENT_TYPES.OPEN_POPUP">
        <div class="setting-item-link">
          <div class="setting-label"><i class="required-icon">*</i>弹窗标题:</div>
          <el-input
            v-model="popupTitleModel"
            placeholder="最长长度30个字符 (中文占两个字符，英文占一个)"
            maxlength="30"
            show-word-limit
          />
        </div>
        
        <div class="setting-item-link">
          <div class="setting-label"><i class="required-icon">*</i>请输入弹窗内容:</div>
          <el-input
            v-model="popupContentModel"
            type="textarea"
            :rows="3"
            placeholder="最长长度100个字符 (中文占两个字符，英文占一个)"
            maxlength="100"
            show-word-limit
          />
        </div>
        
        <div class="setting-item-link">
          <div class="setting-label"><i class="required-icon">*</i>请输入弹窗按钮名称:</div>
          <el-input
            v-model="popupButtonTextModel"
            placeholder="最长长度12个字符 (中文占两个字符，英文占一个)"
            maxlength="12"
            show-word-limit
          />
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { addTemplateResetListener, addTemplateSwitchListener } from '@/utils/templateEvents';
import { useParamStore } from '@/stores/paramStore';
import ClickEventEditor from './ClickEventEditor.vue';

// 导入统一的点击事件管理工具
import {
  CLICK_EVENT_TYPES,
  CLICK_EVENT_CONFIG,
  ClickEventTypeConverter,
  ActionJsonGenerator,
  globalClickEventCache
} from '@/utils/clickEventManager';

// 避免编辑器挂起
let editorInstances = [];
let removeResetListener = null;
let removeSwitchListener = null;
const currentTemplateCardId = ref('');

// 初始化当前模板cardId
const initializeTemplateCardId = () => {
  const currentTemplate = window.TEMPLATE_DIALOG_DATA?.selectedTemplate;
  if (currentTemplate?.cardId) {
    currentTemplateCardId.value = currentTemplate.cardId;
    console.log('初始化当前模板cardId:', currentTemplateCardId.value);
    // 清除上一个模板的缓存（关键修复）
    const prevCardId = sessionStorage.getItem('prevTemplateCardId');
    if (prevCardId) {
      cacheUtils.clearTemplateCache(prevCardId);
    }
    // 记录当前cardId供下次切换时清除
    sessionStorage.setItem('prevTemplateCardId', currentTemplateCardId.value);
  }
};

// 统一的缓存管理工具函数
const cacheUtils = {
  // 获取缓存键
   getCacheKey(contentType, contentId) {
    const currentTemplateCardId = window.TEMPLATE_DIALOG_DATA?.selectedTemplate?.cardId || 'default';
    const contentCategory = window.TEMPLATE_DIALOG_DATA?.content?.type || 'default';
    return `${currentTemplateCardId}_${contentCategory}_${contentType}_${contentId}`; // 新格式：cardId_内容类型_配置类型_内容ID
  },


  // 确保缓存对象存在
  ensureCacheExists() {
    if (!window.CLICK_EVENT_CACHE) {
      window.CLICK_EVENT_CACHE = {};
    }
  },
  
  // 获取或创建缓存项
  getOrCreateCacheItem(contentType, contentId, defaultValues = {}) {
    this.ensureCacheExists();
    const cacheKey = this.getCacheKey(contentType, contentId);
    
    if (!window.CLICK_EVENT_CACHE[cacheKey]) {
      window.CLICK_EVENT_CACHE[cacheKey] = {
        actionType: defaultValues.actionType || defaultActionType,
        actionUrl: defaultValues.actionUrl || '',
        actionPath: defaultValues.actionPath || '',
        ...defaultValues
      };
    }
    
    return { cacheKey, cacheItem: window.CLICK_EVENT_CACHE[cacheKey] };
  },
  
  // 更新缓存项
  updateCacheItem(contentType, contentId, updates) {
    const { cacheKey, cacheItem } = this.getOrCreateCacheItem(contentType, contentId);
    Object.assign(cacheItem, updates, { _lastUpdated: Date.now() });
    return cacheKey;
  },
  
  // 从横滑缓存获取URL
  getUrlFromHorizontalSwipeCache(contentId, selectedCardIndex = 0) {
    if (!contentId || typeof contentId !== 'string' || !window.HORIZONTAL_SWIPE_CACHE) {
      return null;
    }
    
    const isHorizontalSwipeButton = contentId.includes('button') || 
                                   contentId.includes('card') || 
                                   contentId.includes('swipe');
    
    if (!isHorizontalSwipeButton) {
      return null;
    }
    
    try {
      const cardCacheKey = `card_${selectedCardIndex}`;
      const cardData = window.HORIZONTAL_SWIPE_CACHE[cardCacheKey];
      
      if (cardData?.clickEvent) {
        const actionJson = ActionJsonGenerator.fromClickEvent(cardData.clickEvent);
        const url = actionJson.target || '';
        
        if (url) {
          console.log(`从横滑缓存获取到actionUrl: ${url}`);
          return url;
        }
      }
    } catch (error) {
      console.warn('从横滑缓存获取URL时出错:', error);
    }
    
    return null;
  },
  
  // 清除指定模板的缓存
  clearTemplateCache(cardId) {
    this.ensureCacheExists();
    
    if (!cardId) {
      console.warn('clearTemplateCache: cardId 为空，跳过清除');
      return;
    }
    
    // 查找并删除所有以指定cardId开头的缓存项
    const keysToDelete = Object.keys(window.CLICK_EVENT_CACHE).filter(key => 
      key.startsWith(`${cardId}_`)
    );
    
    keysToDelete.forEach(key => {
      delete window.CLICK_EVENT_CACHE[key];
    });
    
    console.log(`已清除模板 ${cardId} 的缓存，共删除 ${keysToDelete.length} 个缓存项`);
  }
};

// 统一的字段数据收集函数
const collectFieldData = () => {
  // 直接返回包含所有字段的对象，让 ActionJsonGenerator.fromClickEventSettings 来处理字段选择
  return {
    actionType: currentActionType.value,
    actionUrl: actionUrl.value,
    actionPath: actionPath.value,
    // 包含所有可能的字段
    packageName: fieldUtils.getPackageNameArray(),
    floorType: floorType.value,
    emailAddress: emailAddress.value,
    emailSubject: emailSubject.value,
    emailBody: emailBody.value,
    scheduleTitle: scheduleTitle.value,
    scheduleContent: scheduleContent.value,
    scheduleStartTimeString: scheduleStartTimeString.value,
    scheduleEndTimeString: scheduleEndTimeString.value,
    popupTitle: popupTitle.value,
    popupContent: popupContent.value,
    popupButtonText: popupButtonText.value,
    copyType: copyType.value,
    selectedParamId: selectedParamId.value,
    fixedContent: fixedContent.value
  };
};

// 立即保存字段数据到缓存的函数
const saveFieldDataToCache = () => {
  if (!props.content || !props.content.type) return;

  const contentType = props.content.type;
  const contentId = props.content?.contentId || '';
  const fieldData = collectFieldData();

  // 更新缓存
  cacheUtils.updateCacheItem(contentType, contentId, fieldData);

  console.log(`字段数据已保存到缓存 (${contentType}_${contentId}):`, fieldData);
};

// 清空所有字段数据
const clearAllFieldData = () => {
  // 清空所有响应式字段
  packageName.value = '';
  floorType.value = '0';
  emailAddress.value = '';
  emailSubject.value = '';
  emailBody.value = '';
  scheduleStartTimeString.value = '';
  scheduleEndTimeString.value = '';
  popupTitle.value = '';
  popupContent.value = '';
  popupButtonText.value = '';
  copyType.value = '1';
  selectedParamId.value = '';
  fixedContent.value = '';

  // 清空actionUrl和actionPath
  actionUrl.value = '';
  actionPath.value = '';

  // 注意：不能直接赋值给computed属性currentActionType
  // currentActionType是computed，它的值由props.content决定
  // 如果需要重置事件类型，应该通过emit('update:content', ...)来更新
  
  console.log('所有字段数据已清空');
};

// 统一的字段管理工具函数
const fieldUtils = {
  // 清空所有字段
  clearAllFields() {
    clearAllFieldData();
  },
  
  // 根据事件类型清空相关字段
  clearFieldsByActionType(actionType) {
    const config = CLICK_EVENT_CONFIG[actionType];
    if (!config || !config.additionalFields) return;
    
    // 更智能的字段清空：只清空与新类型不兼容的字段
    // 而不是清空所有字段，避免丢失用户输入
    config.additionalFields.forEach(field => {
      // 检查当前内容中是否有该字段的值
      const hasContentValue = props.content && props.content[field];
      
      // 如果内容中有值，保留该值；如果没有值，才清空
      if (!hasContentValue) {
        switch (field) {
          case 'packageName':
            packageName.value = '';
            break;
          case 'floorType':
            floorType.value = '0';
            break;
          case 'emailAddress':
            emailAddress.value = '';
            break;
          case 'emailSubject':
            emailSubject.value = '';
            break;
          case 'emailBody':
            emailBody.value = '';
            break;
          case 'scheduleStartTimeString':
            scheduleStartTimeString.value = '';
            break;
          case 'scheduleEndTimeString':
            scheduleEndTimeString.value = '';
            break;
          case 'popupTitle':
            popupTitle.value = '';
            break;
          case 'popupContent':
            popupContent.value = '';
            break;
          case 'popupButtonText':
            popupButtonText.value = '';
            break;
          case 'copyType':
            // copyType.value = '1'; // 保持默认值
            break;
          case 'selectedParamId':
            selectedParamId.value = '';
            break;
          case 'fixedContent':
            fixedContent.value = '';
            break;
        }
      } else {
        console.log(`保留字段 ${field} 的值，因为内容中有数据:`, props.content[field]);
      }
    });
  },
  
  // 获取packageName的数组格式（用于OPEN_APP类型）
  getPackageNameArray() {
    if (currentActionType.value !== CLICK_EVENT_TYPES.OPEN_APP || !packageName.value) {
      return [];
    }

    // 如果packageName.value已经是数组，直接返回；否则包装成数组
    return Array.isArray(packageName.value) ? packageName.value : [packageName.value];
  }
};

const props = defineProps({
  content: {
    type: Object,
    default: () => ({})
  },
  placeholder: {
    type: String,
    default: '打开浏览器'
  }
});

const emit = defineEmits(['update:content', 'insert-param', 'manage-param', 'field-change']);

// 组件状态
const isExpanded = ref(true);
const showAllActions = ref(false);
const clickEventEditor = ref(null);

// 使用统一的事件类型配置替换原有的ACTION_MAP
const ACTION_MAP = CLICK_EVENT_CONFIG;

// 事件类型
const selectedActionType = ref(props.content?.actionType || CLICK_EVENT_TYPES.OPEN_BROWSER);

// 使用统一配置获取事件类型列表
const actionTypes = ref(Object.keys(CLICK_EVENT_TYPES));

// 显示的事件类型 - 使用统一的标签获取方法
const displayedActions = computed(() => {
  return actionTypes.value.map(code => ({
    value: code,
    label: ClickEventTypeConverter.getLabel(code)
  }));
});

// 当前事件类型的提示信息 - 使用统一的提示获取方法
const getActionTooltip = computed(() => {
  const type = formatActionType(currentActionType.value);
  return ClickEventTypeConverter.getTooltip(type);
});

// 当前事件类型的占位符 - 使用统一的占位符获取方法
const currentPlaceholder = computed(() => {
  const type = formatActionType(currentActionType.value);
  return ClickEventTypeConverter.getPlaceholder(type);
});

// 设置默认值
const defaultActionType = CLICK_EVENT_TYPES.OPEN_BROWSER;

// 获取参数存储
// const paramStore = useParamStore();

// 修改事件类型解析方法，使用统一的类型转换器
const formatActionType = (type) => {
  return ClickEventTypeConverter.toActionType(type);
};

// 检查是否是横滑模板 - 移到前面，避免初始化顺序问题
const isHorizontalSwipeTemplate = computed(() => {
  const result = props.content && !props.content.contentId && (props.content.type || props.content.actionType);
  console.log('ClickEventSettings - isHorizontalSwipeTemplate 判断:', {
    content: props.content,
    contentId: props.content?.contentId,
    type: props.content?.type,
    actionType: props.content?.actionType,
    result: result
  });
  return result;
});

// 计算属性：获取当前点击事件类型
const currentActionType = computed({
  get() {
    if (isHorizontalSwipeTemplate.value) {
      // 横滑模板：优先使用内容中的actionType，但增加更强的推断逻辑
      const contentActionType = props.content?.actionType;
      
      // 如果内容中明确有actionType，直接使用
      if (contentActionType) {
        return contentActionType;
      }
      
      // 如果没有actionType，但有特定字段，通过字段推断类型
      // 这可以防止在数据同步过程中过早回退到默认值
      const hasSpecificFields = props.content && (
        props.content.packageName || 
        props.content.emailAddress || 
        props.content.popupTitle || 
        props.content.scheduleStartTimeString || 
        props.content.copyType ||
        props.content.url ||  // 增加 url 字段检查
        props.content.phone ||  // 增加 phone 字段检查
        props.content.text ||  // 增加 text 字段检查
        props.content.app ||  // 增加 app 字段检查
        props.content.quick ||  // 增加 quick 字段检查
        props.content.email ||  // 增加 email 字段检查
        props.content.schedule ||  // 增加 schedule 字段检查
        props.content.popup ||  // 增加 popup 字段检查
        props.content.type  // 增加 type 字段检查
      );
      
      if (hasSpecificFields) {
        // 按优先级推断类型
        if (props.content.packageName) return CLICK_EVENT_TYPES.OPEN_APP;
        if (props.content.emailAddress || props.content.email) return CLICK_EVENT_TYPES.OPEN_EMAIL;
        if (props.content.popupTitle || props.content.popup) return CLICK_EVENT_TYPES.OPEN_POPUP;
        if (props.content.scheduleStartTimeString || props.content.schedule) return CLICK_EVENT_TYPES.OPEN_SCHEDULE;
        if (props.content.copyType) return CLICK_EVENT_TYPES.COPY_PARAMETER;
        if (props.content.phone) return CLICK_EVENT_TYPES.DIAL_PHONE;
        if (props.content.quick) return CLICK_EVENT_TYPES.OPEN_QUICK;
        if (props.content.app) return CLICK_EVENT_TYPES.OPEN_APP;
        if (props.content.url) return CLICK_EVENT_TYPES.OPEN_BROWSER;
        if (props.content.type) return ClickEventTypeConverter.toActionType(props.content.type);
      }
      
      // 最后才回退到默认值
      return CLICK_EVENT_TYPES.OPEN_BROWSER;
    } else {
      // 其他模板：只用actionType，没有就用OPEN_BROWSER
      return props.content?.actionType || CLICK_EVENT_TYPES.OPEN_BROWSER;
    }
  },
  set(newType) {
    // 横滑模板
    if (isHorizontalSwipeTemplate.value) {
      // 构建 ClickEventSettings 格式的数据，包含所有必要字段
      const settingsData = {
        actionType: newType,
        // 包含所有可能的字段，使用现有值或默认值
        packageName: props.content?.packageName || '',
        floorType: props.content?.floorType || '0',
        emailAddress: props.content?.emailAddress || '',
        emailSubject: props.content?.emailSubject || '',
        emailBody: props.content?.emailBody || '',
        scheduleStartTimeString: props.content?.scheduleStartTimeString || '',
        scheduleEndTimeString: props.content?.scheduleEndTimeString || '',
        popupTitle: props.content?.popupTitle || '',
        popupContent: props.content?.popupContent || '',
        popupButtonText: props.content?.popupButtonText || '',
        copyType: props.content?.copyType || '1',
        selectedParamId: props.content?.selectedParamId || '',
        fixedContent: props.content?.fixedContent || '',
        actionUrl: props.content?.actionUrl || '',
        actionPath: props.content?.actionPath || ''
      };

      // 使用统一的转换方法生成 clickEvent
      const clickEventData = ActionJsonGenerator.fromClickEventSettings(settingsData);

      const updatedClickEvent = {
        ...props.content,
        ...settingsData,
        clickEvent: clickEventData
      };

      // 设置标记防止watch误触发
      window._isSettingActionType = true;
      
      emit('update:content', updatedClickEvent);
      
      // 清除标记
      setTimeout(() => {
        window._isSettingActionType = false;
      }, 50);
      
      console.log(`横滑模板设置事件类型为: ${newType}`, updatedClickEvent);
    } else if (props.content?.type === 'ecommerce-settings') {
      // 电商模板：使用新格式
      const settingsData = {
        actionType: newType,
        actionUrl: props.content?.actionUrl || '',
        actionPath: props.content?.actionPath || '',
        packageName: props.content?.packageName || '',
        floorType: props.content?.floorType || '0',
        emailAddress: props.content?.emailAddress || '',
        emailSubject: props.content?.emailSubject || '',
        emailBody: props.content?.emailBody || '',
        scheduleStartTimeString: props.content?.scheduleStartTimeString || '',
        scheduleEndTimeString: props.content?.scheduleEndTimeString || '',
        popupTitle: props.content?.popupTitle || '',
        popupContent: props.content?.popupContent || '',
        popupButtonText: props.content?.popupButtonText || '',
        copyType: props.content?.copyType || '1',
        selectedParamId: props.content?.selectedParamId || '',
        fixedContent: props.content?.fixedContent || ''
      };

      // 直接使用新格式，不转换为老格式
      const updatedContent = {
        ...props.content,
        ...settingsData
      };
      
      // 设置标记防止watch误触发
      window._isSettingActionType = true;
      
      emit('update:content', updatedContent);
      
      // 清除标记
      setTimeout(() => {
        window._isSettingActionType = false;
      }, 50);
      
      console.log(`电商模板设置事件类型为: ${newType}`, updatedContent);
    } else {
      // 其他模板：正常缓存逻辑
      const contentType = props.content?.type;
      const contentId = props.content?.contentId || '';
      
      if (!contentType) return;
      
      // 构建 ClickEventSettings 格式的数据，包含所有必要字段
      const settingsData = {
        actionType: newType,
        // 包含所有可能的字段，使用现有值或默认值
        packageName: props.content?.packageName || '',
        floorType: props.content?.floorType || '0',
        emailAddress: props.content?.emailAddress || '',
        emailSubject: props.content?.emailSubject || '',
        emailBody: props.content?.emailBody || '',
        scheduleStartTimeString: props.content?.scheduleStartTimeString || '',
        scheduleEndTimeString: props.content?.scheduleEndTimeString || '',
        popupTitle: props.content?.popupTitle || '',
        popupContent: props.content?.popupContent || '',
        popupButtonText: props.content?.popupButtonText || '',
        copyType: props.content?.copyType || '1',
        selectedParamId: props.content?.selectedParamId || '',
        fixedContent: props.content?.fixedContent || '',
        actionUrl: props.content?.actionUrl || '',
        actionPath: props.content?.actionPath || ''
      };

      // 使用统一的转换方法生成 clickEvent
      const clickEventData = ActionJsonGenerator.fromClickEventSettings(settingsData);
      
      const updatedContent = {
        ...props.content,
        ...settingsData,
        clickEvent: clickEventData
      };
      
      const cacheKey = cacheUtils.updateCacheItem(contentType, contentId, { actionType: newType });
      console.log(`设置事件类型(${cacheKey}): ${newType}`);
      
      // 设置标记防止watch误触发
      window._isSettingActionType = true;
      
      emit('update:content', updatedContent);
      
      // 清除标记
      setTimeout(() => {
        window._isSettingActionType = false;
      }, 50);
      
      console.log(`非横滑模板设置事件类型为: ${newType}`, updatedContent);
    }
  }
});

// 组件状态
const packageName = ref('');
const floorType = ref('0');

// 初始化packageName - 横滑模板和其他模板兼容
watch(() => props.content?.packageName, (newValue) => {
  if (isHorizontalSwipeTemplate.value) {
    // 横滑模板：直接使用 props.content.packageName
    packageName.value = newValue || '';
  } else {
    // 其他模板：原有逻辑
    if (newValue) {
      // 如果是数组，取第一个值
      packageName.value = Array.isArray(newValue) ? newValue[0] : newValue;
    } else {
      packageName.value = '';
    }
  }
}, { immediate: true });

// 横滑模板下，packageName 的 v-model 绑定
const packageNameModel = computed({
  get() {
    if (isHorizontalSwipeTemplate.value) {
      return props.content.packageName || '';
    }
    // 对于轮播图等非横滑模板，优先从props.content获取，然后从本地ref获取
    // 添加输入状态检测，避免在用户输入时被外部更新覆盖
    const activeElement = document.activeElement;
    const isInputting = activeElement && (
      activeElement.tagName === 'INPUT' || 
      activeElement.tagName === 'TEXTAREA' ||
      activeElement.contentEditable === 'true'
    );
    
    if (isInputting && packageName.value !== '') {
      // 如果用户正在输入且本地有值，优先使用本地值
      return packageName.value;
    }
    
    return props.content?.packageName || packageName.value || '';
  },
  set(val) {
    if (isHorizontalSwipeTemplate.value) {
      emit('update:content', { ...props.content, packageName: val });
    } else {
      // 对于轮播图等非横滑模板，只更新本地ref，不立即emit更新
      // emit更新会通过watch监听器触发，避免循环更新
      packageName.value = val;
    }
  }
});

// 横滑模板下，floorType 的 v-model 绑定
const floorTypeModel = computed({
  get() {
    if (isHorizontalSwipeTemplate.value) {
      return props.content.floorType || '0';
    }
    return floorType.value;
  },
  set(val) {
    if (isHorizontalSwipeTemplate.value) {
      emit('update:content', { ...props.content, floorType: val });
    } else {
      floorType.value = val;
    }
  }
});

// 复制相关状态
const copyType = ref('1');
const selectedParamId = ref('');
const availableParams = ref([]);
const fixedContent = ref('');

// 短信相关状态
// 使用actionUrl作为短信号码，actionPath作为短信内容

// 邮件相关状态
const emailAddress = ref('');
const emailSubject = ref('');
const emailBody = ref('');

// 日程相关状态
const scheduleTitle = ref('');
const scheduleContent = ref('');
const scheduleStartTimeString = ref('');
const scheduleEndTimeString = ref('');

// 弹窗相关状态
const popupTitle = ref('');
const popupContent = ref('');
const popupButtonText = ref('');

// 获取参数存储
const paramStore = useParamStore();

const actionUrl = computed({
  get: () => {
    if (isHorizontalSwipeTemplate.value) {
      // 横滑模板：直接读props.content.url，保证每个卡片独立
      return props.content.url || '';
    } else {
      const contentType = props.content?.type;
      const contentId = props.content?.contentId || '';
      if (!contentType) return '';
      const { cacheKey, cacheItem } = cacheUtils.getOrCreateCacheItem(contentType, contentId, {
        actionType: props.content?.actionType || defaultActionType,
        actionUrl: props.content?.actionUrl || '',
        actionPath: props.content?.actionPath || ''
      });
      if (props.content.actionUrl) {
        cacheItem.actionUrl = props.content.actionUrl;
        return props.content.actionUrl;
      }
      const swipeUrl = cacheUtils.getUrlFromHorizontalSwipeCache(
        contentId, 
        props.content?.selectedCardIndex || 0
      );
      if (swipeUrl) {
        cacheItem.actionUrl = swipeUrl;
        return swipeUrl;
      }
      return cacheItem.actionUrl || '';
    }
  },
  set: (val) => {
    if (isHorizontalSwipeTemplate.value) {
      // 横滑模板：只emit field-change，避免死循环
      emit('field-change', { field: 'url', value: val });
    } else {
      emit('field-change', { field: 'actionUrl', value: val });
    }
    // 触发手动更新，确保内容变化被正确处理
    handleManualUpdate();
  }
});

const actionPath = computed({
  get: () => {
    if (isHorizontalSwipeTemplate.value) {
      return props.content.path || '';
    } else {
      const contentType = props.content?.type;
      const contentId = props.content?.contentId || '';
      if (!contentType) return '';
      const { cacheItem } = cacheUtils.getOrCreateCacheItem(contentType, contentId, {
        actionType: props.content?.actionType || defaultActionType,
        actionUrl: props.content?.actionUrl || '',
        actionPath: props.content?.actionPath || ''
      });
      if (props.content.actionPath) {
        cacheItem.actionPath = props.content.actionPath;
        return props.content.actionPath;
      }
      return cacheItem.actionPath || '';
    }
  },
  set: (val) => {
    if (isHorizontalSwipeTemplate.value) {
      emit('field-change', { field: 'path', value: val });
    } else {
      emit('field-change', { field: 'actionPath', value: val });
    }
    // 触发手动更新，确保内容变化被正确处理
    handleManualUpdate();
  }
});

// 获取面板ID
const getPanelId = () => {
  // 为每个内容创建唯一面板ID
  const contentType = props.content?.type || 'unknown';
  const contentId = props.content?.contentId || '';
  return `click-event-panel-${contentType}-${contentId}`;
};

// 处理内容变化（用于actionUrl字段）
const handleContentChange = (value) => {
  try {
    // 更新actionUrl
    if (value !== undefined) {
      actionUrl.value = value;
    }

    // 使用统一的字段数据收集函数
    const additionalData = collectFieldData();

    // 生成actionJson
    const actionJson = ActionJsonGenerator.generate(
      currentActionType.value,
      value,
      actionPath.value,
      additionalData
    );

    emit('update:content', {
      ...props.content,
      actionUrl: value,
      actionJson
    });
  } catch (error) {
    console.error('处理内容变化时出错:', error);
    // 即使出错也要确保基本更新
    emit('update:content', {
      ...props.content,
      actionUrl: value
    });
  }
};

// 处理短信正文变化（用于actionPath字段）
const handleSmsContentChange = (value) => {
  try {
    // 更新actionPath
    if (value !== undefined) {
      actionPath.value = value;
    }

    // 使用统一的字段数据收集函数
    const additionalData = collectFieldData();

    // 生成actionJson
    const actionJson = ActionJsonGenerator.generate(
      currentActionType.value,
      actionUrl.value,
      value,
      additionalData
    );

    emit('update:content', {
      ...props.content,
      actionPath: value,
      actionJson
    });
  } catch (error) {
    console.error('处理短信正文变化时出错:', error);
    // 即使出错也要确保基本更新
    emit('update:content', {
      ...props.content,
      actionPath: value
    });
  }
};



// 更新全局点击事件缓存 - 使用统一的缓存管理器
const updateGlobalCache = () => {
  try {
    // 安全检查
    if (!props.content || !props.content.type) {
      console.warn('无法更新缓存：内容对象或类型为空');
      return;
    }
    
    const contentType = props.content.type;
    const contentId = props.content?.contentId || '';
    
    // 使用统一的字段数据收集函数构建缓存数据
    const cacheData = {
      actionType: currentActionType.value || defaultActionType,
      actionUrl: actionUrl.value || '',
      actionPath: actionPath.value || '',
      ...collectFieldData()
    };
    
    // 使用统一的缓存管理器
    globalClickEventCache.setCache(contentType, contentId, cacheData);
    
    // 同时更新旧的全局缓存以保持兼容性
    cacheUtils.ensureCacheExists();
    const cacheKey = cacheUtils.getCacheKey(contentType, contentId);
    window.CLICK_EVENT_CACHE[cacheKey] = {
      ...cacheData,
      _lastUpdated: Date.now()
    };
    
    console.log(`缓存更新成功: ${cacheKey}`, cacheData);
  } catch (error) {
    console.error('更新全局缓存时出错:', error);
  }
};

// 统一的内容更新方法
const emitContentUpdate = (type = copyType.value, paramId = selectedParamId.value, content = fixedContent.value) => {
  try {
    // 构建 ClickEventSettings 格式的数据
    const settingsData = {
      actionType: CLICK_EVENT_TYPES.COPY_PARAMETER,
      copyType: type,
      selectedParamId: paramId,
      fixedContent: content,
      // 包含其他必要字段
      packageName: fieldUtils.getPackageNameArray(),
      floorType: floorType.value,
      emailAddress: emailAddress.value,
      emailSubject: emailSubject.value,
      emailBody: emailBody.value,
      scheduleStartTimeString: scheduleStartTimeString.value,
      scheduleEndTimeString: scheduleEndTimeString.value,
      popupTitle: popupTitle.value,
      popupContent: popupContent.value,
      popupButtonText: popupButtonText.value
    };

    // 使用统一的转换方法生成 clickEvent
    const clickEventData = ActionJsonGenerator.fromClickEventSettings(settingsData);

    emit('update:content', { 
      ...props.content, 
      actionType: CLICK_EVENT_TYPES.COPY_PARAMETER,
      copyType: type,
      fixedContent: content,
      selectedParamId: paramId,
      packageName: settingsData.packageName,
      clickEvent: clickEventData
    });
  } catch (error) {
    console.error('发送内容更新时出错:', error);
  }
};

// 处理复制类型切换
const handleCopyTypeChange = (type) => {
  console.log('切换复制类型:', type);
  
  // 防止重复处理
  if (window._isHandlingCopyTypeChange) {
    console.warn('复制类型切换处理中，忽略重复请求');
    return;
  }
  
  window._isHandlingCopyTypeChange = true;
  
  try {
    // 如果类型相同，不处理
    if (type === copyType.value) {
      console.log('复制类型未变化，跳过处理');
      return;
    }
    
    // 更新复制类型
    copyType.value = type;
    
    // 根据类型清空相应的值
    if (type === '1') { // 复制参数
      fixedContent.value = '';
      // 如果有可用参数，选择第一个
      if (availableParams.value.length > 0) {
        selectedParamId.value = availableParams.value[0].id;
      }
    } else if (type === '2') { // 固定内容
      selectedParamId.value = '';
    }
    
    // 构建 ClickEventSettings 格式的数据
    const settingsData = {
      actionType: CLICK_EVENT_TYPES.COPY_PARAMETER,
      copyType: type,
      selectedParamId: selectedParamId.value,
      fixedContent: fixedContent.value,
      // 包含其他必要字段
      packageName: fieldUtils.getPackageNameArray(),
      floorType: floorType.value,
      emailAddress: emailAddress.value,
      emailSubject: emailSubject.value,
      emailBody: emailBody.value,
      scheduleStartTimeString: scheduleStartTimeString.value,
      scheduleEndTimeString: scheduleEndTimeString.value,
      popupTitle: popupTitle.value,
      popupContent: popupContent.value,
      popupButtonText: popupButtonText.value
    };
    
    // 使用统一的转换方法生成 clickEvent
    const clickEventData = ActionJsonGenerator.fromClickEventSettings(settingsData);
    
    // 直接更新内容，不触发其他监听器
    window._ignoreContentUpdate = true;
    emit('update:content', {
      ...props.content,
      actionType: CLICK_EVENT_TYPES.COPY_PARAMETER,
      copyType: type,
      fixedContent: fixedContent.value,
      selectedParamId: selectedParamId.value,
      clickEvent: clickEventData
    });
    
    console.log('复制类型切换完成:', { type, clickEventData });
  } catch (error) {
    console.error('处理复制类型切换时出错:', error);
  } finally {
    // 延迟清除标记
    setTimeout(() => {
      window._isHandlingCopyTypeChange = false;
      window._ignoreContentUpdate = false;
    }, 100);
  }
};

// 处理固定内容输入
const handleFixedContentInput = (value) => {
  console.log('固定内容输入:', value);
  
  // 防止重复处理
  if (window._isHandlingFixedContentChange) {
    console.warn('固定内容处理中，忽略重复请求');
    return;
  }
  
  window._isHandlingFixedContentChange = true;
  try {
    fixedContent.value = value;
    
    // 如果是固定内容模式，更新URL
    if (copyType.value === '2') {
      actionUrl.value = value;
    }
    
    emitContentUpdate(copyType.value, selectedParamId.value, value);
    console.log('固定内容输入处理完成:', { value, copyType: copyType.value });
  } catch (error) {
    console.error('处理固定内容输入时出错:', error);
  } finally {
    setTimeout(() => {
      window._isHandlingFixedContentChange = false;
    }, 100);
  }
};

// 处理参数选择
const handleParamChange = (paramId) => {
  console.log('参数选择变化:', paramId);
  
  if (!paramId) {
    console.warn('参数ID为空，跳过处理');
    return;
  }
  
  try {
    // 更新选中的参数ID
    selectedParamId.value = paramId;
    
    // 确保在复制参数模式
    if (copyType.value !== '1') {
      handleCopyTypeChange('1');
    } else {
      // 更新内容
      emitContentUpdate('1', paramId, '');
    }
    
    console.log('参数选择处理完成:', { paramId, copyType: copyType.value });
  } catch (error) {
    console.error('处理参数选择时出错:', error);
  }
};

// 初始化字段函数
const initializeFields = () => {
  try {
    const contentType = props.content?.type || '';

    // 对于电商模板，新增更多保护条件
    if (contentType === 'ecommerce-settings') {
      // 1. 如果内容正在变化，跳过初始化
      if (window._contentChanging) {
        console.log('电商模板内容变化中，跳过字段初始化以避免覆盖用户输入');
        return;
      }

      // 2. 如果是校验失败后的重新显示，跳过初始化
      if (window._isEcommerceValidationFailed) {
        console.log('电商模板校验失败后重新显示，跳过字段初始化以保护用户输入');
        return;
      }

      // 3. 如果字段已经有用户输入的值，且不是来自props更新，跳过初始化
      if (actionUrl.value && actionUrl.value.trim() !== '' && !window._isPropsUpdate) {
        console.log('电商模板已有用户输入，跳过初始化以保护用户输入:', actionUrl.value);
        return;
      }

      // 4. 如果是展开设置面板操作，且字段已有值，跳过初始化
      if (window._isExpandingSettings && actionUrl.value && actionUrl.value.trim() !== '') {
        console.log('电商模板展开设置面板时已有用户输入，跳过初始化以保护用户输入');
        return;
      }
    }

    // 对于多商品模板，添加更强的保护逻辑
    if (contentType === 'multi-product-settings-button') {
      // 如果用户正在编辑任何字段，跳过初始化
      const hasUserInput = (
        (emailAddress.value !== undefined && emailAddress.value !== null && emailAddress.value !== '') ||
        (emailSubject.value !== undefined && emailSubject.value !== null && emailSubject.value !== '') ||
        (emailBody.value !== undefined && emailBody.value !== null && emailBody.value !== '') ||
        (scheduleTitle.value !== undefined && scheduleTitle.value !== null && scheduleTitle.value !== '') ||
        (scheduleContent.value !== undefined && scheduleContent.value !== null && scheduleContent.value !== '') ||
        (scheduleStartTimeString.value !== undefined && scheduleStartTimeString.value !== null && scheduleStartTimeString.value !== '') ||
        (scheduleEndTimeString.value !== undefined && scheduleEndTimeString.value !== null && scheduleEndTimeString.value !== '') ||
        (popupTitle.value !== undefined && popupTitle.value !== null && popupTitle.value !== '') ||
        (popupContent.value !== undefined && popupContent.value !== null && popupContent.value !== '') ||
        (popupButtonText.value !== undefined && popupButtonText.value !== null && popupButtonText.value !== '')
      );

      if (hasUserInput && !window._isPropsUpdate) {
        console.log('多商品模板字段已有值，跳过初始化以保护用户输入:', {
          emailAddress: emailAddress.value,
          scheduleTitle: scheduleTitle.value,
          scheduleStartTimeString: scheduleStartTimeString.value,
          popupTitle: popupTitle.value
        });
        return;
      }

      // 如果正在更新内容，也跳过初始化
      if (window._isUpdatingContent) {
        console.log('多商品模板正在更新内容，跳过初始化');
        return;
      }
    }
    
    // 其他原有逻辑保持不变...
    // 对于横滑模板，优先从props.content中获取数据
    if (isHorizontalSwipeTemplate.value && props.content) {
      console.log('横滑模板：优先从props.content初始化字段');

      // 初始化所有字段，优先使用props.content中的值
      packageName.value = props.content.packageName || '';
      floorType.value = props.content.floorType || '0';
      emailAddress.value = props.content.emailAddress || '';
      emailSubject.value = props.content.emailSubject || '';
      emailBody.value = props.content.emailBody || '';
      scheduleTitle.value = props.content.scheduleTitle || '';
      scheduleContent.value = props.content.scheduleContent || '';
      scheduleStartTimeString.value = props.content.scheduleStartTimeString || '';
      scheduleEndTimeString.value = props.content.scheduleEndTimeString || '';
      popupTitle.value = props.content.popupTitle || '';
      popupContent.value = props.content.popupContent || '';
      popupButtonText.value = props.content.popupButtonText || '';

      console.log('横滑模板字段初始化 - 弹窗字段:', {
        popupTitle: popupTitle.value,
        popupContent: popupContent.value,
        popupButtonText: popupButtonText.value,
        propsPopupTitle: props.content.popupTitle,
        propsPopupContent: props.content.popupContent,
        propsPopupButtonText: props.content.popupButtonText
      });
      copyType.value = props.content.copyType || '1';
      selectedParamId.value = props.content.selectedParamId || '';
      fixedContent.value = props.content.fixedContent || '';
      actionUrl.value = props.content.actionUrl || props.content.url || '';
      actionPath.value = props.content.actionPath || props.content.path || '';
      
      console.log('横滑模板字段初始化完成:', {
        actionType: props.content.actionType,
        packageName: packageName.value,
        emailAddress: emailAddress.value,
        popupTitle: popupTitle.value,
        actionUrl: actionUrl.value
      });
      
      return;
    }

    // 获取缓存 - 使用统一的缓存键格式
    const contentId = props.content?.contentId || '';
    const cacheKey = cacheUtils.getCacheKey(contentType, contentId);
    const currentCache = window.CLICK_EVENT_CACHE?.[cacheKey] || {};
    
    // 其他模板：使用原有的缓存逻辑
    // 优先从props.content获取值，然后从缓存获取
    // 对于所有模板，增加更强的保护逻辑，避免覆盖用户输入
    let initialActionUrl = '';
    let initialActionPath = '';

    // 统一的保护逻辑：优先使用当前用户输入的值，避免被覆盖
    initialActionUrl = actionUrl.value || props.content?.actionUrl || currentCache.actionUrl || '';
    initialActionPath = actionPath.value || props.content?.actionPath || currentCache.actionPath || '';
    
    // 为所有字段添加保护逻辑：优先使用当前用户输入的值
    const initialPackageName = packageName.value || props.content?.packageName || currentCache.packageName || '';
    const initialFloorType = floorType.value || props.content?.floorType || currentCache.floorType || '0';
    const initialEmailAddress = emailAddress.value || props.content?.emailAddress || currentCache.emailAddress || '';
    const initialEmailSubject = emailSubject.value || props.content?.emailSubject || currentCache.emailSubject || '';
    const initialEmailBody = emailBody.value || props.content?.emailBody || currentCache.emailBody || '';

    console.log('初始化邮箱字段:', {
      propsEmailAddress: props.content?.emailAddress,
      cacheEmailAddress: currentCache.emailAddress,
      finalEmailAddress: initialEmailAddress
    });
    const initialScheduleTitle = scheduleTitle.value || props.content?.scheduleTitle || currentCache.scheduleTitle || '';
    const initialScheduleContent = scheduleContent.value || props.content?.scheduleContent || currentCache.scheduleContent || '';
    const initialScheduleStart = scheduleStartTimeString.value || props.content?.scheduleStartTimeString || currentCache.scheduleStartTimeString || '';
    const initialScheduleEnd = scheduleEndTimeString.value || props.content?.scheduleEndTimeString || currentCache.scheduleEndTimeString || '';
    const initialPopupTitle = popupTitle.value || props.content?.popupTitle || currentCache.popupTitle || '';
    const initialPopupContent = popupContent.value || props.content?.popupContent || currentCache.popupContent || '';
    const initialPopupButtonText = popupButtonText.value || props.content?.popupButtonText || currentCache.popupButtonText || '';
    const initialCopyType = copyType.value || props.content?.copyType || currentCache.copyType || '1';
    const initialParamId = selectedParamId.value || props.content?.selectedParamId || currentCache.selectedParamId || '';
    const initialFixedContent = fixedContent.value || props.content?.fixedContent || currentCache.fixedContent || '';

    console.log('非横滑模板初始化 - 弹窗字段计算:', {
      propsPopupTitle: props.content?.popupTitle,
      cachePopupTitle: currentCache.popupTitle,
      initialPopupTitle: initialPopupTitle,
      propsPopupContent: props.content?.popupContent,
      cachePopupContent: currentCache.popupContent,
      initialPopupContent: initialPopupContent,
      propsPopupButtonText: props.content?.popupButtonText,
      cachePopupButtonText: currentCache.popupButtonText,
      initialPopupButtonText: initialPopupButtonText
    });

    // 设置字段值 - 添加标志防止触发保存
    window._isInitializingFields = true;

    actionUrl.value = initialActionUrl;
    actionPath.value = initialActionPath;
    packageName.value = initialPackageName;
    floorType.value = initialFloorType;
    emailAddress.value = initialEmailAddress;
    emailSubject.value = initialEmailSubject;
    emailBody.value = initialEmailBody;
    scheduleTitle.value = initialScheduleTitle;
    scheduleContent.value = initialScheduleContent;
    scheduleStartTimeString.value = initialScheduleStart;
    scheduleEndTimeString.value = initialScheduleEnd;
    popupTitle.value = initialPopupTitle;
    popupContent.value = initialPopupContent;
    popupButtonText.value = initialPopupButtonText;

    // 延迟清除标志
    setTimeout(() => {
      window._isInitializingFields = false;
    }, 200);

    console.log('非横滑模板初始化 - 弹窗字段设置后:', {
      popupTitle: popupTitle.value,
      popupContent: popupContent.value,
      popupButtonText: popupButtonText.value
    });
    
    // 根据复制类型设置相应的值
    if (initialCopyType === '2') {
      copyType.value = '2';
      fixedContent.value = initialFixedContent;
      selectedParamId.value = '';
    } else {
      copyType.value = '1';
      selectedParamId.value = initialParamId;
      fixedContent.value = '';
    }

  } catch (error) {
    console.error('初始化字段时出错:', error);
  }
};

// 监听关键字段变化，立即保存到缓存
watch([emailAddress, emailSubject, emailBody, scheduleTitle, scheduleContent, scheduleStartTimeString, scheduleEndTimeString, popupTitle, popupContent, popupButtonText, fixedContent, selectedParamId, copyType], () => {
  // 防止在初始化过程中触发保存
  if (!window._isInitializingFields && !window._contentChanging && !window._isUpdatingContent) {
    // 延迟保存，避免频繁触发
    setTimeout(() => {
      saveFieldDataToCache();
    }, 100);
  }
}, { deep: true });

// 监听事件类型变化，清空不相关字段
watch(currentActionType, (newType, oldType) => {
  // 增加更强的保护条件，避免在数据同步和类型设置过程中错误清空字段
  if (newType !== oldType && oldType && 
      !window._isUpdatingContent && 
      !window._contentChanging && 
      !window._isInitializingFields &&
      !window._isSettingActionType) {  // 新增：避免在设置类型时触发
    
    console.log(`事件类型从 ${oldType} 切换到 ${newType}，开始处理字段`);
    
    // 对于横滑模板，进行额外的验证，确保这不是数据同步过程中的临时状态
    if (isHorizontalSwipeTemplate.value) {
      // 检查是否有对应旧类型的特定字段存在
      const hasOldTypeFields = (
        (oldType === CLICK_EVENT_TYPES.OPEN_APP && props.content?.packageName) ||
        (oldType === CLICK_EVENT_TYPES.OPEN_EMAIL && props.content?.emailAddress) ||
        (oldType === CLICK_EVENT_TYPES.OPEN_POPUP && props.content?.popupTitle) ||
        (oldType === CLICK_EVENT_TYPES.OPEN_SCHEDULE && props.content?.scheduleStartTimeString) ||
        (oldType === CLICK_EVENT_TYPES.COPY_PARAMETER && props.content?.copyType)
      );
      
      // 检查新类型是否为OPEN_BROWSER且存在旧类型字段
      // 这通常意味着数据同步过程中的临时状态
      if (hasOldTypeFields && newType === CLICK_EVENT_TYPES.OPEN_BROWSER) {
        console.log('检测到可能的数据同步中间状态，跳过字段清空');
        return;
      }
      
      // 如果是用户真正的类型切换（新类型不是OPEN_BROWSER，或者没有旧类型字段），才处理
      console.log('确认为用户操作的类型切换，继续处理字段');
    }

    // 只清空与新类型不兼容的字段，不动 actionType/type
    fieldUtils.clearFieldsByActionType(oldType);

    // 初始化新类型需要的字段（只针对内容字段，不动类型）
    switch (newType) {
      case CLICK_EVENT_TYPES.OPEN_APP:
        // 只有在字段为空时才设置默认值，避免覆盖用户输入
        if (!packageName.value && !props.content?.packageName) {
          packageName.value = '';
        }
        if (!floorType.value && !props.content?.floorType) {
          floorType.value = '0';
        }
        break;
      case CLICK_EVENT_TYPES.OPEN_EMAIL:
        if (!emailAddress.value && !props.content?.emailAddress) {
          emailAddress.value = '';
        }
        if (!emailSubject.value && !props.content?.emailSubject) {
          emailSubject.value = '';
        }
        if (!emailBody.value && !props.content?.emailBody) {
          emailBody.value = '';
        }
        break;
      case CLICK_EVENT_TYPES.OPEN_SCHEDULE:
        if (!scheduleTitle.value && !props.content?.scheduleTitle) {
          scheduleTitle.value = '';
        }
        if (!scheduleContent.value && !props.content?.scheduleContent) {
          scheduleContent.value = '';
        }
        if (!scheduleStartTimeString.value && !props.content?.scheduleStartTimeString) {
          scheduleStartTimeString.value = '';
        }
        if (!scheduleEndTimeString.value && !props.content?.scheduleEndTimeString) {
          scheduleEndTimeString.value = '';
        }
        break;
      case CLICK_EVENT_TYPES.OPEN_POPUP:
        if (!popupTitle.value && !props.content?.popupTitle) {
          popupTitle.value = '';
        }
        if (!popupContent.value && !props.content?.popupContent) {
          popupContent.value = '';
        }
        if (!popupButtonText.value && !props.content?.popupButtonText) {
          popupButtonText.value = '';
        }
        break;
      case CLICK_EVENT_TYPES.COPY_PARAMETER:
        copyType.value = '1';
        selectedParamId.value = '';
        fixedContent.value = '';
        break;
      default:
        // 其它类型只清空 actionUrl、actionPath
        actionUrl.value = '';
        actionPath.value = '';
        break;
    }

    // 只在需要时清空 actionPath
    const typesNeedingActionPath = [
      CLICK_EVENT_TYPES.OPEN_APP, CLICK_EVENT_TYPES.OPEN_URL, CLICK_EVENT_TYPES.OPEN_QUICK,
      CLICK_EVENT_TYPES.OPEN_SCHEDULE, CLICK_EVENT_TYPES.DIAL_PHONE, CLICK_EVENT_TYPES.OPEN_SMS,
      CLICK_EVENT_TYPES.COPY_PARAMETER, CLICK_EVENT_TYPES.OPEN_EMAIL, CLICK_EVENT_TYPES.OPEN_POPUP,
      CLICK_EVENT_TYPES.OPEN_BROWSER
    ];
    if (!typesNeedingActionPath.includes(newType)) {
      actionPath.value = '';
    }

    console.log(`字段处理完成，当前字段状态:`, {
      actionUrl: actionUrl.value,
      actionPath: actionPath.value,
      packageName: packageName.value,
      emailAddress: emailAddress.value,
      popupTitle: popupTitle.value
    });

    // 更新缓存
    updateGlobalCache();
  } else {
    console.log(`事件类型变化被跳过，原因: newType=${newType}, oldType=${oldType}, _isUpdatingContent=${window._isUpdatingContent}, _contentChanging=${window._contentChanging}, _isInitializingFields=${window._isInitializingFields}, _isSettingActionType=${window._isSettingActionType}`);
  }
});

// 监听内容变化时，提供额外的安全检查
watch(() => props.content, (newContent, oldContent) => {
  // 如果正在处理固定内容变化或复制类型切换，不处理内容更新
  if (window._isHandlingFixedContentChange || window._isHandlingCopyTypeChange || window._ignoreContentUpdate) {
    return;
  }
  
  if (newContent) {
    // 检查是否是真正的模板切换
    const isRealTemplateSwitch = window._isTemplateSwitching || window._isTemplateResetting;
    
    // 只有在不是真正的模板切换时才进行正常的内容同步
    if (!isRealTemplateSwitch) {
      // 设置内容变更标志防止同一tick内重复处理
      window._contentChanging = true;
      
      try {
        // 保持设置组展开
        isExpanded.value = true;
        
        // 获取缓存键 - 使用统一的缓存键格式
        const contentType = newContent.type;
        const contentId = newContent.contentId || '';
        const cacheKey = cacheUtils.getCacheKey(contentType, contentId);
        
        // 确保缓存存在
        if (!window.CLICK_EVENT_CACHE) {
          window.CLICK_EVENT_CACHE = {};
        }
        
        // 从内容对象更新缓存
        if (!window.CLICK_EVENT_CACHE[cacheKey]) {
          window.CLICK_EVENT_CACHE[cacheKey] = {};
        }
        
        // 优先使用内容对象中的数据更新缓存
        if (newContent.actionType || newContent.actionUrl || newContent.actionPath) {
          window.CLICK_EVENT_CACHE[cacheKey] = {
            actionType: newContent.actionType || defaultActionType,
            actionUrl: newContent.actionUrl || '',
            actionPath: newContent.actionPath || '',
            // 保存其他相关字段
            packageName: newContent.packageName|| '',
            floorType: newContent.floorType || '0',
            // hwAppId: newContent.hwAppId || '',
            // ryAppId: newContent.ryAppId || '',
            emailAddress: newContent.emailAddress || '',
            emailSubject: newContent.emailSubject || '',
            emailBody: newContent.emailBody || '',
            scheduleTitle: newContent.scheduleTitle || '',
            scheduleContent: newContent.scheduleContent || '',
            scheduleStartTimeString: newContent.scheduleStartTimeString || '',
            scheduleEndTimeString: newContent.scheduleEndTimeString || '',
            popupTitle: newContent.popupTitle || '',
            popupContent: newContent.popupContent || '',
            popupButtonText: newContent.popupButtonText || ''
          };
        }
        
        // 初始化字段
        nextTick(() => {
          initializeFields();
        });
      } finally {
        // 清除内容变更标志
        setTimeout(() => {
          window._contentChanging = false;
        }, 100);
      }
    } else {
      console.log('检测到真正的模板切换，跳过内容同步');
    }
  }
}, { deep: true, immediate: true });

// 在组件挂载时初始化模板cardId
onMounted(() => {
  // 初始化模板cardId
  initializeTemplateCardId();
  
  // 监听模板切换事件（新增）
  removeSwitchListener = addTemplateSwitchListener((newTemplate, oldTemplate) => {
    if (oldTemplate?.cardId) {
      // 清除旧模板的所有缓存（匹配以旧cardId开头的键）
      Object.keys(window.CLICK_EVENT_CACHE).forEach(key => {
        if (key.startsWith(`${oldTemplate.cardId}_`)) {
          delete window.CLICK_EVENT_CACHE[key];
        }
      });
      console.log(`模板切换：已清除旧模板${oldTemplate.cardId}的点击事件缓存`);
    }
  });

  // 初始化安全标记
  window._isLoadingParams = false;
  window._isHandlingParamChange = false;
  window._isHandlingCopyTypeChange = false;
  window._isHandlingFixedContentChange = false;
  window._ignoreUrlChange = false;
  window._ignoreCopyTypeChange = false;
  window._ignoreParamLoadResult = false;
  window._isProcessingCopyType = false;
  window._isUpdatingContent = false;
  window._isInitializingFields = false;
  window._contentChanging = false;
  
  // 初始化全局点击事件缓存
  if (!window.CLICK_EVENT_CACHE) {
    window.CLICK_EVENT_CACHE = {};
  }
  
  // 确保当前内容项的缓存存在
  if (props.content && props.content.type) {
    const contentType = props.content.type;
    const contentId = props.content?.contentId || '';
    const cacheKey = cacheUtils.getCacheKey(contentType, contentId);
    
    if (!window.CLICK_EVENT_CACHE[cacheKey]) {
      window.CLICK_EVENT_CACHE[cacheKey] = {
        actionType: props.content.actionType || defaultActionType,
        actionUrl: props.content.actionUrl || '',
        actionPath: props.content.actionPath || ''
      };
    }
    
    // 从内容对象同步到缓存
    if (props.content.actionUrl && !window.CLICK_EVENT_CACHE[cacheKey].actionUrl) {
      window.CLICK_EVENT_CACHE[cacheKey].actionUrl = props.content.actionUrl;
      console.log(`初始化: 从内容同步到缓存(${cacheKey}):`, props.content.actionUrl);
    }
  }
  
  // 初始化各种类型的字段
  nextTick(() => {
    // 延迟一些时间初始化字段，确保其他组件已经准备就绪
    // 但是要避免覆盖用户已经输入的内容
    setTimeout(() => {
      // 检查是否有用户输入，如果有则不重新初始化
      const hasUserInput = (
        (popupTitle.value !== undefined && popupTitle.value !== null && popupTitle.value !== '') ||
        (popupContent.value !== undefined && popupContent.value !== null && popupContent.value !== '') ||
        (popupButtonText.value !== undefined && popupButtonText.value !== null && popupButtonText.value !== '') ||
        (scheduleTitle.value !== undefined && scheduleTitle.value !== null && scheduleTitle.value !== '') ||
        (scheduleContent.value !== undefined && scheduleContent.value !== null && scheduleContent.value !== '')
      );

      if (!hasUserInput) {
        console.log('没有用户输入，执行初始化字段');
        initializeFields();
      } else {
        console.log('检测到用户输入，跳过初始化字段以避免覆盖用户数据');
      }
    }, 200);
  });
  
  // 添加参数更新事件监听
  const handleParamsUpdated = (event) => {
    // 延迟执行，避免连续多次触发
    setTimeout(() => {
      if (!window._isLoadingParams && !window._isUpdatingContent && !window._isInitializingFields) {
        loadAvailableParams();
      }
    }, 200);
  };
  
  // 监听自定义DOM事件
  document.addEventListener('template-params-updated', handleParamsUpdated);
  
  // 如果存在事件总线，也监听事件总线上的事件
  if (window.PARAM_EVENT_BUS) {
    window.PARAM_EVENT_BUS.on('params-updated', handleParamsUpdated);
  }
  
  removeResetListener = addTemplateResetListener(() => {
    // 只在真正的模板重置时才清空缓存
    console.log('ClickEventSettings - 收到模板重置事件，清空点击事件缓存');
    window._isTemplateResetting = true;
    window.CLICK_EVENT_CACHE = {};
    clearAllFieldData();
    clearEditors();
    
    // 延迟清除标志
    setTimeout(() => {
      window._isTemplateResetting = false;
    }, 1000);
  });

  removeSwitchListener = addTemplateSwitchListener(() => {
    // 添加更精确的判断，确保只在真正的模板切换时才清空缓存
    console.log('ClickEventSettings - 收到模板切换事件');
    
    // 设置模板切换标志
    window._isTemplateSwitching = true;
    
    // 检查当前模板信息
    const currentTemplate = window.TEMPLATE_DIALOG_DATA?.selectedTemplate;
    const globalTemplate = window.GLOBAL_TEMPLATE_DATA;
    
    console.log('当前模板信息:', {
      currentTemplate: currentTemplate?.cardId,
      globalTemplate: globalTemplate?.cardId,
      currentTemplateCardId: currentTemplateCardId.value
    });
    
    // 只有在以下情况才清空缓存：
    // 1. 没有当前模板信息
    // 2. 模板cardId确实发生了变化
    // 3. 全局模板数据发生了变化
    const shouldClearCache = !currentTemplate || 
                           !globalTemplate ||
                           (currentTemplateCardId.value && 
                            currentTemplateCardId.value !== currentTemplate.cardId) ||
                           (globalTemplate && currentTemplate && 
                            globalTemplate.cardId !== currentTemplate.cardId);
    
    if (shouldClearCache) {
      console.log('ClickEventSettings - 检测到真正的模板切换，清空点击事件缓存');
      window.CLICK_EVENT_CACHE = {};
      clearAllFieldData();
      clearEditors();
      
      // 更新当前模板cardId
      if (currentTemplate?.cardId) {
        currentTemplateCardId.value = currentTemplate.cardId;
        console.log('更新当前模板cardId:', currentTemplateCardId.value);
      }
    } else {
      console.log('ClickEventSettings - 同模板内操作，不清空缓存');
      // 如果不是真正的模板切换，清除标志
      window._isTemplateSwitching = false;
    }
    
    // 延迟清除标志
    setTimeout(() => {
      window._isTemplateSwitching = false;
    }, 1000);
  });
  
  // 监听自定义事件，确保缓存和内容之间的同步
  const handleClickEventUpdated = (event) => {
    if (!event.detail || !event.detail.contentId || !props.content || !props.content.contentId) {
      return;
    }
    
    // 忽略当前内容的事件
    if (event.detail.contentId === props.content.contentId) {
      return;
    }
    
    // 其他内容项更新了点击事件
    console.log(`收到其他内容(${event.detail.contentId})的点击事件更新, 当前内容: ${props.content.contentId}`);
    
    // 确保当前内容的缓存不被其他内容项的更新影响
    const contentType = props.content.type;
    const contentId = props.content.contentId;
    const currentCacheKey = cacheUtils.getCacheKey(contentType, contentId);
    
    if (window.CLICK_EVENT_CACHE && window.CLICK_EVENT_CACHE[currentCacheKey]) {
      // 保护当前缓存，确保不会被其他组件覆盖
      const currentCache = window.CLICK_EVENT_CACHE[currentCacheKey];
      
      // 防止当前内容项的缓存被其他内容项的事件覆盖
      setTimeout(() => {
        if (window.CLICK_EVENT_CACHE && 
            JSON.stringify(window.CLICK_EVENT_CACHE[currentCacheKey]) !== JSON.stringify(currentCache)) {
          console.log(`保护当前缓存(${currentCacheKey})不被覆盖`);
          window.CLICK_EVENT_CACHE[currentCacheKey] = {...currentCache};
        }
      }, 50);
    }
  };
  
  document.addEventListener('click-event-settings-updated', handleClickEventUpdated);
  
  // 组件卸载时移除事件监听器
  onBeforeUnmount(() => {
    document.removeEventListener('click-event-settings-updated', handleClickEventUpdated);
    // 清除当前模板的缓存（防止未正常切换时的残留）
    const currentCardId = currentTemplateCardId.value;
    if (currentCardId) {
      cacheUtils.clearTemplateCache(currentCardId);
    }
    // 在卸载前同步当前设置到内容和缓存
    syncSettingToContent();
    
    // 只有在真正的模板切换时才清空字段数据
    // 检查是否是模板切换导致的组件卸载
    const isTemplateSwitch = window._isTemplateSwitching || 
                            window._isTemplateResetting ||
                            (window.TEMPLATE_DIALOG_DATA?.selectedTemplate?.cardId !== currentTemplateCardId.value);
    
    // 添加更严格的检查：只有在明确的模板切换标志时才清空
    if (isTemplateSwitch && (window._isTemplateSwitching || window._isTemplateResetting)) {
      console.log('检测到真正的模板切换，清空字段数据');
      clearAllFieldData();
    } else {
      console.log('组件切换或同模板内操作，保留字段数据');
      // 确保缓存数据被正确保存
      if (props.content && props.content.type) {
        const contentType = props.content.type;
        const contentId = props.content.contentId || '';
        const cacheKey = cacheUtils.getCacheKey(contentType, contentId);
        
        // 保存当前状态到缓存
        if (window.CLICK_EVENT_CACHE && !window.CLICK_EVENT_CACHE[cacheKey]) {
          window.CLICK_EVENT_CACHE[cacheKey] = {
            actionType: currentActionType.value || defaultActionType,
            actionUrl: actionUrl.value || '',
            actionPath: actionPath.value || '',
            ...collectFieldData(),
            _lastUpdated: Date.now()
          };
          console.log(`保存缓存数据(${cacheKey}):`, window.CLICK_EVENT_CACHE[cacheKey]);
        }
      }
    }
  });
});

// 完全重写loadAvailableParams方法以修复参数加载问题
const loadAvailableParams = () => {
  // 如果已经在加载中，直接返回
  if (window._isLoadingParams === true) {
    console.log('参数加载中，跳过重复请求');
    return;
  }

  try {
    // 设置加载标志
    window._isLoadingParams = true;
    
    // 清空参数列表
    availableParams.value = [];
    
    // 收集实际使用的参数
    const activeParams = [];
    
    // 限定在模板编辑器范围内查找参数
    const templateEditor = document.querySelector('.template-editor-container');
    if (!templateEditor) {
      console.warn('未找到模板编辑器容器，无法加载参数');
      return;
    }
    
    // 仅在编辑器中搜索参数元素，不查询整个document
    const allParamElements = templateEditor.querySelectorAll('input.j-btn[data-param-id], span.j-btn[data-param-id], .param-input, [data-param-id]');
    
    // 从DOM元素提取参数ID
    const paramIds = new Set();
    allParamElements.forEach(el => {
      const paramId = el.getAttribute('data-param-id');
      if (paramId) {
        paramIds.add(paramId);
      }
    });
    
    // 查找所有contenteditable元素中的参数
    const editableElements = templateEditor.querySelectorAll('[contenteditable="true"], .editable-content');
    editableElements.forEach(el => {
      if (!el) return;
      
      // 从HTML内容中提取参数ID
      const content = el.innerHTML || '';
      const regex = /{#param(\d+)#}/g;
      let match;
      while ((match = regex.exec(content)) !== null) {
        if (match[1]) {
          paramIds.add(match[1]);
        }
      }
    });
    
    // 为每个找到的参数ID创建一个参数对象
    paramIds.forEach(id => {
      const paramName = `param${id}`;
      
      // 检查是否有已定义的参数
      let existingParam = null;
      if (window.TEMPLATE_PARAMS && Array.isArray(window.TEMPLATE_PARAMS)) {
        existingParam = window.TEMPLATE_PARAMS.find(p => p && p.id && p.id.toString() === id.toString());
      }
      
      // 使用现有参数或创建新参数
      activeParams.push({
        id: id.toString(),
        name: existingParam?.name || paramName,
        example: existingParam?.example || `示例值${id}`
      });
    });
    
    // 更新参数列表
    availableParams.value = activeParams;
    
    // 如果有参数，自动选择第一个
    if (availableParams.value.length > 0 && !selectedParamId.value) {
      selectedParamId.value = availableParams.value[0].id;
      
      // 安全处理参数变化
      if (!window._isHandlingParamChange) {
        setTimeout(() => {
          handleParamChange(selectedParamId.value);
        }, 50);
      }
    }
    
    console.log(`参数加载完成，共找到 ${availableParams.value.length} 个参数`);
  } catch (err) {
    console.error('加载参数列表时出错:', err);
    availableParams.value = [];
  } finally {
    // 总是清除加载标志
    setTimeout(() => {
      window._isLoadingParams = false;
    }, 100);
  }
};

// 清除所有参数编辑器
const clearEditors = () => {
  try {
    if (clickEventEditor.value && typeof clickEventEditor.value.clear === 'function') {
      clickEventEditor.value.clear();
      console.log('编辑器内容已清除');
    }
  } catch (err) {
    console.warn('清除编辑器内容失败:', err);
  }
};

// 使用自定义按钮更新方式，避免触发监听
const handleManualUpdate = () => {
  try {
    console.log('手动更新触发:', { actionType: currentActionType.value, actionUrl: actionUrl.value });
    
    // 对于电商模板，即使有其他更新进行中也要允许更新，避免用户输入丢失
    if (props.content?.type === 'ecommerce-settings') {
      // 强制保护场景：始终允许更新以保护用户输入
      const isProtectedScenario = window._isEcommerceValidationFailed || 
                                  window._isExpandingSettings || 
                                  window._contentChanging;
      
      if (isProtectedScenario) {
        console.log('电商模板保护场景，强制允许更新以保护用户输入:', {
          _isEcommerceValidationFailed: window._isEcommerceValidationFailed,
          _isExpandingSettings: window._isExpandingSettings,
          _contentChanging: window._contentChanging
        });
        updateContentWithAction();
        return;
      }
      
      // 正常场景：检查更新状态
      if (!window._isUpdatingContent && !window._isInitializingFields) {
        updateContentWithAction();
      } else {
        console.log('电商模板：其他更新进行中，但仍允许更新以避免数据丢失');
        updateContentWithAction();
      }
    } else {
      // 其他模板：正常逻辑
      if (!window._isUpdatingContent && !window._isInitializingFields) {
        updateContentWithAction();
      } else {
        console.log('其他更新进行中，跳过重复请求');
      }
    }
  } catch (error) {
    console.error('手动更新时出错:', error);
  }
};

// 监听界面字段变化 - 合并为一个批量更新方法
const batchUpdateFields = () => {
  try {
    // 对于电商模板，在保护场景下强制允许更新，避免用户输入丢失
    if (props.content?.type === 'ecommerce-settings') {
      const isProtectedScenario = window._isEcommerceValidationFailed || 
                                  window._isExpandingSettings || 
                                  window._contentChanging;
      
      if (isProtectedScenario) {
        console.log('电商模板保护场景，强制允许字段更新以保护用户输入:', {
          _isEcommerceValidationFailed: window._isEcommerceValidationFailed,
          _isExpandingSettings: window._isExpandingSettings,
          _contentChanging: window._contentChanging
        });
        // 直接执行更新，不进行其他检查
        handleManualUpdate();
        return;
      }
    }
    
    // 对于券商品模板，允许用户输入更新，即使其他更新进行中
    const isCouponProductTemplate = props.content?.type === 'header-image' ||
                                   props.content?.type === 'coupon-button' ||
                                   props.content?.type === 'product-button' ||
                                   // 关键修复：将 contentId 转换为字符串后再使用 includes
                               String(props.content?.contentId || '').includes('coupon') ||
                               String(props.content?.contentId || '').includes('product');

    // 如果设置了忽略标志或其他更新进行中，直接返回
    // 但对于券商品模板，允许用户输入更新
    if (window._ignoreUrlChange || window._isInitializingFields ||
        (window._isUpdatingContent && !isCouponProductTemplate)) {
      console.log('批量更新被跳过:', {
        _ignoreUrlChange: window._ignoreUrlChange,
        _isUpdatingContent: window._isUpdatingContent,
        _isInitializingFields: window._isInitializingFields,
        _contentChanging: window._contentChanging,
        isCouponProductTemplate: isCouponProductTemplate
      });
      return;
    }
    
    // 对于电商模板和券商品模板，即使_contentChanging为true也要允许更新，避免用户输入丢失
    if (window._contentChanging && (props.content?.type === 'ecommerce-settings' || isCouponProductTemplate)) {
      console.log('电商模板或券商品模板内容变化中，但仍允许字段更新以避免数据丢失', {
        contentType: props.content?.type,
        isCouponProductTemplate: isCouponProductTemplate
      });
    } else if (window._contentChanging) {
      console.log('批量更新被跳过: 内容变化中');
      return;
    }
    
    // 直接调用更新，不使用防抖
    handleManualUpdate();
  } catch (error) {
    console.error('批量更新字段时出错:', error);
  }
};

// 简化监听器，使用统一的批处理更新方法
watch([actionUrl, actionPath], batchUpdateFields, { deep: true });
watch([floorType], batchUpdateFields, { deep: true });
watch([emailAddress, emailSubject, emailBody], batchUpdateFields, { deep: true });
watch([scheduleTitle, scheduleContent, scheduleStartTimeString, scheduleEndTimeString], batchUpdateFields, { deep: true });
watch([popupTitle, popupContent, popupButtonText], batchUpdateFields, { deep: true });

// 添加一个新方法，用于将当前设置同步到所有内容
const syncSettingToContent = () => {
  try {
    if (!props.content) {
      console.warn('内容对象为空，无法同步设置');
      return;
    }
    
    // 获取当前内容的缓存键
    const contentType = props.content.type;
    const contentId = props.content.contentId || '';
    const cacheKey = cacheUtils.getCacheKey(contentType, contentId);
    
    const actionTypeValue = currentActionType.value || defaultActionType; 

    // 确保当前缓存存在
    if (!window.CLICK_EVENT_CACHE || !window.CLICK_EVENT_CACHE[cacheKey]) {
      console.warn(`缓存不存在: ${cacheKey}`);
      return;
    }
  
    // 先更新全局缓存，确保最新状态被保存
    updateGlobalCache();
    
    // 将当前设置同步到内容对象
    const currentCache = window.CLICK_EVENT_CACHE?.[cacheKey] || {};
    
    // 发出事件通知父组件当前内容已更新
    const dispatchEvent = new CustomEvent('click-event-settings-updated', {
      detail: {
        contentId,
        contentType,
        cacheKey,
        cache: JSON.parse(JSON.stringify(currentCache)), // 深拷贝防止引用问题
        timestamp: Date.now()
      }
    });
    document.dispatchEvent(dispatchEvent);
    
    // 保存当前设置到全局变量，以便其他组件访问
    window.LAST_UPDATED_CLICK_EVENT = {
      contentId,
      contentType,
      cacheKey,
      cache: JSON.parse(JSON.stringify(currentCache)), // 深拷贝防止引用问题
      timestamp: Date.now()
    };
    
    // 更新内容，确保包含所有字段
    emit('update:content', { 
      ...props.content, 
      actionType: currentCache.actionType || props.content.actionType || defaultActionType,
      actionUrl: currentCache.actionUrl || props.content.actionUrl || '',
      actionPath: currentCache.actionPath || props.content.actionPath || '',
      packageName: currentCache.actionType === CLICK_EVENT_TYPES.OPEN_APP && currentCache.packageName ? 
        (Array.isArray(currentCache.packageName) ? currentCache.packageName : [currentCache.packageName]) : 
        (currentCache.packageName || props.content.packageName || ''),
      floorType: currentCache.floorType || props.content.floorType || '',
      // hwAppId: currentCache.hwAppId || props.content.hwAppId || '',
      // ryAppId: currentCache.ryAppId || props.content.ryAppId || '',
      emailAddress: currentCache.emailAddress || props.content.emailAddress || '',
      emailSubject: currentCache.emailSubject || props.content.emailSubject || '',
      emailBody: currentCache.emailBody || props.content.emailBody || '',
      scheduleStartTimeString: currentCache.scheduleStartTimeString || props.content.scheduleStartTimeString || '',
      scheduleEndTimeString: currentCache.scheduleEndTimeString || props.content.scheduleEndTimeString || '',
      popupTitle: currentCache.popupTitle || props.content.popupTitle || '',
      popupContent: currentCache.popupContent || props.content.popupContent || '',
      popupButtonText: currentCache.popupButtonText || props.content.popupButtonText || '',
      _lastSyncTimestamp: Date.now()
    });
    
    console.log('设置同步到内容完成:', { contentId, contentType });
  } catch (error) {
    console.error('同步设置到内容出错:', error);
  }
};

// 修改updateContentWithAction方法，使用统一的ActionJsonGenerator
const updateContentWithAction = () => {
  try {
    console.log('开始updateContentWithAction');
    
    // 安全检查
    if (!props.content) {
      console.warn('内容对象为空，无法更新内容');
      return;
    }
    
    // 对于电商模板，在保护场景下强制允许更新，避免用户输入丢失
    if (props.content?.type === 'ecommerce-settings') {
      // 强制保护场景：始终允许更新以保护用户输入
      const isProtectedScenario = window._isEcommerceValidationFailed || 
                                  window._isExpandingSettings || 
                                  window._contentChanging ||
                                  window._isUpdatingContent;
      
      if (isProtectedScenario) {
        console.log('电商模板保护场景，强制更新以保护用户输入:', {
          _isEcommerceValidationFailed: window._isEcommerceValidationFailed,
          _isExpandingSettings: window._isExpandingSettings,
          _contentChanging: window._contentChanging,
          _isUpdatingContent: window._isUpdatingContent
        });
        // 继续执行更新逻辑，不返回
      }
    } else {
      // 其他模板：正常逻辑
      if (window._isUpdatingContent || window._contentChanging) {
        console.log('内容更新进行中，跳过重复请求');
        return;
      }
    }

    window._isUpdatingContent = true;
    // 获取当前动作类型 - 确保不会出现undefined
    const actionTypeValue = currentActionType.value || defaultActionType;
    // 获取缓存键
    const contentType = props.content.type;
    const contentId = props.content.contentId || '';
    // 使用统一的字段数据收集函数
    const additionalData = collectFieldData();
    // 使用统一的ActionJsonGenerator生成actionJson
    const generatedActionJson = ActionJsonGenerator.generate(
      actionTypeValue,
      actionUrl.value,
      actionPath.value,
      additionalData
    );
    // 构建更新的内容
    const updatedContent = { 
      ...props.content, 
      actionType: actionTypeValue,
      actionUrl: actionUrl.value,
      actionPath: actionPath.value,
      actionJson: generatedActionJson,
      ...additionalData,
      _lastUpdateTimestamp: Date.now()
    };
    // 保存到缓存
    updateGlobalCache();
    // 发出更新事件
    emit('update:content', updatedContent);
    console.log('内容更新完成:', { contentType, contentId, actionType: actionTypeValue });
  } catch (error) {
    console.error('更新内容时出错:', error);
  } finally {
    // 延迟清除标记，确保不会立即触发新的更新
    setTimeout(() => {
      window._isUpdatingContent = false;
    }, 100);
  }
};

// 监听packageName的变化
watch(packageName, (newValue, oldValue) => {
  console.log('packageName changed:', newValue);
  // 如果值没有真正变化，跳过处理
  if (newValue === oldValue) {
    return;
  }
  if (currentActionType.value === CLICK_EVENT_TYPES.OPEN_APP) {
    updateContentWithAction();
  }
}, { immediate: true });

// 横滑模板下，邮件字段的 v-model 绑定
const emailAddressModel = computed({
  get() {
    if (isHorizontalSwipeTemplate.value) {
      console.log('emailAddressModel get (横滑模板):', props.content.emailAddress);
      return props.content.emailAddress || '';
    }
    console.log('emailAddressModel get (普通模板):', emailAddress.value);
    return emailAddress.value;
  },
  set(val) {
    console.log('emailAddressModel set:', val, '横滑模板:', isHorizontalSwipeTemplate.value);

    // 设置标志，防止初始化干扰
    window._isUserEditing = true;

    if (isHorizontalSwipeTemplate.value) {
      emit('update:content', { ...props.content, emailAddress: val });
    } else {
      emailAddress.value = val;
      // 立即更新缓存，避免被旧缓存覆盖
      updateGlobalCache();
    }

    // 延迟清除标志
    setTimeout(() => {
      window._isUserEditing = false;
    }, 100);
  }
});

const emailSubjectModel = computed({
  get() {
    if (isHorizontalSwipeTemplate.value) {
      return props.content.emailSubject || '';
    }
    return emailSubject.value;
  },
  set(val) {
    if (isHorizontalSwipeTemplate.value) {
      emit('update:content', { ...props.content, emailSubject: val });
    } else {
      emailSubject.value = val;
    }
  }
});

const emailBodyModel = computed({
  get() {
    if (isHorizontalSwipeTemplate.value) {
      return props.content.emailBody || '';
    }
    return emailBody.value;
  },
  set(val) {
    if (isHorizontalSwipeTemplate.value) {
      emit('update:content', { ...props.content, emailBody: val });
    } else {
      emailBody.value = val;
    }
  }
});

// 横滑模板下，日程字段的 v-model 绑定
const scheduleTitleModel = computed({
  get() {
    if (isHorizontalSwipeTemplate.value) {
      return props.content.scheduleTitle || '';
    }
    // 如果本地变量为空，但props.content中有值，使用props.content中的值
    const localValue = scheduleTitle.value;
    const propsValue = props.content?.scheduleTitle || '';
    const result = localValue || propsValue;
    return result;
  },
  set(val) {
    if (isHorizontalSwipeTemplate.value) {
      emit('update:content', { ...props.content, scheduleTitle: val });
    } else {
      scheduleTitle.value = val;
      // 触发内容更新，确保数据被传递给父组件
      handleManualUpdate();
    }
  }
});

const scheduleContentModel = computed({
  get() {
    if (isHorizontalSwipeTemplate.value) {
      return props.content.scheduleContent || '';
    }
    // 如果本地变量为空，但props.content中有值，使用props.content中的值
    const localValue = scheduleContent.value;
    const propsValue = props.content?.scheduleContent || '';
    const result = localValue || propsValue;
    return result;
  },
  set(val) {
    if (isHorizontalSwipeTemplate.value) {
      emit('update:content', { ...props.content, scheduleContent: val });
    } else {
      scheduleContent.value = val;
      // 触发内容更新，确保数据被传递给父组件
      handleManualUpdate();
    }
  }
});

const scheduleStartTimeStringModel = computed({
  get() {
    if (isHorizontalSwipeTemplate.value) {
      return props.content.scheduleStartTimeString || '';
    }
    return scheduleStartTimeString.value;
  },
  set(val) {
    if (isHorizontalSwipeTemplate.value) {
      emit('update:content', { ...props.content, scheduleStartTimeString: val });
    } else {
      scheduleStartTimeString.value = val;
      // 触发内容更新，确保数据被传递给父组件
      handleManualUpdate();
    }
  }
});

const scheduleEndTimeStringModel = computed({
  get() {
    if (isHorizontalSwipeTemplate.value) {
      return props.content.scheduleEndTimeString || '';
    }
    return scheduleEndTimeString.value;
  },
  set(val) {
    if (isHorizontalSwipeTemplate.value) {
      emit('update:content', { ...props.content, scheduleEndTimeString: val });
    } else {
      scheduleEndTimeString.value = val;
      // 触发内容更新，确保数据被传递给父组件
      handleManualUpdate();
    }
  }
});

// 横滑模板下，弹窗字段的 v-model 绑定
const popupTitleModel = computed({
  get() {
    if (isHorizontalSwipeTemplate.value) {
      return props.content.popupTitle || '';
    }
    // 如果本地变量为空，但props.content中有值，使用props.content中的值
    const localValue = popupTitle.value;
    const propsValue = props.content?.popupTitle || '';
    const result = localValue || propsValue;
    return result;
  },
  set(val) {
    if (isHorizontalSwipeTemplate.value) {
      emit('update:content', { ...props.content, popupTitle: val });
    } else {
      popupTitle.value = val;
      // 触发内容更新，确保数据被传递给父组件
      handleManualUpdate();
    }
  }
});

const popupContentModel = computed({
  get() {
    if (isHorizontalSwipeTemplate.value) {
      return props.content.popupContent || '';
    }
    // 如果本地变量为空，但props.content中有值，使用props.content中的值
    const localValue = popupContent.value;
    const propsValue = props.content?.popupContent || '';
    const result = localValue || propsValue;
    return result;
  },
  set(val) {
    if (isHorizontalSwipeTemplate.value) {
      emit('update:content', { ...props.content, popupContent: val });
    } else {
      popupContent.value = val;
      // 触发内容更新，确保数据被传递给父组件
      handleManualUpdate();
    }
  }
});

const popupButtonTextModel = computed({
  get() {
    if (isHorizontalSwipeTemplate.value) {
      return props.content.popupButtonText || '';
    }
    // 如果本地变量为空，但props.content中有值，使用props.content中的值
    const localValue = popupButtonText.value;
    const propsValue = props.content?.popupButtonText || '';
    const result = localValue || propsValue;
    return result;
  },
  set(val) {
    if (isHorizontalSwipeTemplate.value) {
      emit('update:content', { ...props.content, popupButtonText: val });
    } else {
      popupButtonText.value = val;
      // 触发内容更新，确保数据被传递给父组件
      handleManualUpdate();
    }
  }
});

// 监听actionUrl和actionPath的变化，去掉防抖，直接同步
watch([actionUrl, actionPath], batchUpdateFields, { deep: true });

// 监听packageName的变化，已是立即同步，无需更改

// 删除重复的监听器，避免冲突
// 删除actionUrlUpdateTimer等所有setTimeout相关防抖逻辑
</script>

<style lang="scss" scoped>
.setting-group {
  .required-icon{
    color: red;
    margin-right: 4px;
  }
  .group-title {
    font-size: 13px;
    font-weight: bold;
    color: #333;
    margin-bottom: 16px;
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #eee;
    padding: 14px;
   
    .expand-icon {
      transition: transform 0.3s;
      font-size: 16px;
      color: #909399;

      &.is-expanded {
        transform: rotate(180deg);
      }
    }
  }

  .setting-content {
    transition: all 0.3s;
    padding: 0px 14px;
  }

  .setting-item {
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    

    .select-with-tooltip {
      display: flex;
      align-items: center;
      gap: 10px;
      flex: 1;

      :deep(.el-select) {
        flex: 1;
      }

      .help-icon {
        font-size: 20px;
        color: #909399;
        cursor: help;
        color: #409EFF;
      }
    }

    :deep(.el-input),
    :deep(.el-select) {
      width: 100%;
    }

    &:last-child {
      margin-bottom: 20px;
    }
  }
  
  .setting-item-link {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
    
    .setting-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }
  
    .tips{
      font-size: 13px;
      color: red;
      margin-bottom: 8px;
    }
    .setting-tip {
      font-size: 12px;
      color: #E6A23C;
      margin-bottom: 8px;
    }
  }
  
  .validate-button {
    margin-top: 8px;
    align-self: flex-start;
  }

  .param-select-empty {
    color: #999;
    text-align: center;
    padding: 10px;
    border-radius: 4px;
    border: 1px dashed #dcdfe6;
  }

  .param-radio-list {
    display: flex;
    gap: 10px;
    width: 100%;
    max-height: 90px;
    overflow-y: auto;
    padding-right: 10px;
    
    :deep(.el-radio) {
      height: 20px;
      margin: 0;
      width: 110px;
      overflow: hidden;

      
      .el-radio__label {
        padding-left: 8px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }

  .copy-type-radio {
    width: 100%;
    display: flex;
    
    :deep(.el-radio) {
      margin-left: 0;
    }
  }
}
.setting-date-label{
  margin-right: 10px;
}
.setting-label {
  font-size: 14px;
  color: #666;
  min-width: 70px;
}
.setting-desc {
  font-size: 14px;
  color: #999;
  margin-top: 4px;
}
.expand-actions{
  font-size: 18px;
  color: #666;
  cursor: pointer;
  margin-top: 10px;
  text-align: center;
}
</style>

<style>
.custom-tooltip{
  color: #d2a470;
  width: 320px; 
  font-size: 13px;
}
</style>