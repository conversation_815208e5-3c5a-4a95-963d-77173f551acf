<template>
  <div class="ecommerce-settings">
    <!-- 样式选择 -->
    <div class="setting-section">
      <h4 class="section-title">样式选择</h4>
      <div class="style-options">
        <div 
          v-for="style in styleOptions" 
          :key="style.id"
          class="style-option"
          :class="{ 'is-selected': selectedStyle === style.id }"
          @click="handleStyleChange(style.id)"
        >
          <img 
            :src="getStyleImage(style.image)" 
            :alt="style.name"
            class="style-image"
          />
          <span class="style-name">{{ style.name }}</span>
        </div>
      </div>
    </div>

    <!-- 图片设置区域 -->
    <div v-if="localSettings.style === 'image'" class="setting-section">
      
      <!-- 轮播图控制器 -->
      <div class="carousel-controls">
        <!-- 减号按钮 -->
        <button 
          class="control-btn minus-btn" 
          :disabled="localSettings.images.length <= 1"
          @click="removeImage"
        >
          −
        </button>
        
        <!-- 数字按钮组（指示器） -->
        <div class="number-buttons">
          <button
            v-for="(image, index) in localSettings.images"
            :key="index"
            :class="['number-btn', { active: currentImageIndex === index }]"
            @click="selectImage(index)"
          >
            {{ index + 1 }}
          </button>
        </div>
        
        <!-- 加号按钮 -->
        <button 
          class="control-btn plus-btn" 
          :disabled="localSettings.images.length >= 8"
          @click="addDefaultImage"
        >
          +
        </button>
      </div>

      <!-- 轮播图预览 -->
      <div class="carousel-preview-container">
        <div class="carousel-wrapper">
          <el-carousel
            v-if="localSettings.images.length > 0"
            :key="carouselKey"
            :height="carouselHeight"
            :autoplay="false"
            :initial-index="currentImageIndex"
            :indicator-position="localSettings.images.length > 1 ? 'outside' : 'none'"
            :arrow="localSettings.images.length > 1 ? 'hover' : 'never'"
            ref="carouselRef"
            @change="handleSettingsCarouselChange"
            :loop="localSettings.images.length > 1"
            trigger="click"
          >
            <el-carousel-item 
              v-for="(image, index) in localSettings.images" 
              :key="`carousel-${index}-${image.src}-${carouselKey}`"
              @click.stop="selectImageFile"
            >
              <div class="carousel-image-wrapper">
                <img 
                  v-if="image && image.src" 
                  :src="getMediaUrl(image.src)" 
                  :alt="image.alt || `图片${index + 1}`"
                  class="carousel-image"
                  @error="handleImageError(image, index)"
                  @load="handleImageLoad(image, index)"
                />
                <div v-else class="no-image">
                  <el-icon><Picture /></el-icon>
                  <span>点击选择图片</span>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
        
        <!-- 当前图片描述 -->
        <div class="setting-desc">
          {{ localSettings.images[currentImageIndex]?.content || props.content.content || '该图片位建议选择比例为1：1（像素最小1088:1088）的图片，大小建议500KB以内' }}
        </div>
        
        <el-button 
          class="select-btn"
          @click="selectImageFile"
        >
          选择图片
        </el-button>
      </div>

      <!-- 当前选中图片的点击事件设置 -->
      <ClickEventSettings 
        v-if="currentImage"
        :content="currentImageContent"
        @update:content="handleClickEventUpdate"
        @insert-param="handleParamInsert"
        @manage-param="handleParamManage"
      />
    </div>

    <!-- 视频设置区域 - 暂时注释掉 -->
    <!--
    <div v-else-if="localSettings.style === 'video'" class="setting-section">
      <div class="video-upload-area">
        <div class="video-preview" v-if="localSettings.videoSrc && !isDefaultVideo(localSettings.videoSrc)">
          <video
            :src="getMediaUrl(localSettings.videoSrc)"
            controls
            class="preview-video"
          />
        </div>
        <div v-else class="video-placeholder">
          <img
            :src="getMediaUrl('/aim_files/aim_defult/defaultVideo.jpg')"
            alt="默认视频缩略图"
            class="video-default-image"
          />
          <div class="video-placeholder-icon">
            ▶
          </div>
        </div>
        <el-button  @click="selectVideoFile">
          选择视频
        </el-button>
      </div>
    </div>
    -->

    <!-- 价格显示开关 -->
    <div class="setting-section setting-card setting-card-price">
      <h4 class="is-show-price-title">¥是否显示</h4>
      <el-switch 
        v-model="showCurrencySymbol"
        @change="handleCurrencyDisplayChange"
      />
    </div>

    <!-- 价格文本编辑 -->
    <div class="setting-section setting-card">
      <h4 class="section-title">编辑价格</h4>
      <RichParamInput
        v-model="localSettings.priceText"
        @input="handleSettingsChange"
      />
    </div>

    <!-- 标签文本编辑 -->
    <div class="setting-section setting-card">
      <h4 class="section-title">编辑标签</h4>
      <RichParamInput
        v-model="localSettings.tagText"
        @input="handleSettingsChange"
      />
    </div>

    <!-- 主标题编辑 -->
    <div class="setting-section setting-card">
      <h4 class="section-title">编辑主标题</h4>
      <RichParamInput
        v-model="localSettings.titleText"
        @input="handleSettingsChange"
      />
    </div>

    <!-- 正文文本编辑 -->
    <div class="setting-section setting-card">
      <h4 class="section-title">编辑正文</h4>
      <RichParamInput
        v-model="localSettings.contentText"
        @input="handleSettingsChange"
      />
    </div>

    <!-- 按钮设置 -->
    <div class="setting-section setting-card">
      <h4 class="section-title">编辑按钮</h4>
      
      <!-- 按钮数量控制 -->
      <div class="button-count-control">
        <!-- 减号按钮 -->
        <button 
          class="control-btn minus-btn" 
          :disabled="buttonCount <= 1"
          @click="decreaseButtonCount"
        >
          −
        </button>
        
        <!-- 数字按钮组（指示器） -->
        <div class="number-buttons">
          <button
            v-for="(button, index) in visibleButtons"
            :key="index"
            :class="['number-btn', { active: currentVisualButtonIndex === index }]"
            @click="selectButton(index)"
          >
            {{ index + 1 }}
          </button>
        </div>
        
        <!-- 加号按钮 -->
        <button 
          class="control-btn plus-btn" 
          :disabled="buttonCount >= 2"
          @click="increaseButtonCount"
        >
          +
        </button>
      </div>

      <h4 class="button-name-title">按钮名称：<span>（最多7位）</span></h4>
      <RichParamInput
        v-model="currentButton.text"
        @input="handleButtonTextChange"
      />
      
      <div class="button-click-event-setting">
        <ClickEventSettings 
          :content="currentButtonContent"
          @update:content="handleButtonClickEventUpdate"
          @insert-param="handleParamInsert"
          @manage-param="handleParamManage"
        />
      </div>
     
    </div>
  </div>

  <!-- 媒体选择弹框 -->
  <MediaSelectorDialog 
    v-model="dialogVisible"
    :mediaType="dialogMediaType"
    @confirm="handleMediaSelect"
    :filter-app-key="appKey"
  />
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, inject } from 'vue';
import { ElMessage } from 'element-plus';
import MediaSelectorDialog from '../MediaSelectorDialog.vue';
import ClickEventSettings from './ClickEventSettings.vue';
import RichParamInput from '../richtext/RichParamInput.vue';
import { getMediaUrl } from '@/utils/mediaUtils';
import { CLICK_EVENT_TYPES, ActionJsonGenerator, ClickEventTypeConverter } from '@/utils/clickEventManager.js';

const props = defineProps({
  content: {
    type: Object,
    required: true
  },
  appKey: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['update:content', 'insert-param', 'manage-param']);

// 响应式数据
const dialogVisible = ref(false);
const dialogMediaType = ref('image');
const currentImageIndex = ref(0);
const carouselRef = ref(null);
// 选中的样式
const selectedStyle = ref('image');
// 价格货币符号显示控制
const showCurrencySymbol = ref(false);
const styleOptions = ref([
  {
    id: 'image',
    name: '图片',
    image: '/aim_files/aim_defult/eCommerce_image.png'
  }
  // 暂时注释掉视频选项
  // {
  //   id: 'video',
  //   name: '视频',
  //   image: '/aim_files/aim_defult/eCommerce_video.png'
  // }
]);

// 按钮相关的响应式数据
const buttonCount = ref(2); // 修改：默认显示2个按钮
const currentButtonIndex = ref(0);

// 添加一个标志来防止循环更新
const isUserSelecting = ref(false);

// 获取样式图片路径
const getStyleImage = (imagePath) => {
  try {
    // 使用服务器上的图片路径
    return getMediaUrl(imagePath);
  } catch (error) {
    // 返回一个简单的占位符数据URL
    return '/aim_files/aim_defult/simple.png';
  }
};

// 计算轮播图高度 - 与CarouselSettings保持一致
const carouselHeight = computed(() => {
  // 电商模板固定使用300px高度
  const height = '300px';
  console.log('EcommerceSettings - 轮播图高度:', height);
  return height;
});

// 轮播图唯一标识 - 与CarouselSettings保持一致
const carouselKey = computed(() => {
  const imageCount = localSettings.value.images.length;
  // 只在图片数量变化时更新key，避免过度重新渲染
  const key = `ecommerce-carousel-${imageCount}`;
  console.log('EcommerceSettings - 生成轮播图key:', key);
  return key;
});

// 本地设置数据
const localSettings = ref({
  style: 'image', // 'image' | 'video'
  images: [
    {
      src: '/aim_files/aim_defult/defaultImg48_65.jpg',
      alt: '图片1',
      clickEvent: {
        actionType: 'OPEN_BROWSER',
        actionUrl: ''
      }
    }
  ],
  // 新增按钮配置
  buttons: [
    {
      text: '编辑按钮',
      positionNumber: 6, // 第一个按钮位置
      hidden: false,
      clickEvent: {
        actionType: 'OPEN_BROWSER',
        actionUrl: ''
      }
    },
    {
      text: '编辑按钮',
      positionNumber: 7, // 第二个按钮位置
      hidden: false, // 修改：默认显示第二个按钮
      clickEvent: {
        actionType: 'OPEN_BROWSER',
        actionUrl: ''
      }
    }
  ],
  // 价格货币符号显示控制：1显示￥，0不显示￥
  aimCurrencyDisplay: 1,
  priceText: '',
  tagText: '',
  titleText: '',
  contentText: '',
  buttonText: '', // 保持向后兼容
  videoSrc: ''
});

// 当前选中的图片
const currentImage = computed(() => {
  // 确保localSettings.value和images数组存在
  if (!localSettings.value || !localSettings.value.images || !Array.isArray(localSettings.value.images)) {
    return { 
      src: '/aim_files/aim_defult/defaultImg48_65.jpg', 
      alt: '默认图片',
      positionNumber: 1,
      clickEvent: {
        actionType: 'OPEN_BROWSER',
        actionUrl: ''
      }
    };
  }
  
  // 确保currentImageIndex.value是有效的索引
  const index = Math.max(0, Math.min(currentImageIndex.value || 0, localSettings.value.images.length - 1));
  
  return localSettings.value.images[index] || { 
    src: '/aim_files/aim_defult/defaultImg48_65.jpg', 
    alt: '默认图片',
    positionNumber: 1,
    clickEvent: {
      actionType: 'OPEN_BROWSER',
      actionUrl: ''
    }
  };
});

// 当前选中图片的内容对象（用于点击事件设置组件）
const currentImageContent = computed(() => {
  try {
    const image = currentImage.value;
    if (!image) {
      return {
        type: 'image',
        contentId: 'ecommerce-image-0',
        src: '/aim_files/aim_defult/defaultImg48_65.jpg',
        alt: '默认图片',
        positionNumber: 1,
        actionType: CLICK_EVENT_TYPES.OPEN_BROWSER,
        actionUrl: '',
        actionPath: '',
      };
    }
    const clickEvent = image.clickEvent || {};
    
    // 电商模板使用新格式，直接使用actionType和actionUrl
    const actionType = clickEvent.actionType || CLICK_EVENT_TYPES.OPEN_BROWSER;
    const actionUrl = clickEvent.actionUrl || '';
    
    return {
      type: 'image',
      contentId: `ecommerce-image-${currentImageIndex.value || 0}`,
      src: image.src || '/aim_files/aim_defult/defaultImg48_65.jpg',
      alt: image.alt || '图片',
      positionNumber: 8 + currentImageIndex.value,
      actionType: actionType,
      actionUrl: actionUrl,
      actionPath: clickEvent.actionPath || '',
      packageName: clickEvent.packageName || '',
      floorType: clickEvent.floorType || '0',
      emailAddress: clickEvent.emailAddress || '',
      emailSubject: clickEvent.emailSubject || '',
      emailBody: clickEvent.emailBody || '',
      scheduleTitle: clickEvent.scheduleTitle || '',
      scheduleContent: clickEvent.scheduleContent || '',
      scheduleStartTimeString: clickEvent.scheduleStartTimeString || '',
      scheduleEndTimeString: clickEvent.scheduleEndTimeString || '',
      popupTitle: clickEvent.popupTitle || '',
      popupContent: clickEvent.popupContent || '',
      popupButtonText: clickEvent.popupButtonText || '',
      copyType: clickEvent.copyType || '1',
      selectedParamId: clickEvent.selectedParamId || '',
      fixedContent: clickEvent.fixedContent || ''
    };
  } catch (error) {
    console.error('EcommerceSettings - currentImageContent 计算错误:', error);
    return {
      type: 'image',
      contentId: 'ecommerce-image-0',
      src: '/aim_files/aim_defult/defaultImg48_65.jpg',
      alt: '默认图片',
      positionNumber: 1,
      actionType: CLICK_EVENT_TYPES.OPEN_BROWSER,
      actionUrl: '',
      actionPath: '',
      packageName: '',
      floorType: '0',
      emailAddress: '',
      emailSubject: '',
      emailBody: '',
      scheduleTitle: '',
      scheduleContent: '',
      scheduleStartTimeString: '',
      scheduleEndTimeString: '',
      popupTitle: '',
      popupContent: '',
      popupButtonText: '',
      copyType: '1',
      selectedParamId: '',
      fixedContent: ''
    };
  }
});

// 当前选中的按钮
const currentButton = computed(() => {
  try {
    // 确保localSettings.value和buttons数组存在
    if (!localSettings.value || !localSettings.value.buttons || !Array.isArray(localSettings.value.buttons)) {
      return {
        text: '编辑按钮',
        positionNumber: 6,
        clickEvent: {
          actionType: 'OPEN_BROWSER',
          actionUrl: ''
        }
      };
    }
    
    // 确保currentButtonIndex.value是有效的索引
    const index = Math.max(0, Math.min(currentButtonIndex.value || 0, localSettings.value.buttons.length - 1));
    
    if (index >= 0 && index < localSettings.value.buttons.length) {
      return localSettings.value.buttons[index];
    }
    
    return localSettings.value.buttons[0] || {
      text: '编辑按钮',
      positionNumber: 6,
      clickEvent: {
        actionType: 'OPEN_BROWSER',
        actionUrl: ''
      }
    };
  } catch (error) {
    console.error('EcommerceSettings - currentButton 计算错误:', error);
    return {
      text: '编辑按钮',
      positionNumber: 6,
      clickEvent: {
        actionType: 'OPEN_BROWSER',
        actionUrl: ''
      }
    };
  }
});

// 计算当前选中按钮在可见按钮中的视觉索引
const currentVisualButtonIndex = computed(() => {
  try {
    if (!localSettings.value || !localSettings.value.buttons || !Array.isArray(localSettings.value.buttons)) {
      return 0;
    }
    
    const visibleButtonsList = localSettings.value.buttons.filter((button, index) => {
      return index < buttonCount.value && !button.hidden;
    });
    
    const currentButton = localSettings.value.buttons[currentButtonIndex.value];
    return visibleButtonsList.findIndex(button => button === currentButton);
  } catch (error) {
    console.error('EcommerceSettings - currentVisualButtonIndex 计算错误:', error);
    return 0;
  }
});

// 当前选中按钮的内容对象（用于点击事件设置组件）
const currentButtonContent = computed(() => {
  try {
    const button = currentButton.value;
    if (!button) {
      return {
        type: 'button',
        contentId: 'ecommerce-button-6',
        text: '编辑按钮',
        positionNumber: 6,
        actionType: CLICK_EVENT_TYPES.OPEN_BROWSER,
        actionUrl: '',
        actionPath: '',
        packageName: '',
        floorType: '0',
        emailAddress: '',
        emailSubject: '',
        emailBody: '',
        scheduleTitle: '',
        scheduleContent: '',
        scheduleStartTimeString: '',
        scheduleEndTimeString: '',
        popupTitle: '',
        popupContent: '',
        popupButtonText: '',
        copyType: '1',
        selectedParamId: '',
        fixedContent: ''
      };
    }
    const clickEvent = button.clickEvent || {};
    // 电商模板使用新格式，直接使用actionType和actionUrl
    const actionType = clickEvent.actionType || CLICK_EVENT_TYPES.OPEN_BROWSER;
    const actionUrl = clickEvent.actionUrl || '';
    return {
      type: 'button',
      contentId: `ecommerce-button-${button.positionNumber}`,
      text: button.text || '',
      positionNumber: button.positionNumber || (6 + currentButtonIndex.value),
       actionType: clickEvent.actionType || CLICK_EVENT_TYPES.OPEN_BROWSER,
      actionUrl: clickEvent.actionUrl || '',
      actionPath: clickEvent.actionPath || '',
      packageName: clickEvent.packageName || '',
      floorType: clickEvent.floorType || '0',
      emailAddress: clickEvent.emailAddress || '',
      emailSubject: clickEvent.emailSubject || '',
      emailBody: clickEvent.emailBody || '',
      scheduleTitle: clickEvent.scheduleTitle || '',
      scheduleContent: clickEvent.scheduleContent || '',
      scheduleStartTimeString: clickEvent.scheduleStartTimeString || '',
      scheduleEndTimeString: clickEvent.scheduleEndTimeString || '',
      popupTitle: clickEvent.popupTitle || '',
      popupContent: clickEvent.popupContent || '',
      popupButtonText: clickEvent.popupButtonText || '',
      copyType: clickEvent.copyType || '1',
      selectedParamId: clickEvent.selectedParamId || '',
      fixedContent: clickEvent.fixedContent || ''
    };
  } catch (error) {
    console.error('EcommerceSettings - currentButtonContent 计算错误:', error);
    return {
      type: 'button',
      contentId: 'ecommerce-button-6',
      text: '编辑按钮',
      positionNumber: 6,
      actionType: CLICK_EVENT_TYPES.OPEN_BROWSER,
      actionUrl: '',
      actionPath: '',
      packageName: '',
      floorType: '0',
      emailAddress: '',
      emailSubject: '',
      emailBody: '',
      scheduleTitle: '',
      scheduleContent: '',
      scheduleStartTimeString: '',
      scheduleEndTimeString: '',
      popupTitle: '',
      popupContent: '',
      popupButtonText: '',
      copyType: '1',
      selectedParamId: '',
      fixedContent: ''
    };
  }
});

// 在模板中显示按钮的计算属性
const visibleButtons = computed(() => {
  const visible = localSettings.value.buttons.filter((button, index) => {
    const isVisible = index < buttonCount.value && !button.hidden;
    console.log(`EcommerceSettings - 按钮${index}可见性检查:`, {
      index,
      buttonCount: buttonCount.value,
      hidden: button.hidden,
      text: button.text,
      isVisible
    });
    return isVisible;
  });
  
  console.log('EcommerceSettings - visibleButtons计算结果:', {
    totalButtons: localSettings.value.buttons.length,
    buttonCount: buttonCount.value,
    visibleCount: visible.length,
    visibleButtons: visible.map((btn, idx) => ({ index: idx, text: btn.text, hidden: btn.hidden }))
  });
  
  return visible;
});

// 初始化设置数据
const initializeSettings = () => {
  console.log('=== EcommerceSettings - 开始初始化设置数据 ===');
  console.log('EcommerceSettings - props.content:', props.content);
  console.log('EcommerceSettings - props.content.currentData:', props.content?.currentData);
  
  if (props.content && props.content.currentData) {
    const data = props.content.currentData;
    
    // 深度合并数据，确保文本字段正确更新
    localSettings.value = {
      ...localSettings.value,
      ...data,
      // 明确设置文本字段，避免undefined覆盖现有值
      priceText: data.priceText !== undefined ? data.priceText : localSettings.value.priceText,
      tagText: data.tagText !== undefined ? data.tagText : localSettings.value.tagText,
      titleText: data.titleText !== undefined ? data.titleText : localSettings.value.titleText,
      contentText: data.contentText !== undefined ? data.contentText : localSettings.value.contentText,
      buttonText: data.buttonText !== undefined ? data.buttonText : localSettings.value.buttonText,
      // 处理按钮数据
      buttons: data.buttons && Array.isArray(data.buttons) ? data.buttons : localSettings.value.buttons
    };
    
    // 同步selectedStyle状态
    selectedStyle.value = localSettings.value.style || 'image';
    
    // 同步货币符号显示状态：1显示￥，0不显示￥
    showCurrencySymbol.value = (localSettings.value.aimCurrencyDisplay === 1);
    console.log('EcommerceSettings - 同步货币符号显示状态:', {
      aimCurrencyDisplay: localSettings.value.aimCurrencyDisplay,
      showCurrencySymbol: showCurrencySymbol.value
    });
    
    // 确保始终有两个按钮，并设置正确的hidden状态
    while (localSettings.value.buttons.length < 2) {
      const buttonIndex = localSettings.value.buttons.length;
      localSettings.value.buttons.push({
        text: buttonIndex === 0 ? '编辑按钮' : '编辑按钮',
        positionNumber: 6 + buttonIndex,
        hidden: false, // 修改：默认两个按钮都显示
        clickEvent: {
          actionType: 'OPEN_BROWSER',
          actionUrl: ''
        }
      });
    }

    // 同步按钮数量状态（计算可见按钮数量）
    if (localSettings.value.buttons && Array.isArray(localSettings.value.buttons)) {
      const visibleButtonCount = localSettings.value.buttons.filter(button => !button.hidden).length;
      buttonCount.value = Math.max(1, Math.min(visibleButtonCount, 2)); // 限制在1-2之间
      console.log('EcommerceSettings - 同步按钮数量:', {
        可见按钮数量: visibleButtonCount,
        设置的buttonCount: buttonCount.value,
        按钮状态: localSettings.value.buttons.map((btn, idx) => ({ 索引: idx, 隐藏: btn.hidden, 文本: btn.text }))
      });
    }
    
    // 同步当前按钮和图片索引
    if (typeof data.currentButtonIndex === 'number' && data.currentButtonIndex >= 0 && data.currentButtonIndex < buttonCount.value) {
      currentButtonIndex.value = data.currentButtonIndex;
      console.log('initializeSettings - 同步按钮索引到:', data.currentButtonIndex);
    }
    // 优化：保持有效的图片索引，避免不必要的重置
    if (typeof data.currentImageIndex === 'number' && 
        data.currentImageIndex >= 0 && 
        data.currentImageIndex < localSettings.value.images.length) {
      // 如果传入的索引有效，使用它
      currentImageIndex.value = data.currentImageIndex;
      console.log('initializeSettings - 同步图片索引到:', data.currentImageIndex);
    } else if (currentImageIndex.value >= localSettings.value.images.length) {
      // 只有当前索引超出范围时才重置
      currentImageIndex.value = 0;
      console.log('initializeSettings - 图片索引超出范围，重置为 0');
    } else {
      // 保持当前有效的索引
      console.log('initializeSettings - 保持当前图片索引:', currentImageIndex.value);
    }
    
    // 确保images数组存在且不为空
    if (!localSettings.value.images || localSettings.value.images.length === 0) {
      localSettings.value.images = [
        {
          src: '/aim_files/aim_defult/defaultImg48_65.jpg',
          alt: '图片1',
          clickEvent: {
            actionType: 'OPEN_BROWSER',
            actionUrl: ''
          }
        }
      ];
      console.log('EcommerceSettings - 添加默认图片');
    }

    // 确保按钮的hidden状态正确：第一个按钮总是显示，第二个按钮根据buttonCount显示
    if (localSettings.value.buttons.length >= 2) {
      localSettings.value.buttons[0].hidden = false; // 第一个按钮总是显示
      localSettings.value.buttons[1].hidden = buttonCount.value < 2; // 第二个按钮根据count显示
    }

    console.log('EcommerceSettings - 初始化后的按钮状态:', {
      buttonCount: buttonCount.value,
      buttons: localSettings.value.buttons.map((btn, idx) => ({ 
        索引: idx, 
        隐藏: btn.hidden, 
        文本: btn.text,
        positionNumber: btn.positionNumber 
      })),
      visibleButtons: localSettings.value.buttons.filter((btn, idx) => idx < buttonCount.value && !btn.hidden).length
    });

    // 确保每张图片都有完整的点击事件配置
    localSettings.value.images.forEach((image, index) => {
      if (!image.clickEvent) {
        image.clickEvent = {
          actionType: 'OPEN_BROWSER',
          actionUrl: ''
        };
      }
      if (!image.alt) {
        image.alt = `图片${index + 1}`;
      }
      // 只有在图片src完全为空或null时才设置默认图片，保持接口返回的真实路径
      if (!image.src || image.src === '' || image.src === null || image.src === undefined) {
        image.src = '/aim_files/aim_defult/defaultImg48_65.jpg';
        console.log('EcommerceSettings - 设置默认图片给空src的图片:', index);
      } else {
        console.log('EcommerceSettings - 保持接口返回的图片路径:', image.src);
      }
      // 确保positionNumber
      if (typeof image.positionNumber === 'undefined') {
        if (index === 0) {
          image.positionNumber = 1; // 第一张图片为1
        } else {
          image.positionNumber = 4 + index; // 第二张为5，第三张为6，以此类推
        }
      }
    });

    // 确保每个按钮都有完整的配置
    localSettings.value.buttons.forEach((button, index) => {
      if (!button.clickEvent) {
        button.clickEvent = {
          actionType: 'OPEN_BROWSER',
          actionUrl: ''
        };
      }
      if (!button.text) {
        button.text = index === 0 ? '编辑按钮' : '编辑按钮';
      }

      // 确保positionNumber
      if (typeof button.positionNumber === 'undefined') {
        button.positionNumber = 6 + index; // 第一个按钮为6，第二个为7
      }
      // 确保hidden状态
      if (button.hidden === undefined) {
        button.hidden = index >= buttonCount.value;
      }
    });
    
    // 暂时注释掉视频模式初始化逻辑
    // // 如果是视频模式但没有视频源，设置默认视频缩略图
    // if (localSettings.value.style === 'video' && !localSettings.value.videoSrc) {
    //   localSettings.value.videoSrc = '/aim_files/aim_defult/defaultVideo.jpg';
    //   console.log('视频模式初始化，设置默认视频缩略图');
    // }
  } else {
    console.log('EcommerceSettings - 没有传入设置数据，使用默认值');
    console.log('EcommerceSettings - 当前props.content结构:', {
      content: props.content,
      hasContent: !!props.content,
      hasCurrentData: !!(props.content && props.content.currentData),
      currentDataKeys: props.content?.currentData ? Object.keys(props.content.currentData) : 'N/A'
    });
    
    // 确保默认显示2个按钮
    buttonCount.value = 2; // 修改：默认显示2个按钮
    if (localSettings.value.buttons.length >= 2) {
      localSettings.value.buttons[0].hidden = false; // 确保第一个按钮显示
      localSettings.value.buttons[1].hidden = false; // 确保第二个按钮显示
    }
  }
  
  console.log('=== EcommerceSettings - 初始化完成，最终状态 ===');
  console.log('EcommerceSettings - 最终localSettings文本字段:', {
    priceText: localSettings.value.priceText,
    tagText: localSettings.value.tagText,
    titleText: localSettings.value.titleText,
    contentText: localSettings.value.contentText,
    buttonText: localSettings.value.buttonText,
    buttons: localSettings.value.buttons
  });
  console.log('EcommerceSettings - 最终localSettings完整数据:', localSettings.value);
  console.log('======================================');
};

// 样式切换处理
const handleStyleChange = (styleId) => {
  console.log('EcommerceSettings - 样式变化:', styleId);
  selectedStyle.value = styleId;
  localSettings.value.style = styleId;
  
  // 暂时注释掉视频模式处理
  // // 切换到视频模式时的特殊处理
  // if (styleId === 'video') {
  //   // 设置默认视频缩略图
  //   localSettings.value.videoSrc = '/aim_files/aim_defult/defaultVideo.jpg';
  //   console.log('切换到视频模式，设置默认视频缩略图:', localSettings.value.videoSrc);
  //
  //   // 确保视频样式选中状态正确显示
  //   nextTick(() => {
  //     console.log('视频模式UI更新完成');
  //   });
  // } else
  if (styleId === 'image') {
    // 切换到图片模式时，确保有默认图片
    if (!localSettings.value.images || localSettings.value.images.length === 0) {
      localSettings.value.images = [{
        src: '/aim_files/aim_defult/defaultImg48_65.jpg',
        alt: '默认图片',
        clickEvent: {
          actionType: 'OPEN_BROWSER',
          actionUrl: ''
        }
      }];
      console.log('切换到图片模式，设置默认图片');
    }
  }
  
  handleSettingsChange();
};

// 暂时注释掉视频相关函数
// // 判断是否为默认视频
// const isDefaultVideo = (videoSrc) => {
//   if (!videoSrc) return true;
//
//   // 检查是否是默认视频路径
//   return videoSrc === '/aim_files/aim_defult/defaultVideo.jpg' ||
//          videoSrc.includes('defaultVideo.jpg');
// };

// 处理设置变化
const handleSettingsChange = () => {
  console.log('EcommerceSettings - 设置发生变化:', localSettings.value);
  
  // 发送更新事件，包含当前轮播图索引
  emit('update:content', {
    type: 'ecommerce-settings',
    isEcommerceSettings: true,
    currentData: { 
      ...localSettings.value,
      currentImageIndex: currentImageIndex.value // 添加当前轮播图索引
    }
  });
};

// 轮播图操作方法
const addDefaultImage = () => {
  if (localSettings.value.images.length >= 8) {
    ElMessage.warning('最多只能添加8张图片');
    return;
  }
  
  const newImage = {
    src: '/aim_files/aim_defult/defaultImg48_65.jpg',
    alt: `图片${localSettings.value.images.length + 1}`,
    positionNumber: 8 + localSettings.value.images.length, // 第一张为8，后续递增
    clickEvent: {
      actionType: 'OPEN_BROWSER',
      actionUrl: ''
    }
  };
  
  localSettings.value.images.push(newImage);
  // 重要：设置当前索引为新添加的图片
  const newIndex = localSettings.value.images.length - 1;
  
  console.log('EcommerceSettings - 添加新图片:', {
    newImageIndex: newIndex,
    totalImages: localSettings.value.images.length,
    newImage: newImage
  });
  
  // 标记用户正在操作，防止外部更新干扰
  isUserSelecting.value = true;
  
  // 先更新本地索引
  currentImageIndex.value = newIndex;
  
  // 立即发出更新事件，包含正确的索引
  emit('update:content', {
    type: 'ecommerce-settings',
    isEcommerceSettings: true,
    currentData: { 
      ...localSettings.value,
      currentImageIndex: newIndex // 确保传递正确的索引
    }
  });
  
  nextTick(() => {
    if (carouselRef.value) {
      try {
        carouselRef.value.setActiveItem(newIndex);
        console.log('EcommerceSettings - 轮播图切换到新图片:', newIndex);
      } catch (error) {
        console.error('EcommerceSettings - 设置轮播图索引失败:', error);
      }
    }
    
    // 延迟重置用户选择标志，给予足够时间完成所有更新
    setTimeout(() => {
      isUserSelecting.value = false;
      console.log('EcommerceSettings - 重置用户选择标志，允许外部更新');
    }, 800); // 增加延迟时间到800ms，确保所有组件都完成更新
  });
  
  ElMessage.success(`添加图片成功，当前选中第${newIndex + 1}张图片`);
};

const removeImage = () => {
  if (localSettings.value.images.length <= 1) {
    ElMessage.warning('至少需要保留一张图片');
    return;
  }
  
  localSettings.value.images.splice(currentImageIndex.value, 1);

  // 重新分配positionNumber：第一张图片为1，后续从5开始
  localSettings.value.images.forEach((image, index) => {
    if (index === 0) {
      image.positionNumber = 1; // 第一张图片为1
    } else {
      image.positionNumber = 4 + index; // 第二张为5，第三张为6，以此类推
    }
  });
  
  if (currentImageIndex.value >= localSettings.value.images.length) {
    currentImageIndex.value = localSettings.value.images.length - 1;
  }
  
  nextTick(() => {
    if (carouselRef.value) {
      carouselRef.value.setActiveItem(currentImageIndex.value);
    }
  });
  
  handleSettingsChange();
  ElMessage.success('删除图片成功');
};

const selectImage = (index) => {
  console.log('EcommerceSettings - 选择图片:', index);
  isUserSelecting.value = true; // 标记用户正在选择
  currentImageIndex.value = index;
  
  // 关键修复：同步 localSettings.value.currentImageIndex
  localSettings.value.currentImageIndex = index;
  
  // 立即发出更新事件，确保索引同步
  emit('update:content', {
    type: 'ecommerce-settings',
    isEcommerceSettings: true,
    currentData: { 
      ...localSettings.value,
      currentImageIndex: index // 确保传递正确的索引
    }
  });
  
  // 确保轮播图组件同步到正确位置
  nextTick(() => {
    if (carouselRef.value) {
      carouselRef.value.setActiveItem(index);
    }
    
    console.log('EcommerceSettings - 当前选中图片的点击事件:', {
      imageIndex: index,
      clickEvent: localSettings.value.images[index]?.clickEvent,
      currentImageContent: currentImageContent.value
    });
    
    // 延迟重置标志，给足够时间完成切换
    setTimeout(() => {
      isUserSelecting.value = false;
      console.log('EcommerceSettings - 重置用户选择标志(selectImage)');
    }, 300);
  });
};

const handleSettingsCarouselChange = (newIndex) => {
  console.log('设置面板轮播图切换到:', newIndex)
  
  // 设置用户操作标志，防止循环更新
  isUserSelecting.value = true
  
  // 关键修复：同步更新两个索引变量
  currentImageIndex.value = newIndex
  localSettings.value.currentImageIndex = newIndex
  
  // 立即发出更新事件，通知预览区域
  emit('update:content', {
    type: 'ecommerce-settings',
    isEcommerceSettings: true,
    currentData: {
      ...localSettings.value,
      currentImageIndex: newIndex
    }
  })
  
  // 延时重置用户选择标志
  setTimeout(() => {
    isUserSelecting.value = false
    console.log('设置面板 - 重置用户选择标志')
  }, 300)
}

// 媒体选择方法
const selectImageFile = () => {
  dialogMediaType.value = 'image';
  dialogVisible.value = true;
};

// 暂时注释掉视频选择功能
// const selectVideoFile = () => {
//   dialogMediaType.value = 'video';
//   dialogVisible.value = true;
// };

const handleMediaSelect = (mediaData) => {
  console.log('EcommerceSettings - 选择媒体文件:', mediaData);
  
  if (dialogMediaType.value === 'image') {
    // 更新当前选中的图片
    if (currentImageIndex.value < localSettings.value.images.length) {
      const mediaUrl = mediaData.mediaUrl || getMediaUrl(`/aim_files/${mediaData.appKey}/${mediaData.path}`);
      const relativePath = `/aim_files/${mediaData.appKey}/${mediaData.path}`;
      localSettings.value.images[currentImageIndex.value].src = relativePath;
      localSettings.value.images[currentImageIndex.value].alt = mediaData.mediaDesc || `图片${currentImageIndex.value + 1}`;
    }
  }
  // 暂时注释掉视频处理逻辑
  // else if (dialogMediaType.value === 'video') {
  //   // 对于视频，保存相对路径而不是完整URL
  //   const relativePath = `/aim_files/${mediaData.appKey}/${mediaData.path}`;
  //   localSettings.value.videoSrc = relativePath;
  //   console.log('EcommerceSettings - 设置视频源路径:', relativePath);
  // }
  
  handleSettingsChange();
  dialogVisible.value = false;
  ElMessage.success('媒体文件设置成功');
};

// 处理点击事件更新
const handleClickEventUpdate = (updatedContent) => {
  console.log('EcommerceSettings - 图片点击事件更新:', updatedContent);
  
  // 直接使用新格式保存点击事件，而不是转换为老格式
  const newClickEvent = {
    actionType: updatedContent.actionType || 'OPEN_BROWSER',
    actionUrl: updatedContent.actionUrl || '',
    actionPath: updatedContent.actionPath || '',
    packageName: updatedContent.packageName || '',
    floorType: updatedContent.floorType || '0',
    emailAddress: updatedContent.emailAddress || '',
    emailSubject: updatedContent.emailSubject || '',
    emailBody: updatedContent.emailBody || '',
    scheduleTitle: updatedContent.scheduleTitle || '',
    scheduleContent: updatedContent.scheduleContent || '',
    scheduleStartTimeString: updatedContent.scheduleStartTimeString || '',
    scheduleEndTimeString: updatedContent.scheduleEndTimeString || '',
    popupTitle: updatedContent.popupTitle || '',
    popupContent: updatedContent.popupContent || '',
    popupButtonText: updatedContent.popupButtonText || '',
    copyType: updatedContent.copyType || '1',
    selectedParamId: updatedContent.selectedParamId || '',
    fixedContent: updatedContent.fixedContent || ''
  };
  
  // 更新当前图片的点击事件
  localSettings.value.images[currentImageIndex.value] = {
    ...localSettings.value.images[currentImageIndex.value],
    clickEvent: newClickEvent
  };
  
  console.log('EcommerceSettings - 更新后的图片点击事件:', newClickEvent);
  
  // 直接发出更新事件，不调用 handleSettingsChange
  // 这样避免触发重新初始化，保持轮播图索引不变
  console.log('EcommerceSettings - 点击事件更新，保持轮播图索引:', currentImageIndex.value);
  emit('update:content', {
    type: 'ecommerce-settings',
    isEcommerceSettings: true,
    currentData: { 
      ...localSettings.value,
      currentImageIndex: currentImageIndex.value // 明确保持当前索引
    }
  });
};

// 处理参数插入
const handleParamInsert = (paramInfo) => {
  emit('insert-param', paramInfo);
};

// 处理参数管理
const handleParamManage = () => {
  emit('manage-param');
};

// 按钮操作方法
const increaseButtonCount = () => {

  if (buttonCount.value < 2) {
    buttonCount.value++;
    
    // 显示第二个按钮（将hidden设置为false）
    if (localSettings.value.buttons.length >= 2) {
      localSettings.value.buttons[1].hidden = false;
      console.log('EcommerceSettings - 显示第二个按钮');
    }
    
    console.log('EcommerceSettings - 增加按钮后状态:', {
      新buttonCount: buttonCount.value,
      按钮状态: localSettings.value.buttons.map((btn, idx) => ({ 索引: idx, 隐藏: btn.hidden, 文本: btn.text }))
    });
    
    handleSettingsChange();
  }
};

const decreaseButtonCount = () => {

  
  if (buttonCount.value > 1) {
    buttonCount.value--;
    
    // 隐藏第二个按钮（将hidden设置为true）
    if (localSettings.value.buttons.length >= 2) {
      localSettings.value.buttons[1].hidden = true;
      console.log('EcommerceSettings - 隐藏第二个按钮');
    }
    
    // 如果当前选中的是第二个按钮，切换到第一个按钮
    if (currentButtonIndex.value >= buttonCount.value) {
      currentButtonIndex.value = 0;
      console.log('EcommerceSettings - 切换到第一个按钮');
    }
    
    handleSettingsChange();
  }
};

const selectButton = (visualIndex) => {
  console.log('EcommerceSettings - 选择按钮:', visualIndex);
  
  // 将视觉索引转换为实际按钮索引
  const visibleButtonsList = localSettings.value.buttons.filter((button, index) => {
    return index < buttonCount.value && !button.hidden;
  });
  
  if (visualIndex < visibleButtonsList.length) {
    // 找到对应的实际按钮索引
    const actualIndex = localSettings.value.buttons.findIndex(button => button === visibleButtonsList[visualIndex]);
    if (actualIndex !== -1) {
      currentButtonIndex.value = actualIndex;
      console.log('EcommerceSettings - 实际选中按钮索引:', actualIndex);
    }
  }
};

const handleButtonTextChange = () => {
  console.log('EcommerceSettings - 按钮文本变化:', currentButton.value.text);
  handleSettingsChange();
};

// 处理按钮点击事件更新
const handleButtonClickEventUpdate = (updatedContent) => {
  console.log('EcommerceSettings - 按钮点击事件更新:', updatedContent);
  
  // 直接使用新格式保存点击事件，而不是转换为老格式
  const newClickEvent = {
    actionType: updatedContent.actionType || 'OPEN_BROWSER',
    actionUrl: updatedContent.actionUrl || '',
    actionPath: updatedContent.actionPath || '',
    packageName: updatedContent.packageName || '',
    floorType: updatedContent.floorType || '0',
    emailAddress: updatedContent.emailAddress || '',
    emailSubject: updatedContent.emailSubject || '',
    emailBody: updatedContent.emailBody || '',
    scheduleTitle: updatedContent.scheduleTitle || '',
    scheduleContent: updatedContent.scheduleContent || '',
    scheduleStartTimeString: updatedContent.scheduleStartTimeString || '',
    scheduleEndTimeString: updatedContent.scheduleEndTimeString || '',
    popupTitle: updatedContent.popupTitle || '',
    popupContent: updatedContent.popupContent || '',
    popupButtonText: updatedContent.popupButtonText || '',
    copyType: updatedContent.copyType || '1',
    selectedParamId: updatedContent.selectedParamId || '',
    fixedContent: updatedContent.fixedContent || ''
  };
  
  console.log('EcommerceSettings - 转换后的按钮clickEvent:', newClickEvent);
  
  // 更新当前选中按钮的点击事件
  localSettings.value.buttons[currentButtonIndex.value] = {
    ...localSettings.value.buttons[currentButtonIndex.value],
    clickEvent: newClickEvent
  };
  
  handleSettingsChange();
};

// 监听内容变化
watch(() => props.content, (newVal, oldVal) => {
  console.log('EcommerceSettings - Props内容变化:', newVal, oldVal);
  
  // 检查是否是简单的点击事件更新
  if (newVal && newVal.currentData && oldVal && oldVal.currentData) {
    const isClickEventOnlyUpdate = 
      newVal.currentData.currentImageIndex === oldVal.currentData.currentImageIndex &&
      JSON.stringify({...newVal.currentData, images: undefined, buttons: undefined}) === 
      JSON.stringify({...oldVal.currentData, images: undefined, buttons: undefined});
    
    if (isClickEventOnlyUpdate) {
      console.log('EcommerceSettings - 检测到点击事件更新，仅同步图片数据');
      // 不要直接覆盖，而是合并保留当前最新的图片数据和按钮数据
      // 因为 newVal.currentData.images 和 buttons 可能是旧数据，会覆盖用户刚输入的内容
      console.log('EcommerceSettings - 跳过图片和按钮数据覆盖，保持当前状态');
      return; // 跳过完整的重新初始化
    }
  }
  
  if (newVal && newVal.isEcommerceSettings && newVal.currentData) {
    console.log('EcommerceSettings - 需要重新初始化设置');
    initializeSettings();
  }
}, { 
  immediate: true, 
  deep: true 
});

// 组件挂载时初始化
onMounted(() => {
  initializeSettings();
});

// 处理图片加载错误 - 与CarouselSettings保持一致
const handleImageError = (image, index) => {
  console.error(`EcommerceSettings - 图片加载错误: 索引 ${index}`, image);
};

// 处理图片加载成功 - 与CarouselSettings保持一致  
const handleImageLoad = (image, index) => {
  console.log(`EcommerceSettings - 图片加载成功: 索引 ${index}`, image);
};

// 处理货币符号显示变化
const handleCurrencyDisplayChange = () => {
  console.log('EcommerceSettings - 货币符号显示变化:', showCurrencySymbol.value);
  localSettings.value.aimCurrencyDisplay = showCurrencySymbol.value ? 1 : 0;
  handleSettingsChange();
};
</script>

<style scoped lang="scss">
.ecommerce-settings {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
  :deep(.setting-content){
    padding: 0;
  }
  :deep(.rich-param-input){
    padding:14px 16px;
  }
}
.style-options {
  display: flex;
  gap: 14px;
  flex-wrap: wrap;
}

.style-option .style-image{
  cursor: pointer;
  transition: all 0.2s ease;
}

.style-option.is-selected .style-image {
  border: 1px solid #1989fa;
}

.style-image {
  width: 101px;
  display: block;
}

.style-name {
  font-size: 14px;
  color: #666;
  text-align: center;
  display: block;
  margin-top: 10px;
}
.setting-card-price{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;

}
.is-show-price-title{
  color: #303133;
  font-weight: normal;
}

.setting-section {
  margin-bottom: 24px;
  .section-title{
    color: #303133;
    font-weight: normal;
    padding: 10px 16px;
    width: 100%;
    border-bottom: 1px solid #ebeef5;
    position: relative;
  }
}

.setting-card{
  border: 1px solid #ebeef5;
  background-color: #fff;
  transition: .3s;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);

  border-radius: 4px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.style-selector {
  margin-bottom: 16px;
}

/* 轮播图控制器样式 */
.carousel-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 0 16px;
  border-radius: 8px;
}

.control-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #dcdfe6;
  background-color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.control-btn:hover:not(:disabled) {
  border-color: #409eff;
  color: #409eff;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.number-buttons {
  display: flex;
  gap: 4px;
}

.number-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #dcdfe6;
  background-color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.number-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.number-btn.active {
  background-color: #409eff;
  border-color: #409eff;
  color: #ffffff;
}

/* 轮播图预览样式 */
.carousel-preview-container {
  margin-bottom: 20px;
}

.carousel-wrapper {
  position: relative;
  width: 300px;
  overflow: hidden;
  margin-bottom: 12px;
}

.carousel-image-wrapper {
  width: 300px;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
}

.carousel-image {
  width: 100%;
  height: 100%;
  display: block;
}
.setting-desc{
  margin: 10px 0;
}
.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
  width: 100%;
  height: 100%;
}

.no-image .el-icon {
  font-size: 48px;
  margin-bottom: 8px;
}

.no-image span {
  font-size: 14px;
}

.select-btn {
  width: auto;
}

/* 暂时注释掉视频设置样式 */
/*
.video-preview {
  margin-bottom: 12px;
}

.preview-video {
  width: 100%;
  height: 210px;
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 12px;
  position: relative;
  height: 210px;
  justify-content: center;
}

.video-default-image {
  width: 100%;
  height: 100%;
}

.video-placeholder-icon {
  width: 46px;
  height: 46px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  font-size: 20px;
  color: white;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.video-placeholder-icon:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

.video-icon {
  font-size: 48px;
  margin-bottom: 8px;
}
*/

/* 按钮设置样式 */
.button-count-control {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}
.button-name-title{
  color: #303133;
  font-weight: normal;
  padding: 10px 16px 0;
  width: 100%;
  span{
    color: #999;
  }

}
.button-click-event-setting{
  width: 100%;
  padding: 0px 16px;
}
.button-count-options {
  margin-top: 8px;
}

.button-selector {
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
}

.button-selection {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.button-selector-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #dcdfe6;
  background-color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.button-selector-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.button-selector-btn.active {
  background-color: #409eff;
  border-color: #409eff;
  color: #ffffff;
}

.setting-label {
  display: block;
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
}

/* Element Plus 样式覆盖 */
:deep(.el-carousel) {
  .el-carousel__item {
    display: flex;
    align-items: center;
  }
  
  .el-carousel__indicators {
    width: 100%;
    position: absolute !important;
    bottom: 0px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 10 !important;
  }
  
  .el-carousel__indicator {
    margin: 0 4px !important;
  }
  
  .el-carousel__button {
    width: 8px !important;
    height: 8px !important;
    border-radius: 50% !important;
    background-color: rgba(255, 255, 255, 0.6) !important;
    border: 1px solid rgba(255, 255, 255, 0.8) !important;
    opacity: 1 !important;
    transition: all 0.3s ease !important;
  }
  
  .el-carousel__indicator.is-active .el-carousel__button {
    background-color: #409eff !important;
    border-color: #409eff !important;
    transform: scale(1.2) !important;
  }
  
  .el-carousel__indicator:hover .el-carousel__button {
    background-color: rgba(255, 255, 255, 0.8) !important;
  }
  
  .el-carousel__arrow {
    background-color: rgba(0, 0, 0, 0.5) !important;
    color: #fff !important;
    border: none !important;
    width: 32px !important;
    height: 32px !important;
    border-radius: 50% !important;
  }
  
  .el-carousel__arrow:hover {
    background-color: rgba(0, 0, 0, 0.7) !important;
  }
  
  .el-carousel__arrow--left {
    left: 10px !important;
  }
  
  .el-carousel__arrow--right {
    right: 10px !important;
  }
}

:deep(.el-input__count) {
  font-size: 12px;
}

:deep(.el-textarea__inner) {
  resize: none;
}
</style> 