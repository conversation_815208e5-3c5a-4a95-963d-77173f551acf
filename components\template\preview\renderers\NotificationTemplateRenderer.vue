<template>
  <div class="notification-template" :class="{ 'enhanced': isEnhanced }" @click="handleTemplateClick">
    <!-- 标题 -->
    <div class="notification-title" v-if="titleContent">
      <TextElement 
        :content="titleContent"
        :isSelected="selectedContent && selectedContent.contentId === titleContent.contentId"
        :editable="editable"
        :usage="usage"
        @select="handleContentSelect"
        @update:content="handleContentUpdate"
      />
    </div>
    
    <!-- 描述 -->
    <div class="notification-desc" v-if="descriptionContent">
      <TextElement 
        :content="descriptionContent"
        :isSelected="selectedContent && selectedContent.contentId === descriptionContent.contentId"
        :editable="editable"
        :usage="usage"
        @select="handleContentSelect"
        @update:content="handleContentUpdate"
      />
    </div>
    
    <!-- 参数对 -->
    <div 
      v-if="visibleParamPairs && visibleParamPairs.length > 0" 
      class="notification-params"
    >
      <div 
        v-for="(pair, index) in visibleParamPairs" 
        :key="`param-pair-${index}`"
        class="param-pair"
      >
        <!-- 左侧参数 -->
        <div class="param-left" v-if="pair.left">
          <TextElement
            :content="pair.left"
            :is-selected="selectedContent?.contentId === pair.left.contentId"
            :editable="editable"
            :usage="usage"
            @select="handleContentSelect"
            @update:content="handleContentUpdate"
          />
        </div>
        <!-- 右侧参数 -->
        <div class="param-right" v-if="pair.right">
          <TextElement
            :content="pair.right"
            :is-selected="selectedContent?.contentId === pair.right.contentId"
            :editable="editable"
            :usage="usage"
            @select="handleContentSelect"
            @update:content="handleContentUpdate"
          />
        </div>
      </div>
    </div>
    
    <!-- 按钮区域 -->
    <div class="notification-buttons" v-if="visibleButtons.length > 0">
      <div 
        v-for="(button, index) in visibleButtons" 
        :key="button.contentId"
        class="button-wrapper"
        :class="{ 
          'single-button': visibleButtons.length === 1,
          'left-button': visibleButtons.length === 2 && index === 0,
          'right-button': visibleButtons.length === 2 && index === 1
        }"
      >
        <ButtonElement 
          :content="button"
          :isSelected="selectedContent && selectedContent.contentId === button.contentId"
          :editable="editable"
          @select="handleContentSelect"
          @update:content="handleContentUpdate"
        />
      </div>
    </div>
    
    <!-- 底部图片 -->
    <div class="notification-image" v-if="imageContent">
      <ImageElement 
        :content="imageContent"
        :isSelected="selectedContent && selectedContent.contentId === imageContent.contentId"
        :editable="editable"
        :get-media-url="getMediaUrl"
        @select="handleContentSelect"
        @update:content="handleContentUpdate"
      />
    </div>
  </div>
</template>

<script setup>
import { computed, inject, watch, onMounted, nextTick, onBeforeUnmount, reactive, provide } from 'vue';
import ImageElement from '../elements/ImageElement.vue';
import TextElement from '../elements/TextElement.vue';
import ButtonElement from '../elements/ButtonElement.vue';

const props = defineProps({
  contents: {
    type: Array,
    default: () => []
  },
  selectedContent: {
    type: Object,
    default: null
  },
  editable: {
    type: Boolean,
    default: false
  },
  usage: {
    type: String,
    default: 'editor'
  },
  getMediaUrl: {
    type: Function,
    default: (src) => src
  }
});

const emit = defineEmits(['select-content', 'update:content']);

// 注入设置数据，如果没有则使用默认值
const injectedSettings = inject('notificationSettings', null);

// 直接使用注入的设置，如果没有则使用默认值
const notificationSettings = computed(() => {
  if (injectedSettings) {
    // 在模板创建页面中，injectedSettings是一个reactive对象
    // 在模板列表页面中，injectedSettings是一个computed对象
    const settings = injectedSettings.value || injectedSettings;
    return {
      maxVisibleParams: settings.maxVisibleParams || 2,
      maxVisibleButtons: settings.maxVisibleButtons || 2
    };
  }
  return {
    maxVisibleParams: 2,
    maxVisibleButtons: 2
  };
});

// 监听注入的设置变化
watch(() => injectedSettings, (newSettings) => {
  if (newSettings) {
    const settings = newSettings.value || newSettings;
    console.log('NotificationTemplateRenderer - 更新注入的设置:', settings);
  }
}, { deep: true, immediate: true });

provide('notificationSettings', notificationSettings);

// 注入当前模板信息
const currentTemplate = inject('currentTemplate', null);

// 判断是否为增强通知类
const isEnhanced = computed(() => {
  // 只通过模板名称判断，避免一般通知类因为参数对数量增加而被误判
  return currentTemplate?.value?.templateName?.includes('增强') || false;
});

// 计算属性：标题内容
const titleContent = computed(() => {
  console.log('NotificationTemplateRenderer - 计算标题内容，原始contents:', props.contents);
  
  // 根据API数据结构，标题是 isTextTitle === 1 的内容
  const title = props.contents?.find(item => item.isTextTitle === 1);
  
  if (!title) return null;
  
  // 确保标题内容正确显示
  const processedTitle = {
    ...title,
    visible: title.visible !== false
  };
  
  console.log('NotificationTemplateRenderer - 处理后的标题:', processedTitle);
  return processedTitle;
});

// 计算属性：描述内容
const descriptionContent = computed(() => {
  
  // 根据API数据结构，描述是 isTextTitle === 0 且 positionNumber === 2 的内容
  const description = props.contents?.find(item => 
    item.isTextTitle === 0 && 
    item.positionNumber === 2 && 
    item.type === 'text'
  );
  
  if (!description) return null;
  
  // 确保描述内容正确显示
  const processedDescription = {
    ...description,
    visible: description.visible !== false
  };
  
  console.log('NotificationTemplateRenderer - 处理后的描述:', processedDescription);
  return processedDescription;
});

// 计算属性：参数对内容
const paramPairContents = computed(() => {
  
  // 根据API数据结构，参数对是 positionNumber > 2 的文本内容
  const paramPairs = props.contents?.filter(item => 
    item.type === 'text' && 
    item.positionNumber > 2
  ) || [];
  
  // 按 positionNumber 排序
  const sortedParamPairs = paramPairs.sort((a, b) => a.positionNumber - b.positionNumber);
  
  // 将参数对按奇偶数分组（奇数为左侧，偶数为右侧）
  const groupedPairs = [];
  for (let i = 0; i < sortedParamPairs.length; i += 2) {
    const leftParam = sortedParamPairs[i];
    const rightParam = sortedParamPairs[i + 1];
    
    if (leftParam) {
      groupedPairs.push({
        left: leftParam,
        right: rightParam || null,
        contentId: `param-pair-${leftParam.positionNumber}`,
        visible: true
      });
    }
  }
  
  console.log('NotificationTemplateRenderer - 处理后的参数对:', groupedPairs);
  return groupedPairs;
});

// 可见的参数对（根据设置限制数量）
const visibleParamPairs = computed(() => {
  if (!paramPairContents.value.length) return [];
  
  // 获取最大可见参数对数量
  const maxVisible = notificationSettings.value?.maxVisibleParams || 2;
  
  // 判断是否为增强通知类
  const isEnhanced = currentTemplate?.value?.templateName?.includes('增强') || false;
  
  if (isEnhanced) {
    // 增强通知类：第一对参数固定显示，其他根据设置显示
    // maxVisibleParams 表示除了第一对之外的可配置参数对数量
    const totalVisible = 1 + maxVisible; // 第一对固定 + 可配置的对数
    const result = paramPairContents.value.slice(0, Math.min(totalVisible, paramPairContents.value.length));
    return result;
  } else {
    // 一般通知类：所有参数对都根据设置显示
    const result = paramPairContents.value.slice(0, Math.min(maxVisible, paramPairContents.value.length));
    return result;
  }
});

// 动态生成按钮
const buttonContents = computed(() => {
  // 从实际内容中获取按钮
  const buttons = props.contents.filter(c => c.type === 'button');
  return buttons.sort((a, b) => (a.positionNumber || 0) - (b.positionNumber || 0));
});

// 可见的按钮（根据设置限制数量）
const visibleButtons = computed(() => {
  const maxButtons = notificationSettings.value.maxVisibleButtons || 2;
  return buttonContents.value.slice(0, maxButtons);
});

// 获取图片内容
const imageContent = computed(() => {
  const existing = props.contents?.find(c => c.type === 'image');
  return existing;
});

// 处理内容选择
const handleContentSelect = (content) => {
  emit('select-content', content);
};

// 处理内容更新
const handleContentUpdate = (newContent) => {
  emit('update:content', newContent);
};

// 处理模板点击事件
const handleTemplateClick = (event) => {
  // 检查点击的目标是否是内容元素
  const isContentElement = event.target.closest('[contenteditable="true"]') || 
                          event.target.closest('.preview-title') ||
                          event.target.closest('.preview-desc') ||
                          event.target.closest('.preview-button') ||
                          event.target.closest('.preview-image') ||
                          event.target.closest('.param-input') ||
                          event.target.closest('.j-btn');
  
  // 如果点击的是内容元素，阻止事件冒泡
  if (isContentElement) {
    console.log('NotificationTemplateRenderer: 点击了内容元素，阻止冒泡');
    event.stopPropagation();
  } else {
    // 如果点击的不是内容元素，让事件继续冒泡到父组件
    console.log('NotificationTemplateRenderer: 点击了空白区域，事件将冒泡');
  }
};

// 监听通知设置变化
watch(() => notificationSettings, (newSettings) => {
  console.log('NotificationTemplateRenderer: 通知设置变化', {
    最大可见参数对: newSettings?.maxVisibleParams
  });
}, { deep: true, immediate: true });

// 添加事件监听器来响应设置变化
onMounted(() => {
  // 监听通知设置变化事件
  if (window.PARAM_EVENT_BUS) {
    window.PARAM_EVENT_BUS.on('notification-settings-changed', (settings) => {
      
      // 强制触发重新计算
      nextTick(() => {
        // 触发响应式更新
        if (notificationSettings) {
          Object.assign(notificationSettings, settings);
        }
      });
    });
  }
  
  // 监听自定义事件
  const handleSettingsUpdate = (event) => {
    // 强制重新计算可见项目
    nextTick(() => {
      // 触发计算属性重新计算
      const currentVisible = visibleParamPairs.value?.length || 0;
      console.log('当前可见参数对数量:', currentVisible);
    });
  };
  
  document.addEventListener('template-settings-updated', handleSettingsUpdate);
  
  // 清理事件监听器
  onBeforeUnmount(() => {
    if (window.PARAM_EVENT_BUS) {
      window.PARAM_EVENT_BUS.off('notification-settings-changed');
    }
    document.removeEventListener('template-settings-updated', handleSettingsUpdate);
  });
});
</script>

<style scoped lang="scss">
.notification-template {
  width: 100%;
  min-height: 364px;
  position: relative;
  padding: 20px 16px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  .editable {
    margin: 0;
  }
  .notification-title {
    margin-bottom: 12px;
    
    :deep(.preview-title) {
      font-size: 18px;
      font-weight: 600;
      color: #1a1a1a;
      line-height: 1.3;
      margin: 0;
    }
  }
  
  .notification-desc {
    margin-bottom: 20px;
    
    :deep(.preview-desc) {
      font-size: 14px;
      color: #666;
      margin: 0;
      text-indent: 90px;
      height: 22px;
      line-height: 22px;
      &:before {
        content: "【国泰海通】";
        color: #ff9500;
        margin-right: 8px;
        font-weight: 500;
      }
    }
  }
  
  .notification-params {
    padding: 0 0 16px;
    
    .param-pair {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;
      
      &:last-child {
        border-bottom: none;
        padding-bottom: 0;
      }
      
      .param-left {
        width: 80px;
        margin-right: 10px;
        
        :deep(.preview-desc) {
          font-size: 16px;
          color: #606060;
          text-indent: 0;
          height: auto;
          margin: 0;
          font-weight: 500;
          height: 24px;
          line-height: 24px;
          overflow: auto;
          &:before {
            display: none;
          }
        }
      }
      
      .param-right {
        flex: 1;
        
        :deep(.preview-desc) {
          font-size: 16px;
          color: #222;
          text-indent: 0;
          height: auto;
          margin: 0;
          font-weight: 500;
          height: 24px;
          line-height: 24px;
          overflow: auto;
          &:before {
            display: none;
          }
        }
      }
    }
  }
  
  .notification-buttons {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    
    .button-wrapper {
      flex: 1;
      .preview-button{
        margin: 5px auto;
      }
      :deep(.button-element){
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
      }
    }
    :deep(.left-button .button-element){
      background: #e5e5e5;
      color: #2878ff;
    }
  }
  
  .notification-image {
    margin-top: 20px;
    
    :deep(.preview-image) {
      width: 100%;
      border-radius: 8px;
      overflow: hidden;
      
      img {
        width: 100%;
        height: 102px;
        display: block;
        object-fit: initial;
      }
    }
  }
  
  // 增强通知类的特殊样式
  &.enhanced {
    .notification-desc{
      margin-bottom: 0px;
    }
    .notification-params {
      .param-pair {
        
        // 第一对参数的特殊样式
        &:first-child {
          margin-bottom: 10px;
          .param-left {
            width: 128px;
            margin-right: 5px;
            :deep(.preview-title) {
              font-size: 22px;
              height: 34px;
              line-height: 34px;
            }
          }
          
          .param-right {
            :deep(.preview-desc) {
              color: #606060;
              font-size: 14px;
              margin-top: 7px;
              width: 100px;
            }
          }
        }
        
        // 其他参数对保持原样
        &:not(:first-child) {
          .param-left,
          .param-right {
            :deep(.preview-desc) {
              font-size: 16px;
              font-weight: 500;
            }
          }
        }
      }
    }
  }
}
</style> 