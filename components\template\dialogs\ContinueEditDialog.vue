<template>
  <el-dialog
    v-model="dialogVisible"
    title="提示"
    width="28%"
    :show-close="true"
    align-center
    class="continue-edit-dialog"
  >
    <div class="continue-dialog-content">
      <el-icon class="warning-icon"><InfoFilled /></el-icon>
      <span class="warning-text">检测到您上次有未完成的编辑内容，是否继续编辑?</span>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleRestart">重新开始</el-button>
        <el-button type="primary" @click="handleContinue">继续编辑</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue';
import { InfoFilled } from '@element-plus/icons-vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'continue', 'restart']);

// 计算属性：对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 处理继续编辑按钮点击
const handleContinue = () => {
  emit('continue');
  dialogVisible.value = false;
};

// 处理重新开始按钮点击
const handleRestart = () => {
  emit('restart');
  dialogVisible.value = false;
};
</script>

<style scoped lang="scss">
.continue-dialog-content {
  display: flex;
  align-items: center;
  padding: 20px 0;
}

.warning-icon {
  font-size: 24px;
  color: #409eff;
  margin-right: 15px;
}

.warning-text {
  font-size: 14px;
  line-height: 1.5;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style> 