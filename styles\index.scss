@use "./reset";

.app-container {
  padding: 15px;
}

.search-bar {
  padding: 18px 0 0 10px;
  margin-bottom: 10px;
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
}

.table-container > .el-card__header {
  padding: calc(var(--el-card-padding) - 8px) var(--el-card-padding);
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32 160 255);
  }
}

/*  -- flex弹性布局 -- */
.flex {
	display: flex;
}
.flex-wrap {
	flex-wrap: wrap;
}
.justify-end {
  justify-content: flex-end;
}
.flex-unset{
  align-items: unset;
}
.flex-column-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.flex-column-between {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.flex-column-start {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.flex-column-around {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}

.flex-row-start {
  display: flex;
  align-items: center;
}

.flex-row-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex-row-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-row-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.text-hidden{
  white-space: pre;
  overflow: hidden;
  text-overflow: ellipsis;
}
