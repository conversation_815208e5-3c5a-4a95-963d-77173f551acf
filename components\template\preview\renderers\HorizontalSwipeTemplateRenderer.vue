<template>
  <div class="horizontal-swipe-template-renderer">
    <HorizontalSwipeElement
      :pages="groupedPages"
      :current-page-index="currentPageIndex"
      :usage="usage"
      :editable="editable"
      :get-media-url="getMediaUrl"
      :selected-content="selectedContent"
      @page-change="handlePageChange"
      @select="handleContentSelect"
    />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import HorizontalSwipeElement from '../elements/HorizontalSwipeElement.vue';

const props = defineProps({
  contents: {
    type: Array,
    required: true
  },
  selectedContent: {
    type: Object,
    default: null
  },
  editable: {
    type: Boolean,
    default: true
  },
  usage: {
    type: String,
    default: 'editor'
  },
  getMediaUrl: {
    type: Function,
    default: (src) => src
  }
});

const emit = defineEmits(['select-content', 'page-change']);

// 当前页面索引
const currentPageIndex = ref(0);

// 将内容按页面分组
const groupedPages = computed(() => {
  if (!props.contents || props.contents.length === 0) {
  // HorizontalSwipeTemplateRenderer - 没有内容，返回空数组
    return [];
  }
  
  // 按pageIndex分组
  const pageGroups = {};
  
  props.contents.forEach((content, index) => {
    // 优先使用pageIndex，如果没有则使用pageId，最后使用计算值
    let pageIndex = content.pageIndex;
    if (pageIndex === undefined || pageIndex === null) {
      if (content.pageId) {
        pageIndex = content.pageId - 1; // pageId通常从1开始，需要转换为从0开始的索引
      } else {
        pageIndex = Math.floor(index / 4); // 每4个内容为一页
      }
    }
    
    
    if (!pageGroups[pageIndex]) {
      pageGroups[pageIndex] = [];
    }
    
    pageGroups[pageIndex].push(content);
  });
  
  
  // 将分组转换为数组格式，确保按页面索引排序
  const pages = Object.keys(pageGroups)
    .map(key => parseInt(key))
    .sort((a, b) => a - b)
    .map(pageIndex => ({
      pageIndex,
      contents: pageGroups[pageIndex]
    }));
  
  console.log('HorizontalSwipeTemplateRenderer - 最终页面数组:', pages);
  console.log('HorizontalSwipeTemplateRenderer - 页面数量:', pages.length);
  
  return pages;
});

// 处理页面切换
const handlePageChange = (pageIndex) => {
  currentPageIndex.value = pageIndex;
  emit('page-change', pageIndex);
};

// 处理内容选择
const handleContentSelect = (content) => {
  emit('select-content', content);
};
</script>

<style scoped>
.horizontal-swipe-template-renderer {
  width: 100%;
  height: 100%;
}

</style> 