/**
 * 将文本复制到剪贴板
 * @param {string} text - 要复制的文本内容
 * @returns {Promise<void>} - 复制成功或失败的 Promise
 */
export const copyTextToClipboard = (text) => {
  return new Promise((resolve, reject) => {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(text)
        .then(() => {
          resolve();
        })
        .catch((err) => {
          reject(new Error('复制到剪贴板失败: ' + err.message));
        });
    } else {
      const textarea = document.createElement('textarea');
      textarea.value = text;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      document.body.appendChild(textarea);
      textarea.select();
      try {
        const successful = document.execCommand('copy');
        if (successful) {
          resolve();
        } else {
          reject(new Error('复制到剪贴板失败'));
        }
      } catch (err) {
        reject(new Error('复制到剪贴板失败: ' + err.message));
      } finally {
        document.body.removeChild(textarea);
      }
    }
  });
};