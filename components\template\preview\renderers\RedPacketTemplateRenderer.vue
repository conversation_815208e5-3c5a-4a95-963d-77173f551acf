<template>
  <div class="redpacket-template" @click="handleTemplateClick">
    <!-- 红包背景 -->
    <template v-for="(content, index) in contents" :key="content.contentId || index">
      <ImageElement 
        v-if="content.type === 'background'"
        :content="content"
        :isSelected="selectedContent && selectedContent.contentId === content.contentId"
        :editable="editable"
        :get-media-url="getMediaUrl"
        @select="handleContentSelect"
        @update:content="handleContentUpdate"
        @click.stop
      />
    </template>
    
    <div class="redpacket-content" @click="handleContentClick">
      <template v-for="(content, index) in contents" :key="content.contentId || index">
        <!-- 头像图片 -->
        <ImageElement 
          v-if="content.type === 'image'"
          :content="content"
          :isSelected="selectedContent && selectedContent.contentId === content.contentId"
          :editable="editable"
          :get-media-url="getMediaUrl"
          :custom-class="'red-packet-avatar'"
          @select="handleContentSelect"
          @update:content="handleContentUpdate"
          @click.stop
        />
        
        <!-- 红包名称 -->
        <TextElement 
          v-if="content.type === 'text' && content.positionNumber === 2"
          :content="content"
          :isSelected="selectedContent && selectedContent.contentId === content.contentId"
          :editable="editable"
          :usage="usage"
          :custom-class="'red-packet-name'"
          @select="handleContentSelect"
          @update:content="handleContentUpdate"
          @click.stop
        />

        <!-- 红包标题 -->
        <TextElement 
          v-else-if="content.type === 'text' && content.isTextTitle === 1"
          :content="content"
          :isSelected="selectedContent && selectedContent.contentId === content.contentId"
          :editable="editable"
          :usage="usage"
          :custom-class="'red-packet-title'"
          @select="handleContentSelect"
          @update:content="handleContentUpdate"
          @click.stop
        />

        <!-- 红包描述 -->
        <TextElement 
          v-else-if="content.type === 'text' && content.isTextTitle === 0 && content.positionNumber !== 2"
          :content="content"
          :isSelected="selectedContent && selectedContent.contentId === content.contentId"
          :editable="editable"
          :usage="usage"
          :custom-class="['red-packet-desc', shouldAddFocusClass(content) ? 'empty-focused' : '']"
          @select="handleContentSelect"
          @update:content="handleContentUpdate"
          @input="handleInput(content)"
          @focus="handleFocus(content)"
          @blur="handleBlur(content)"
          @click.stop
        />
        
        <!-- 按钮 -->
        <ButtonElement 
          v-else-if="content.type === 'button'"
          :content="content"
          :isSelected="selectedContent && selectedContent.contentId === content.contentId"
          :editable="editable"
          @select="handleContentSelect"
          @update:content="handleContentUpdate"
          @click.stop
        />
      </template>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref } from 'vue';
import ImageElement from '../elements/ImageElement.vue';
import TextElement from '../elements/TextElement.vue';
import ButtonElement from '../elements/ButtonElement.vue';

const props = defineProps({
  contents: {
    type: Array,
    default: () => []
  },
  selectedContent: {
    type: Object,
    default: null
  },
  editable: {
    type: Boolean,
    default: false
  },
  getMediaUrl: {
    type: Function,
    default: (src) => src
  },
  usage: {
    type: String,
    default: 'editor'
  }
});

const emit = defineEmits(['select-content', 'update:content']);

// 存储处于焦点且内容为空的元素
const focusedEmptyContents = ref(new Set());
// 简化的拖拽状态追踪
const isDragging = ref(false);

// 设置红包模板全局标记
onMounted(() => {
  window.TEMPLATE_IS_REDPACKET = true;
  
  // 只添加必要的事件监听来防止拖拽时的异常行为
  document.addEventListener('selectstart', handleSelectStart);
});

// 清除红包模板全局标记
onBeforeUnmount(() => {
  window.TEMPLATE_IS_REDPACKET = false;
  
  // 移除事件监听
  document.removeEventListener('selectstart', handleSelectStart);
});

// 防止文本选择事件 - 简化版本
const handleSelectStart = (event) => {
  // 添加event和event.target的空值检查
  if (!event || !event.target || typeof event.target.closest !== 'function') {
    return;
  }
  
  // 如果不是在可编辑元素内，阻止文本选择（这样就能防止拖拽时的文本选择）
  const editableElement = event.target.closest('[contenteditable="true"]');
  if (!editableElement) {
    event.preventDefault();
    return false;
  }
};

// 处理内容选择
const handleContentSelect = (content) => {
  console.log('红包模板选择内容:', content);
  emit('select-content', content);
};

// 处理内容更新
const handleContentUpdate = (content) => {
  console.log('红包模板更新内容:', content);
  
  // 检查更新后的内容是否不为空，如果不为空则立即移除empty-focused类名
  // 改进：更宽松的内容检查，包括拼音输入状态
  if (content && content.content && !isStrictlyEmpty(content.content)) {
    focusedEmptyContents.value.delete(content.contentId);
    console.log('RedPacketTemplateRenderer - 用户输入内容，立即移除empty-focused类名');
  }
  
  emit('update:content', content);
};

// 处理模板点击（空白区域）
const handleTemplateClick = (event) => {
  console.log('RedPacketTemplateRenderer: 处理模板点击', {
    target: event.target.className || event.target.tagName,
    editable: props.editable,
    isCurrentTarget: event.target === event.currentTarget,
    isDragging: isDragging.value
  });
  
  if (!props.editable) return;

  // 如果是拖拽操作，不处理点击
  if (isDragging.value) {
    console.log('RedPacketTemplateRenderer: 检测到拖拽操作，忽略点击处理');
    event.preventDefault();
    event.stopPropagation();
    return;
  }

  // 只有直接点击在红包模板容器本身才处理（真正的外部空白区域）
  if (event.target === event.currentTarget) {
    console.log('RedPacketTemplateRenderer: 点击了模板外部空白区域，清除选中内容');
    
    // 让当前有焦点的元素失去焦点
    if (document.activeElement && document.activeElement.blur) {
      console.log('RedPacketTemplateRenderer: 让当前有焦点的元素失去焦点:', document.activeElement);
      document.activeElement.blur();
    }
    
    event.preventDefault();
    event.stopPropagation();
    emit('select-content', null);
  } else {
    console.log('RedPacketTemplateRenderer: 点击了模板内部区域，阻止冒泡');
    // 阻止事件冒泡到TemplateEditor，避免触发其点击处理
    event.stopPropagation();
  }
};

// 处理红包内容区域点击
const handleContentClick = (event) => {
  console.log('RedPacketTemplateRenderer: 处理红包内容区域点击', {
    target: event.target.className || event.target.tagName,
    editable: props.editable,
    isCurrentTarget: event.target === event.currentTarget,
    isDragging: isDragging.value
  });
  
  if (!props.editable) return;

  // 如果是拖拽操作，不处理点击
  if (isDragging.value) {
    console.log('RedPacketTemplateRenderer: 检测到拖拽操作，忽略内容区域点击');
    event.preventDefault();
    event.stopPropagation();
    return;
  }

  // 检查点击的目标是否是内容元素或其子元素
  const isContentElement = event.target.closest('[contenteditable="true"]') || 
                          event.target.closest('.preview-title') ||
                          event.target.closest('.preview-desc') ||
                          event.target.closest('.preview-name') ||
                          event.target.closest('.red-packet-title') ||
                          event.target.closest('.red-packet-name') ||
                          event.target.closest('.red-packet-desc') ||
                          event.target.closest('.button-element') ||
                          event.target.closest('.preview-image') ||
                          event.target.closest('.param-input') ||
                          event.target.closest('.j-btn') ||
                          event.target.closest('.text-element') ||
                          event.target.closest('.image-element') ||
                          event.target.tagName === 'INPUT' ||
                          event.target.tagName === 'BUTTON' ||
                          event.target.tagName === 'IMG';
  
  console.log('RedPacketTemplateRenderer: 内容区域点击检查', {
    isContentElement: !!isContentElement,
    targetClass: event.target.className,
    targetTag: event.target.tagName,
    isCurrentTarget: event.target === event.currentTarget
  });
  
  // 如果点击的是内容元素，让事件正常传播
  if (isContentElement) {
    console.log('RedPacketTemplateRenderer: 点击了内容元素，允许事件传播');
    return;
  }
  
  // 如果直接点击在红包内容区域的空白处，清除选中内容并让当前元素失去焦点
  if (event.target === event.currentTarget) {
    console.log('RedPacketTemplateRenderer: 点击了红包内容区域空白处，清除选中内容');
    
    // 让当前有焦点的元素失去焦点
    if (document.activeElement && document.activeElement.blur) {
      console.log('RedPacketTemplateRenderer: 让当前有焦点的元素失去焦点:', document.activeElement);
      document.activeElement.blur();
    }
    
    event.preventDefault();
    event.stopPropagation();
    emit('select-content', null);
  } else {
    console.log('RedPacketTemplateRenderer: 点击了红包内容区域的其他元素，阻止冒泡');
    event.stopPropagation();
  }
};

// 判断是否应该添加焦点类名
const shouldAddFocusClass = (content) => {
  const contentId = content.contentId || content;
  const isFocused = focusedEmptyContents.value.has(contentId);
  const result = isFocused;
  
  console.log('RedPacketTemplateRenderer - shouldAddFocusClass:', result, 'for content:', contentId);
  return result;
};

// 处理聚焦事件
const handleFocus = (content) => {
  console.log('RedPacketTemplateRenderer - 文本获得焦点:', content);
  
  // 检查内容是否为空
  const isEmpty = isContentEmpty(content.content);
  console.log('RedPacketTemplateRenderer - 内容是否为空:', isEmpty, '内容:', content.content);
  
  if (isEmpty) {
    focusedEmptyContents.value.add(content.contentId);
    console.log('RedPacketTemplateRenderer - 添加到空焦点集合，当前集合大小:', focusedEmptyContents.value.size);
  }
  
  // 选中内容
  emit('select-content', content);
};

// 处理失焦事件
const handleBlur = (content) => {
  console.log('RedPacketTemplateRenderer - 文本失去焦点:', content);
  
  // 立即检查内容是否仍为空
  const isEmpty = isContentEmpty(content.content);
  
  if (!isEmpty) {
    // 内容不为空，从空焦点集合中移除
    focusedEmptyContents.value.delete(content.contentId);
    console.log('RedPacketTemplateRenderer - 内容不为空，从空焦点集合移除，当前集合大小:', focusedEmptyContents.value.size);
  } else {
    // 内容仍为空，保持在空焦点集合中
    console.log('RedPacketTemplateRenderer - 内容仍为空，保持在空焦点集合中');
  }
};

// 处理输入事件 - 新增：专门处理拼音输入等实时输入状态
const handleInput = (content) => {
  console.log('RedPacketTemplateRenderer - 输入事件触发:', content);
  
  // 在任何输入发生时，立即移除empty-focused类名
  // 这样可以处理拼音输入、复制粘贴等各种输入场景
  if (content && content.contentId) {
    focusedEmptyContents.value.delete(content.contentId);
    console.log('RedPacketTemplateRenderer - 检测到输入活动，立即移除empty-focused类名');
  }
};

// 统一的默认内容判断函数
const isDefaultContent = (content) => {
  if (!content || !content.content) return true;
  
  const contentText = content.content.trim();
  
  // 如果有原始内容，判断当前内容是否与原始内容相同
  if (content.originalContent) {
    return contentText === content.originalContent.trim();
  }
  
  // 检查是否为空或默认文本
  const defaultTexts = ['编辑文本', '编辑描述', '编辑标题', '红包名称'];
  return contentText === '' || 
         contentText === '<br>' || 
         contentText === '&nbsp;' ||
         defaultTexts.includes(contentText);
};

// 判断内容是否为空
const isContentEmpty = (content) => {
  if (!content) return true;
  const contentText = content.trim();
  return contentText === '' || 
         contentText === '<br>' || 
         contentText === '&nbsp;';
};

// 更严格的空值判断，只检查真正的空内容
const isStrictlyEmpty = (content) => {
  if (!content) return true;
  const contentText = content.trim();
  // 只有完全为空才返回true，任何内容（包括拼音、特殊字符等）都视为非空
  return contentText === '';
};
</script>

<style scoped lang="scss">
.redpacket-template {
  width: 100%;
  height: 460px;
  position: relative;
  overflow: hidden;
  
  // 禁用文本选择，参考普通图文模板
  user-select: none !important;
  
  // 确保子元素也禁用文本选择
  * {
    user-select: none !important;
  }
  
  // 可编辑元素恢复文本选择
  [contenteditable="true"] {
    user-select: text !important;
  }
}

.redpacket-content {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 46px;
  width: 100%;
  height: 100%;
  
  .preview-button{
    width: 60%;
    position: absolute;
    bottom: 30px;
  }
  
  :deep(.button-element){
    font-size: 16px;
    font-family: 微软雅黑;
    font-style: normal;
    font-weight: normal;
    text-decoration: none;
    letter-spacing: 0px;
    color: rgb(255, 255, 255);
    text-align: center;
    background: linear-gradient(135deg, rgb(255, 255, 255) 0%, rgb(255, 173, 71) 40%, rgb(255, 173, 71) 40%, rgb(255, 141, 22) 100%);
    opacity: 1;
    box-sizing: border-box;
    border-style: none;
    border-radius: 32px;
    width: 100%;
  }
}

:deep(.preview-image.red-packet-avatar) {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  margin: -1px auto;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100% !important;
    // object-fit: cover;
  }
}

:deep(.red-packet-title) {
  color: #fff;
  font-size: 24px;
  margin: 30px 10px 0;
  height: 40px;
  width: 90%;
  overflow: auto;
  text-align: center;
}

:deep(.red-packet-name) {
  color: #fff;
  text-align: center;
  margin: 15px 10px;
  width: 90%;
  height: 24px;
  overflow: auto;
  font-weight: bold;
}

:deep(.red-packet-desc) {
  color: #fff;
  text-align: center;
  margin: 0 10px;
  text-indent: 0 !important;
  width: 90%;
  overflow: auto;
  font-weight: bold;
  
  &:before {
    position: static;
    transform: none;
  }
  // 空内容且聚焦时的特殊样式
  &.empty-focused:before {
    position: absolute;
    left: calc(50% - 74px - 4px);  // 原有的空内容定位
  }

}
</style> 
