/**
 * 电商模板处理器
 * 负责处理电商模板的特殊逻辑和内容初始化
 */
import { ActionJsonGenerator, ClickEventTypeConverter } from '@/utils/clickEventManager';
class EcommerceTemplate {
  /**
   * 初始化电商模板内容
   * @param {Object} template 模板数据
   * @returns {Array} 初始化后的内容数组
   */
  static initializeContents(template) {
    console.log('EcommerceTemplate - 初始化内容，模板数据:', template);
    
    if (!template) {
      console.log('EcommerceTemplate - 没有模板数据，返回空数组');
      return [];
    }

    // 检查模板是否有pages数据
    if (!template.pages) {
      console.log('EcommerceTemplate - 模板没有pages数据，使用默认内容');
      return this.getDefaultContents(template);
    }

    try {
      // 解析pages数据
      const pages = typeof template.pages === 'string' 
        ? JSON.parse(template.pages) 
        : template.pages;
      
      if (!pages || !Array.isArray(pages) || pages.length === 0) {
        console.log('EcommerceTemplate - pages数据为空，使用默认内容');
        return this.getDefaultContents(template);
      }

      const firstPage = pages[0];
      if (!firstPage || !firstPage.contents || !Array.isArray(firstPage.contents)) {
        console.log('EcommerceTemplate - 第一页没有内容数据，使用默认内容');
        return this.getDefaultContents(template);
      }

      console.log('EcommerceTemplate - 找到现有内容，进行处理:', firstPage.contents);
      // 直接处理现有内容，而不是返回默认内容
      return this.processExistingContents(firstPage.contents);
      
    } catch (error) {
      console.error('EcommerceTemplate - 解析模板数据失败，使用默认内容:', error);
      return this.getDefaultContents(template);
    }
  }

  /**
   * 处理现有内容，确保符合电商模板格式
   * @param {Array} contents 现有内容数组
   * @returns {Array} 处理后的内容数组
   */
  static processExistingContents(contents) {
    console.log('EcommerceTemplate - 处理现有内容:', contents);
    
    return contents.map((content, index) => {
      const processedContent = {
        ...content,
        id: content.contentId || content.id || `content-${index}`,
        isEcommerce: true
      };

      // 根据内容类型进行特定处理
      switch (content.type) {
        case 'image':
          processedContent.content = content.content || '该图片位建议选择比例为1：1（像素最小1088:1088）的图片，大小建议500KB以内';
          processedContent.src = content.src || '/aim_files/aim_defult/defaultImg48_65.jpg';
          break;
          
        case 'text':
          // 针对 com.hbm.ecImageAndText 模板的文本处理逻辑
          processedContent.content = content.content || '编辑文本';
          
          // 根据 positionNumber 和属性识别文本类型
          if (content.positionNumber === 2 && content.aimCurrencyDisplay === 1 && content.isTextTitle === 1) {
            // 价格文本
            processedContent.textType = 'price';
          } else if (content.positionNumber === 3 && content.isTextTitle === 1) {
            // 标签文本
            processedContent.textType = 'tag';
          } else if (content.positionNumber === 4 && content.isTextTitle === 1) {
            // 标题文本
            processedContent.textType = 'title';
          } else if (content.positionNumber === 5 && content.isTextTitle === 0) {
            // 描述文本
            processedContent.textType = 'description';
          }
          break;
          
        case 'button':
          processedContent.content = content.content || '编辑按钮';
          break;
      }

      return processedContent;
    });
  }

    // 从内容数组中提取电商数据
  static extractEcommerceDataFromContents = (contents) => {
    console.log('TemplateEditor - extractEcommerceDataFromContents 被调用，contents:', contents);
    
    if (!contents || !Array.isArray(contents)) {
      console.log('TemplateEditor - 没有有效的内容数组');
      return null;
    }
    
    const extractedData = {
      priceText: '',
      tagText: '',
      titleText: '',
      contentText: '',
      buttonText: '',
      aimCurrencyDisplay: 1,
      buttons: [],
      images: []
    };
    
    // 遍历内容数组提取数据
    contents.forEach((content, index) => {
      console.log(`TemplateEditor - 处理内容项 ${index}:`, {
        contentId: content.contentId,
        type: content.type,
        content: content.content,
        isTextTitle: content.isTextTitle,
        aimCurrencyDisplay: content.aimCurrencyDisplay,
        positionNumber: content.positionNumber
      });
      
      if (content.type === 'image') {
        // 处理图片数据
        const imageData = {
          src: content.src || '/aim_files/aim_defult/defaultImg48_65.jpg',
          alt: content.alt || `图片${index + 1}`,
          positionNumber: content.positionNumber || 1,
          clickEvent: content.clickEvent || this.getDefaultClickEvent()
        };
        
        // 如果是轮播图，提取轮播图数据
        if (content.carouselImages && Array.isArray(content.carouselImages)) {
          extractedData.images = content.carouselImages.map((img, imgIndex) => ({
            src: img.src || '/aim_files/aim_defult/defaultImg48_65.jpg',
            alt: img.alt || `图片${imgIndex + 1}`,
            content: img.content || content.content || '该图片位建议选择比例为1：1（像素最小1088:1088）的图片，大小建议500KB以内',
            positionNumber: imgIndex === 0 ? 1 : 7 + imgIndex, // 第一张为1，后续从8开始
            clickEvent: img.clickEvent || this.getDefaultClickEvent()
          }));
        } else {
          // 单张图片
          extractedData.images = [{
            ...imageData,
            content: content.content || '该图片位建议选择比例为1：1（像素最小1088:1088）的图片，大小建议500KB以内'
          }];
        }
        
        console.log(`TemplateEditor - 提取图片数据:`, extractedData.images);
      } else if (content.type === 'text') {
        // 根据 positionNumber 和其他标识符来判断文本类型
        if (content.positionNumber === 2 && content.aimCurrencyDisplay === 1 && content.isTextTitle === 1) {
          extractedData.priceText = content.content || '';
          extractedData.aimCurrencyDisplay = content.aimCurrencyDisplay;
          console.log(`TemplateEditor - 提取价格文本: "${extractedData.priceText}"`);
        } else if (content.positionNumber === 3 && content.isTextTitle === 1) {
          extractedData.tagText = content.content || '';
          console.log(`TemplateEditor - 提取标签文本: "${extractedData.tagText}"`);
        } else if (content.positionNumber === 4 && content.isTextTitle === 1) {
          extractedData.titleText = content.content || '';
          console.log(`TemplateEditor - 提取标题文本: "${extractedData.titleText}"`);
        } else if (content.positionNumber === 5 && content.isTextTitle === 0) {
          extractedData.contentText = content.content || '';
          console.log(`TemplateEditor - 提取内容文本: "${extractedData.contentText}"`);
        }
        // 兼容旧版本数据格式
        else if (content.contentId === 49 && !extractedData.priceText) {
          extractedData.priceText = content.content || '';
          if (typeof content.aimCurrencyDisplay === 'number') {
            extractedData.aimCurrencyDisplay = content.aimCurrencyDisplay;
          }
        } else if (content.contentId === 174 && !extractedData.tagText) {
          extractedData.tagText = content.content || '';
        } else if (content.contentId === 50 && !extractedData.titleText) {
          extractedData.titleText = content.content || '';
        } else if (content.contentId === 51 && !extractedData.contentText) {
          extractedData.contentText = content.content || '';
        }
      } else if (content.type === 'button') {
        // 处理按钮数据
        if (content.contentId === 52 || content.contentId === 53 ||
            content.contentId === '52' || content.contentId === '53') {
          extractedData.buttons.push({
            text: content.content || '',
            positionNumber: content.positionNumber || 0,
            contentId: content.contentId,
            hidden: false,
            clickEvent: content.clickEvent || this.getDefaultClickEvent()
          });
          
          // 保持向后兼容性，设置第一个按钮文本
          if (!extractedData.buttonText) {
            extractedData.buttonText = content.content || '';
          }
          
          console.log(`TemplateEditor - 提取按钮数据: "${content.content}"`);
        }
      }
    });
    
    // 按 positionNumber 排序按钮
    extractedData.buttons.sort((a, b) => (a.positionNumber || 0) - (b.positionNumber || 0));
    
    console.log('TemplateEditor - 数据提取完成:', extractedData);
    return extractedData;
  };


  /**
   * 获取默认内容（从API数据中动态获取）
   * @param {Object} template 模板数据，包含API返回的pages信息
   * @returns {Array} 默认内容数组
   */
  static getDefaultContents(template = null) {
    console.log('EcommerceTemplate - 获取默认内容，模板数据:', template);
    
    // 如果有模板数据，尝试从API中提取
    if (template && template.pages) {
      try {
        const pages = typeof template.pages === 'string' 
          ? JSON.parse(template.pages) 
          : template.pages;
        
        if (pages && pages[0] && pages[0].contents) {
          const apiContents = pages[0].contents;
          console.log('EcommerceTemplate - 从API提取内容:', apiContents);
          
          // 针对 com.hbm.ecImageAndText 模板的处理
          if (template.cardId === 'com.hbm.ecImageAndText') {
            return apiContents.map(content => {
              const processedContent = {
                contentId: content.contentId || `content-${content.positionNumber}`,
                type: content.type,
                positionNumber: content.positionNumber,
                pageLayout: content.pageLayout,
                pageLines: content.pageLines,
                actionJson: content.actionJson
              };
              
              switch (content.type) {
                case 'image':
                  processedContent.src = content.src || '/aim_files/aim_defult/defaultImg48_65.jpg';
                  processedContent.content = content.content || '该图片位建议选择比例为1：1（像素最小1088:1088）的图片，大小建议500KB以内';
                  processedContent.isEcommerceImage = true;
                  processedContent.clickEvent = this.getDefaultClickEvent();
                  break;
                  
                case 'text':
                  processedContent.content = content.content || '编辑文本';
                  processedContent.text = content.content || '编辑文本';
                  processedContent.isEcommerceText = true;
                  processedContent.isTextTitle = content.isTextTitle;
                  processedContent.aimCurrencyDisplay = content.aimCurrencyDisplay;
                  
                case 'button':
                  processedContent.content = content.content || '编辑按钮';
                  processedContent.text = content.content || '编辑按钮';
                  processedContent.isEcommerceButton = true;
                  processedContent.clickEvent = this.getDefaultClickEvent();
                  break;
              }
              
              return processedContent;
            });
          }
        }
      } catch (error) {
        console.error('EcommerceTemplate - 解析API模板数据失败:', error);
      }
    }
    
    console.log('EcommerceTemplate - 使用硬编码的后备默认内容');
    // 如果无法从API获取数据，返回后备的硬编码默认内容
    return [
      {
        contentId: 'ecommerce-image-1',
        type: 'image',
        src: '/aim_files/aim_defult/defaultImg48_65.jpg',
        content: '该图片位建议选择比例为1：1（像素最小1088:1088）的图片，大小建议500KB以内',
        positionNumber: 1,
        isEcommerceImage: true,
        carouselImages: [
          {
            src: '/aim_files/aim_defult/defaultImg48_65.jpg',
            alt: '商品图片1',
            clickEvent: this.getDefaultClickEvent()
          }
        ],
        currentImageIndex: 0,
        clickEvent: this.getDefaultClickEvent()
      },
      {
        contentId: 'ecommerce-price-1',
        type: 'text',
        text: '文本',
        content: '文本',
        positionNumber: 2,
        isEcommerceText: true,
        isPrice: true,
        isTextTitle: 1,
        aimCurrencyDisplay: 1,
        fontSize: 24,
        color: '#ff6600',
        fontWeight: 'bold'
      },
      {
        contentId: 'ecommerce-tag-1',
        type: 'text',
        text: '文本',
        content: '文本',
        positionNumber: 3,
        isEcommerceText: true,
        isTag: true,
        isTextTitle: 1,
        fontSize: 12,
        color: '#ffffff',
        backgroundColor: '#ff6600'
      },
      {
        contentId: 'ecommerce-title-1',
        type: 'text',
        text: '编辑标题，最多显示17个字',
        content: '编辑标题，最多显示17个字',
        positionNumber: 4,
        isEcommerceText: true,
        isTextTitle: 1,
        fontSize: 16,
        color: '#333333',
        fontWeight: '600'
      },
      {
        contentId: 'ecommerce-content-1',
        type: 'text',
        text: '编辑文本描述，最多显示69个字。编辑文本描述，最多显示69个字。编辑文本描述，最多显示69个字。',
        content: '编辑文本描述，最多显示69个字。编辑文本描述，最多显示69个字。编辑文本描述，最多显示69个字。',
        positionNumber: 5,
        isEcommerceText: true,
        isTextTitle: 0,
        fontSize: 14,
        color: '#666666'
      },
      {
        contentId: 'ecommerce-button-1',
        type: 'button',
        text: '编辑按钮',
        content: '编辑按钮',
        positionNumber: 6,
        isEcommerceButton: true,
        backgroundColor: '#ff6600',
        color: '#ffffff',
        borderRadius: 4,
        padding: '12px 24px',
        fontSize: 16,
        fontWeight: '600',
        clickEvent: this.getDefaultClickEvent()
      }
    ];
  }
  
   // 处理电商模板提交数据
  static processEcommerceTemplateData = (ecommerceSettingsState) => {
    // 兼容ref和普通对象
    if (ecommerceSettingsState && ecommerceSettingsState.value) {
      ecommerceSettingsState = ecommerceSettingsState.value;
    }
    if (!ecommerceSettingsState || !ecommerceSettingsState.images) {
      console.error('processEcommerceTemplateData: 参数无效', ecommerceSettingsState);
      return [];
    }
    console.log('电商设置状态:', ecommerceSettingsState);
    
    const processedContents = [];
    
    // 处理图片或视频内容
    if (ecommerceSettingsState.style === 'video') {
      // 视频模式：只生成视频内容，不需要点击事件
      processedContents.push({
        type: 'video',
        src: ecommerceSettingsState.videoSrc,
        positionNumber: 1,
      });
    } else if (ecommerceSettingsState.images && Array.isArray(ecommerceSettingsState.images)) {
      // 图片模式
      console.log('处理电商图片数据，数量:', ecommerceSettingsState.images.length);
      ecommerceSettingsState.images.forEach((image, index) => {
        // 修正电商模板图片的positionNumber分配：第一张为1，后续从8开始递增
        let correctPositionNumber;
        if (index === 0) {
          correctPositionNumber = 1; // 第一张图片
        } else {
          correctPositionNumber = 7 + index; // 第二张为8，第三张为9，依次递增
        }
        const imageContent = {
          type: 'image',
          src: image.src,
          positionNumber: correctPositionNumber,
          isTextTitle: 0
        };
        // 处理图片点击事件 - 参考普通轮播图的转换方式
        if (image.clickEvent && image.clickEvent.actionType !== 'NONE') {
          const clickEvent = image.clickEvent;
          
          // 确保actionType字段存在
          if (!clickEvent.actionType && clickEvent.type) {
            clickEvent.actionType = clickEvent.type;
          }
          
          // 使用统一的方法处理actionType
          imageContent.actionType = ClickEventTypeConverter.toActionType(clickEvent.actionType);
          // 使用统一的方法生成actionJson
          imageContent.actionJson = ActionJsonGenerator.fromClickEvent(clickEvent);
          console.log(`电商图片第${index + 1}张点击事件处理完成:`, {
            原始事件: clickEvent,
            处理后actionType: imageContent.actionType,
            处理后actionJson: imageContent.actionJson
          });
        } else {
          // 没有点击事件或事件类型为none
          imageContent.actionType = 'NONE';
          imageContent.actionJson = { target: '' };
        }
        processedContents.push(imageContent);
        console.log(`电商图片第${index + 1}张已添加到提交数据:`, imageContent);
      });
    }
    
    // 处理文本内容 - 确保aimCurrencyDisplay字段正确传递
    const textContents = [
      {
        type: 'text',
        content: ecommerceSettingsState.priceText || '',
        positionNumber: 2,
        isTextTitle: 1,
        aimCurrencyDisplay: ecommerceSettingsState.aimCurrencyDisplay ?? 1
      },
      {
        type: 'text',
        content: ecommerceSettingsState.tagText || '',
        positionNumber: 3,
        isTextTitle: 1
      },
      {
        type: 'text',
        content: ecommerceSettingsState.titleText || '',
        positionNumber: 4,
        isTextTitle: 1
      },
      {
        type: 'text',
        content: ecommerceSettingsState.contentText || '',
        positionNumber: 5,
        isTextTitle: 0
      }
    ];
    
    processedContents.push(...textContents);
    
    // 处理按钮内容 - 只提交非隐藏的按钮
    if (ecommerceSettingsState.buttons && Array.isArray(ecommerceSettingsState.buttons)) {
      console.log('处理电商按钮数据，总数量:', ecommerceSettingsState.buttons.length);

      // 过滤出非隐藏的按钮
      const visibleButtons = ecommerceSettingsState.buttons.filter(button => !button.hidden);
      console.log('过滤后的可见按钮数量:', visibleButtons.length);
      console.log('可见按钮详情:', visibleButtons.map((btn, idx) => ({
        索引: idx,
        文本: btn.text,
        positionNumber: btn.positionNumber,
        隐藏状态: btn.hidden
      })));

      visibleButtons.forEach((button, index) => {
        const buttonContent = {
          type: 'button',
          content: button.text || '',
          positionNumber: button.positionNumber || (6 + index),
          isTextTitle: 0
        };

        // 处理按钮点击事件 - 参考普通轮播图的转换方式
        if (button.clickEvent && button.clickEvent.actionType !== 'NONE') {
          const clickEvent = button.clickEvent;

          // 确保actionType字段存在
          if (!clickEvent.actionType && clickEvent.type) {
            clickEvent.actionType = clickEvent.type;
          }

          // 使用统一的方法处理actionType
          buttonContent.actionType = ClickEventTypeConverter.toActionType(clickEvent.actionType);

          // 使用统一的方法生成actionJson
          buttonContent.actionJson = ActionJsonGenerator.fromClickEvent(clickEvent);

          console.log(`电商可见按钮第${index + 1}个点击事件处理完成:`, {
            原始事件: clickEvent,
            处理后actionType: buttonContent.actionType,
            处理后actionJson: buttonContent.actionJson
          });
        } else {
          // 没有点击事件或事件类型为none
          buttonContent.actionType = 'NONE';
          buttonContent.actionJson = { target: '' };
        }

        processedContents.push(buttonContent);
        console.log(`电商可见按钮第${index + 1}个已添加到提交数据:`, buttonContent);
      });

      console.log(`电商按钮处理完成，实际提交按钮数量: ${visibleButtons.length}/${ecommerceSettingsState.buttons.length}`);
    }
    
    console.log('TemplateEditor - 电商模板数据处理完成:', processedContents);
    return processedContents;
  };


  /**
   * 初始化电商设置状态
   * @param {Object} extractedData 从模板内容中提取的数据
   * @returns {Object} 初始化后的电商设置状态
   */
  static initializeEcommerceSettingsState(extractedData) {
    console.log('EcommerceTemplate - 初始化电商设置状态，提取的数据:', extractedData);
    
    // 使用API数据初始化电商设置状态
    const ecommerceSettingsState = {
      style: 'image', // 默认使用图片模式
      images: extractedData.images && Array.isArray(extractedData.images) ? extractedData.images.map(img => ({
        ...img,
        content: img.content || '该图片位建议选择比例为1：1（像素最小1088:1088）的图片，大小建议500KB以内'
      })) : [
        {
          src: '/aim_files/aim_defult/defaultImg48_65.jpg',
          alt: '图片1',
          content: '该图片位建议选择比例为1：1（像素最小1088:1088）的图片，大小建议500KB以内',
          clickEvent: this.getDefaultClickEvent()
        }
      ],
      currentImageIndex: 0,
      priceText: extractedData.priceText || '',
      tagText: extractedData.tagText || '',
      titleText: extractedData.titleText || '',
      contentText: extractedData.contentText || '',
      buttonText: extractedData.buttonText || '',
      aimCurrencyDisplay: extractedData.aimCurrencyDisplay ?? 1,
      buttonCount: 2, // 默认显示2个按钮
      currentButtonIndex: 0,
      buttons: extractedData.buttons && Array.isArray(extractedData.buttons) ? extractedData.buttons : [
        {
          text: '编辑按钮',
          positionNumber: 6,
          hidden: false,
          clickEvent: this.getDefaultClickEvent()
        },
        {
          text: '编辑按钮',
          positionNumber: 7,
          hidden: false,
          clickEvent: this.getDefaultClickEvent()
        }
      ]
    };
    
    console.log('EcommerceTemplate - 电商设置状态初始化完成:', ecommerceSettingsState);
    return ecommerceSettingsState;
  }

  /**
   * 获取默认点击事件配置
   * @returns {Object} 默认点击事件
   */
  static getDefaultClickEvent() {
    return {
      type: 'browser',
      url: ''
    };
  }

  /**
   * 验证是否为有效的电商模板内容
   * @param {Array} contents 内容数组
   * @returns {boolean} 是否有效
   */
  static validateContents(contents) {
    if (!Array.isArray(contents)) {
      return false;
    }

    // 检查是否包含必要的电商元素
    const hasImage = contents.some(content => 
      content.type === 'image' || content.type === 'background'
    );
    
    const hasText = contents.some(content => 
      content.type === 'text'
    );
    
    const hasButton = contents.some(content => 
      content.type === 'button'
    );

    return hasImage && hasText && hasButton;
  }

  /**
   * 更新电商模板设置
   * @param {Array} contents 当前内容数组
   * @param {Object} settings 新的设置
   * @returns {Array} 更新后的内容数组
   */
  static updateSettings(contents, settings) {
    if (!Array.isArray(contents) || !settings) {
      return contents;
    }

    const updatedContents = [...contents];

    // 更新图片/视频设置
    if (settings.style === 'image' && settings.images) {
      const imageContentIndex = updatedContents.findIndex(content => 
        content.type === 'image' || content.type === 'background'
      );
      
      if (imageContentIndex !== -1) {
        updatedContents[imageContentIndex] = {
          ...updatedContents[imageContentIndex],
          carouselImages: settings.images,
          isCarousel: true,
          currentImageIndex: 0
        };
      }
    } else if (settings.style === 'video' && settings.videoSrc) {
      const videoContentIndex = updatedContents.findIndex(content => 
        content.type === 'video'
      );
      
      if (videoContentIndex !== -1) {
        updatedContents[videoContentIndex] = {
          ...updatedContents[videoContentIndex],
          src: settings.videoSrc
        };
      } else {
        // 添加新的视频内容
        updatedContents.unshift({
          contentId: 'ecommerce-video-1',
          type: 'video',
          src: settings.videoSrc,
          positionNumber: 1,
          isEcommerceVideo: true
        });
      }
    }

    // 更新文本内容
    ['priceText', 'tagText', 'titleText', 'contentText'].forEach(key => {
      if (settings[key] !== undefined) {
        const textType = this.getTextType(key);
        const textContentIndex = updatedContents.findIndex(content => 
          content.type === 'text' && content[textType]
        );
        
        if (textContentIndex !== -1) {
          updatedContents[textContentIndex] = {
            ...updatedContents[textContentIndex],
            text: settings[key]
          };
        }
      }
    });

    // 更新按钮设置
    if (settings.buttonText !== undefined) {
      const buttonContentIndex = updatedContents.findIndex(content => 
        content.type === 'button'
      );
      
      if (buttonContentIndex !== -1) {
        updatedContents[buttonContentIndex] = {
          ...updatedContents[buttonContentIndex],
          text: settings.buttonText,
          content: settings.buttonText,
          backgroundColor: settings.buttonColor || '#ff6600',
          color: settings.buttonTextColor || '#ffffff'
        };
      }
    }

    return updatedContents;
  }

  /**
   * 根据设置键获取文本类型标识
   * @param {string} settingKey 设置键
   * @returns {string} 文本类型标识
   */
  static getTextType(settingKey) {
    const typeMap = {
      'priceText': 'isPrice',
      'tagText': 'isTag',
      'titleText': 'isTextTitle',
      'contentText': 'isContent'
    };
    
    return typeMap[settingKey] || 'isContent';
  }
}

export default EcommerceTemplate; 