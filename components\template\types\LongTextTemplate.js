/**
 * 长文本模板处理器
 * 负责处理长文本模板的特殊逻辑，支持标题+长文本描述+按钮+图片的布局
 */

import { ClickEventValidator } from '@/utils/clickEventManager.js';

export default {
  /**
   * 获取模板渲染器组件
   * @returns {string} 渲染器组件名称
   */
  getRenderer() {
    return 'LongTextTemplateRenderer';
  },

  /**
   * 处理模板数据，标准化内容格式
   * @param {Object} template 原始模板数据
   * @returns {Object} 处理后的模板数据
   */
  processTemplate(template) {
    if (!template) return template;

    console.log('LongTextTemplate.processTemplate - 开始处理模板:', template);

    const processedTemplate = {
      ...template,
      templateType: 'longtext' // 标记为长文本模板
    };

    // 确保有pages数组
    let pages = [];
    if (typeof template.pages === 'string') {
      try {
        pages = JSON.parse(template.pages);
      } catch (error) {
        console.error('LongTextTemplate.processTemplate - 解析pages字符串失败:', error);
        pages = [];
      }
    } else if (Array.isArray(template.pages)) {
      pages = template.pages;
    }

    console.log('LongTextTemplate.processTemplate - 解析后的pages:', pages);

    // 处理每一页的内容
    const processedPages = pages.map(page => {
      if (!page.contents || !Array.isArray(page.contents)) {
        return page;
      }

      // 处理页面内容，确保长文本内容正确显示
      const processedContents = page.contents.map(content => {
        const processedContent = {
          ...content,
          // 确保内容文本正确显示，优先使用content字段，其次是text字段
          content: content.content || content.text || '',
          // 确保类型正确
          type: content.type || 'text',
          // 确保有contentId
          contentId: content.contentId || `${content.type || 'text'}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          // 确保可见性
          visible: content.visible !== false,
          // 添加模板相关信息
          userId: template.userId || null,
          templateId: template.templateId || null,
          pageId: content.pageId || page.pageId || 1
        };

        // 对于按钮类型，确保有正确的动作配置
        if (processedContent.type === 'button') {
          processedContent.actionType = content.actionType || 'OPEN_BROWSER';
          processedContent.actionUrl = content.actionUrl || '';
          processedContent.actionJson = content.actionJson || { target: '' };
        }

        console.log('LongTextTemplate.processTemplate - 处理内容项:', {
          原始: content,
          处理后: processedContent
        });

        return processedContent;
      });

      return {
        ...page,
        contents: processedContents
      };
    });

    processedTemplate.pages = processedPages;

    console.log('LongTextTemplate.processTemplate - 处理完成:', processedTemplate);
    return processedTemplate;
  },

  /**
   * 初始化模板内容
   * @param {Object} template 模板数据
   * @param {Object} settings 长文本设置
   * @returns {Array} 初始化后的内容数组
   */
  initializeContents(template, settings = {}) {
    console.log('LongTextTemplate.initializeContents - 开始初始化内容:', template);

    if (!template) {
      console.log('LongTextTemplate.initializeContents - 没有模板数据，返回空数组');
      return [];
    }

    // 如果模板已有内容，使用现有内容
    if (template.pages && Array.isArray(template.pages) && template.pages.length > 0) {
      const page = template.pages[0];
      if (page.contents && Array.isArray(page.contents) && page.contents.length > 0) {
        console.log('LongTextTemplate.initializeContents - 使用现有内容:', page.contents);
        return page.contents.map(content => ({
          ...content,
          contentId: content.contentId || `${content.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          userId: template.userId || null,
          templateId: template.templateId || null,
          pageId: content.pageId || page.pageId || 1
        }));
      }
    }

    // 如果模板有字符串格式的pages，解析它
    if (typeof template.pages === 'string') {
      try {
        const parsedPages = JSON.parse(template.pages);
        if (Array.isArray(parsedPages) && parsedPages.length > 0) {
          const page = parsedPages[0];
          if (page.contents && Array.isArray(page.contents) && page.contents.length > 0) {
            console.log('LongTextTemplate.initializeContents - 使用解析后的内容:', page.contents);
            return page.contents.map(content => ({
              ...content,
              contentId: content.contentId || `${content.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              userId: template.userId || null,
              templateId: template.templateId || null,
              pageId: content.pageId || page.pageId || 1
            }));
          }
        }
      } catch (error) {
        console.error('LongTextTemplate.initializeContents - 解析pages字符串失败:', error);
      }
    }

    // 如果没有内容，创建默认内容
    console.log('LongTextTemplate.initializeContents - 创建默认内容');
    return this.createDefaultContents(template, settings);
  },

  /**
   * 验证模板内容
   * @param {Array} contents 模板内容数组
   * @param {Object} settings 长文本设置（包含样式信息）
   * @returns {Object} 验证结果
   */
  validateContents(contents, settings = {}) {
    const result = {
      isValid: true,
      errors: [],
      focusType: null, // 添加需要聚焦的内容类型
      focusIndex: -1   // 添加需要聚焦的内容索引
    };

    if (!Array.isArray(contents) || contents.length === 0) {
      result.isValid = false;
      result.errors.push('长文本模板必须包含内容');
      return result;
    }

    // 检查是否有标题
    const titleContent = contents.find(content => 
      content.type === 'text' && content.isTextTitle === 1
    );
    if (!titleContent || !titleContent.content || titleContent.content.trim() === '') {
      result.isValid = false;
      result.errors.push('长文本模板必须包含标题');
      result.focusType = 'text';
      result.focusIndex = contents.findIndex(c => c.type === 'text' && c.isTextTitle === 1);
      return result;
    }

    // 检查是否有长文本描述
    const descContent = contents.find(content => 
      content.type === 'text' && content.isTextTitle === 0 && content.positionNumber === 2
    );
    if (!descContent || !descContent.content || descContent.content.trim() === '') {
      result.isValid = false;
      result.errors.push('长文本模板必须包含文本描述');
      result.focusType = 'text';
      result.focusIndex = contents.findIndex(c => c.type === 'text' && c.isTextTitle === 0 && c.positionNumber === 2);
      return result;
    }

    // 检查按钮内容（根据样式确定需要检查的按钮数量）
    const buttonContents = contents.filter(content => content.type === 'button');
    const selectedStyle = settings.selectedStyle || 'simple';
    
    if (selectedStyle === 'general') {
      // 一般样式需要检查两个按钮
      if (buttonContents.length < 2) {
        result.isValid = false;
        result.errors.push('一般样式的长文本模板必须包含两个按钮');
        result.focusType = 'button';
        result.focusIndex = buttonContents.length;
        return result;
      } else {
        // 检查两个按钮的内容和动作
        for (let i = 0; i < buttonContents.length; i++) {
          const button = buttonContents[i];
          
          // 先检查按钮文本 - 检查是否还是默认的"编辑按钮"
          if (!button.content || button.content.trim() === '' || button.content.trim() === '编辑按钮') {
            result.isValid = false;
            result.errors.push(`请编辑第${i + 1}个按钮的内容`);
            result.focusType = 'button';
            result.focusIndex = i;
            return result;
          }
          
          // 再检查按钮动作 - 使用统一的校验器
          const buttonValidationResult = ClickEventValidator.validate(button);
          if (!buttonValidationResult.valid) {
            result.isValid = false;
            result.errors.push(`第${i + 1}个按钮：${buttonValidationResult.message}`);
            result.focusType = 'button';
            result.focusIndex = i;
            return result;
          }
        }
      }
    } else {
      // 简单样式只检查一个按钮
      if (buttonContents.length < 1) {
        result.isValid = false;
        result.errors.push('长文本模板必须包含至少一个按钮');
        result.focusType = 'button';
        result.focusIndex = 0;
        return result;
      } else {
        // 简单样式只验证第一个按钮，忽略其他按钮
        const button = buttonContents[0];
        // 先检查按钮文本 - 检查是否还是默认的"编辑按钮"
        if (!button.content || button.content.trim() === '' || button.content.trim() === '编辑按钮') {
          result.isValid = false;
          result.errors.push('请编辑按钮的内容');
          result.focusType = 'button';
          result.focusIndex = 0;
          return result;
        }
        
        // 再检查按钮动作 - 使用统一的校验器
        const buttonValidationResult = ClickEventValidator.validate(button);
        if (!buttonValidationResult.valid) {
          result.isValid = false;
          result.errors.push(`按钮：${buttonValidationResult.message}`);
          result.focusType = 'button';
          result.focusIndex = 0;
          return result;
        }
      }
    }

    // 最后检查图片内容
    const imageContents = contents.filter(content => content.type === 'image');
    for (let i = 0; i < imageContents.length; i++) {
      const imageContent = imageContents[i];
      if (!imageContent.src || imageContent.src.trim() === '' || 
          imageContent.src.includes('defaultImg') || 
          imageContent.src.includes('default')) {
        result.isValid = false;
        result.errors.push('请上传图片');
        result.focusType = 'image';
        result.focusIndex = contents.findIndex(c => c.type === 'image');
        return result;
      }
    }

    return result;
  },

  /**
   * 格式化内容用于提交
   * @param {Array} contents 模板内容数组
   * @param {Object} settings 长文本设置
   * @returns {Array} 格式化后的内容数组
   */
  formatContentsForSubmit(contents, settings = {}) {
    if (!Array.isArray(contents)) return [];
    
    const selectedStyle = settings.selectedStyle || 'simple';
    
    return contents.map(content => {
      const formattedContent = { ...content };
      
      // 移除编辑器特定的属性
      delete formattedContent.isEditing;
      delete formattedContent.editContent;
      delete formattedContent.defaultSrc;
      
      // 对于文本类型，确保内容字段被更新
      if (formattedContent.type === 'text' && formattedContent.editContent !== undefined) {
        formattedContent.content = formattedContent.editContent;
      }
      
      return formattedContent;
    }).filter(content => {
      // 如果是简单样式，过滤掉多余的按钮（只保留第一个）
      if (selectedStyle === 'simple' && content.type === 'button') {
        const buttonContents = contents.filter(c => c.type === 'button');
        const buttonIndex = buttonContents.findIndex(b => b.contentId === content.contentId);
        return buttonIndex === 0; // 只保留第一个按钮
      }
      return true;
    });
  },

  /**
   * 确保一般样式有两个按钮
   * @param {Array} contents 模板内容数组
   * @param {Object} settings 长文本设置
   * @returns {Array} 更新后的内容数组
   */
  ensureButtonsForGeneralStyle(contents, settings = {}) {
    if (!Array.isArray(contents)) return [];
    
    const selectedStyle = settings.selectedStyle || 'simple';
    
    if (selectedStyle === 'general') {
      const buttonContents = contents.filter(content => content.type === 'button');
      
      // 如果按钮数量少于2个，添加缺少的按钮
      if (buttonContents.length < 2) {
        const newContents = [...contents];
        
        // 确保现有按钮有正确的contentId，但不修改内容
        buttonContents.forEach((button, index) => {
          if (!button.contentId || button.contentId === 'undefined') {
            button.contentId = `btn${index + 1}-${Date.now()}`;
          }
          // 不修改第一个按钮的内容，保持接口返回的"编辑按钮"
        });
        
        // 添加第二个按钮
        if (buttonContents.length === 1) {
          const secondButton = {
            contentId: `btn2-${Date.now()}`,
            type: 'button',
            content: '编辑按钮', // 第二个按钮使用不同的默认文本
            pageId: 1,
            positionNumber: 5, // 修改为5，避免与第一个按钮的位置冲突
            isTextTitle: 0,
            pageLayout: 'center',
            pageLines: 5, // 修改为5
            actionType: 'OPEN_BROWSER',
            actionUrl: '',
            actionJson: { target: '' }
          };
          newContents.push(secondButton);
        }
        // 如果没有按钮，添加两个按钮
        else if (buttonContents.length === 0) {
          const firstButton = {
            contentId: `btn1-${Date.now()}`,
            type: 'button',
            content: '编辑按钮',
            pageId: 1,
            positionNumber: 3,
            isTextTitle: 0,
            pageLayout: 'center',
            pageLines: 3,
            actionType: 'OPEN_BROWSER',
            actionUrl: '',
            actionJson: { target: '' }
          };
          
          const secondButton = {
            contentId: `btn2-${Date.now()}`,
            type: 'button',
            content: '编辑按钮',
            pageId: 1,
            positionNumber: 5, // 修改为5
            isTextTitle: 0,
            pageLayout: 'center',
            pageLines: 5, // 修改为5
            actionType: 'OPEN_BROWSER',
            actionUrl: '',
            actionJson: { target: '' }
          };
          
          newContents.push(firstButton, secondButton);
        }
        
        return newContents;
      }
    }
    
    return contents;
  },

  /**
   * 获取模板默认配置
   * @returns {Object} 默认配置
   */
  getDefaultConfig() {
    return {
      maxTitleLength: 15,
      maxDescriptionLength: 350,
      maxButtonLength: 15,
      allowedContentTypes: ['text', 'button', 'image'],
      supportedStyles: ['simple', 'general', 'advanced']
    };
  },

  /**
   * 创建默认的长文本模板内容
   * @param {Object} template 模板数据（可选，用于从接口获取默认内容）
   * @param {Object} settings 长文本设置
   * @returns {Array} 默认内容数组
   */
  createDefaultContents(template = null, settings = {}) {
    // 如果有模板数据且有页面内容，优先使用接口返回的内容
    if (template && template.pages && Array.isArray(template.pages) && template.pages.length > 0) {
      const page = template.pages[0];
      if (page.contents && Array.isArray(page.contents) && page.contents.length > 0) {
        return page.contents.map(content => ({
          ...content,
          contentId: content.contentId || `${content.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          userId: template.userId || null,
          templateId: template.templateId || null
        }));
      }
    }

    // 创建默认的长文本模板内容
    const defaultContents = [];
    const selectedStyle = settings.selectedStyle || 'simple';
    
    // 标题文本
    defaultContents.push({
      contentId: `title-${Date.now()}`,
      type: 'text',
      content: '编辑标题，最多显示15个字',
      pageId: 1,
      positionNumber: 1,
      isTextTitle: 1,
      pageLayout: 'left',
      pageLines: 1,
      actionJson: null
    });
    
    // 长文本描述
    defaultContents.push({
      contentId: `description-${Date.now()}`,
      type: 'text',
      content: '【名称】编辑文本描述，最多显示350个字。编辑文本描述，最多显示350个字。编辑文本描述，最多显示350个字。编辑文本描述，最多显示350个字。编辑文本描述，最多显示350个字',
      pageId: 1,
      positionNumber: 2,
      isTextTitle: 0,
      pageLayout: 'left',
      pageLines: 2,
      actionJson: null
    });
    
    // 根据样式创建按钮 - 恢复正确的按钮内容
    if (selectedStyle === 'general') {
      // 一般样式：创建两个按钮
      defaultContents.push({
        contentId: `btn1-${Date.now()}`,
        type: 'button',
        content: '编辑按钮',
        pageId: 1,
        positionNumber: 3,
        isTextTitle: 0,
        pageLayout: 'center',
        pageLines: 3,
        actionType: 'OPEN_BROWSER',
        actionUrl: '',
        actionJson: { target: '' }
      });
      
      defaultContents.push({
        contentId: `btn2-${Date.now()}`,
        type: 'button',
        content: '编辑按钮',
        pageId: 1,
        positionNumber: 5,
        isTextTitle: 0,
        pageLayout: 'center',
        pageLines: 5,
        actionType: 'OPEN_BROWSER',
        actionUrl: '',
        actionJson: { target: '' }
      });
    } else {
      // 简单样式：创建一个按钮，内容是"编辑按钮"（与接口数据一致）
      defaultContents.push({
        contentId: `btn1-${Date.now()}`,
        type: 'button',
        content: '编辑按钮',
        pageId: 1,
        positionNumber: 3,
        isTextTitle: 0,
        pageLayout: 'center',
        pageLines: 3,
        actionType: 'OPEN_BROWSER',
        actionUrl: '',
        actionJson: { target: '' }
      });
    }
    
    // 底部图片
    defaultContents.push({
      contentId: `image-${Date.now()}`,
      type: 'image',
      content: '该图片位建议选择比例为3：1（像素最小576:192）的图片，大小建议500KB以内',
      src: '/aim_files/aim_defult/defaultImg1.jpg',
      pageId: 1,
      positionNumber: selectedStyle === 'general' ? 6 : 4, // 根据按钮数量调整图片位置
      isTextTitle: 0,
      pageLayout: 'center',
      pageLines: selectedStyle === 'general' ? 6 : 4,
      actionJson: { target: '' }
    });
    
    return defaultContents;
  }
};