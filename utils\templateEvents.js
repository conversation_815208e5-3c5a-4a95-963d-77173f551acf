/**
 * 模板事件处理工具类
 * 提供模板相关的事件分发和处理
 */

/* eslint-disable no-unused-vars */
// 模板事件管理

// 创建自定义事件
const createCustomEvent = (eventName, detail = {}) => {
  try {
    return new CustomEvent(eventName, { detail, bubbles: true });
  } catch (error) {
    console.error(`创建自定义事件 ${eventName} 失败:`, error);
    return null;
  }
};

/**
 * 触发模板重置事件，通知所有监听组件清除缓存数据
 */
export const dispatchTemplateReset = () => {
  // console.log('触发模板重置事件');
  
  // 通过事件总线发送模板重置事件
  if (window.PARAM_EVENT_BUS) {
    window.PARAM_EVENT_BUS.emit('template-reset');
  }
  
  // 通过DOM事件发送模板重置事件
  const event = new CustomEvent('template-reset');
  document.dispatchEvent(event);
};

/**
 * 触发模板切换事件，通知组件在模板切换时重置状态
 */
export const dispatchTemplateSwitchEvent = (templateData) => {
  // console.log('触发模板切换事件');
  
  // 通过事件总线发送模板切换事件
  if (window.PARAM_EVENT_BUS) {
    window.PARAM_EVENT_BUS.emit('template-switch', templateData);
  }
  
  // 通过DOM事件发送模板切换事件
  const event = new CustomEvent('template-switch', {
    detail: templateData
  });
  document.dispatchEvent(event);
};

/**
 * 切换模板，设置当前选中的模板
 * @param {Object} template 要切换到的模板
 * @param {Object} TemplateFactory 模板工厂实例
 * @param {Function} callback 切换成功后的回调函数
 */
export const switchTemplate = (template, TemplateFactory, callback) => {
  // console.log('switchTemplate: 接收到模板切换请求', template);
  
  if (!template) {
    console.warn('switchTemplate: 没有提供模板数据');
    return false;
  }
  
  try {
    // 重置红包模板标记
    window.TEMPLATE_IS_REDPACKET = false;
    
    // 判断是否为红包模板 - 使用简化的判断逻辑
    const isRedPacket = template.cardId === 'com.hbm.redpacket' || 
                       template.tplType === 'redpacket' || 
                       (template.scene && template.scene.includes('红包'));
    
    if (isRedPacket) {
      // console.log('选择了红包模板:', template.templateName || template.scene);
      window.TEMPLATE_IS_REDPACKET = true;
      
      // 将选中的模板信息保存到全局变量，方便其他组件访问
      window.TEMPLATE_DIALOG_DATA = {
        selectedTemplate: template,
        isRedPacket: true
      };
    } else if (template) {
      // 如果不是红包模板，则确保全局数据正确
      window.TEMPLATE_DIALOG_DATA = {
        selectedTemplate: template,
        isRedPacket: false
      };
    }
    
    // 触发模板切换事件，通知其他组件
    dispatchTemplateSwitchEvent(template);
    
    // 执行回调
    if (typeof callback === 'function') {
      callback(template);
    }
    return true;
  } catch (error) {
    console.error('switchTemplate: 切换模板时发生错误:', error);
    return false;
  }
};

/**
 * 添加模板重置事件监听
 * @param {Function} callback 回调函数
 * @returns {Function} 移除监听的函数
 */
export const addTemplateResetListener = (callback) => {
  if (typeof window !== 'undefined' && typeof callback === 'function') {
    const handler = () => {
      // console.log('接收到模板重置事件');
      callback();
    };
    
    window.addEventListener('template-reset', handler);
    
    // 返回移除监听的函数
    return () => {
      window.removeEventListener('template-reset', handler);
    };
  }
  
  // 如果不在浏览器环境或回调不是函数，返回空函数
  return () => {};
};

/**
 * 添加模板切换事件监听
 * @param {Function} callback 回调函数
 * @returns {Function} 移除监听的函数
 */
export const addTemplateSwitchListener = (callback) => {
  if (typeof window !== 'undefined' && typeof callback === 'function') {
    const handler = () => {
      // console.log('接收到模板切换事件');
      callback();
    };
    
    window.addEventListener('template-switch', handler);
    
    // 返回移除监听的函数
    return () => {
      window.removeEventListener('template-switch', handler);
    };
  }
  
  // 如果不在浏览器环境或回调不是函数，返回空函数
  return () => {};
};

// 全局参数事件总线
let paramEventBus = null;
const pendingEvents = new Map(); // 用于防止重复触发的事件缓存

/**
 * 参数插入事件分发函数
 * 尝试向当前选中的文本元素或可编辑区域插入参数
 * @param {Object|string|number} paramId - 参数ID或包含参数信息的对象
 * @returns {boolean} - 是否成功分发事件
 */
export const dispatchParamInsertEvent = (paramId) => {
  // console.log('全局参数插入事件分发:', paramId);
  
  // 防止频繁触发，增加时间间隔限制
  if (window.lastParamDispatchTimestamp && (Date.now() - window.lastParamDispatchTimestamp < 300)) {
    // console.log('忽略短时间内的重复参数插入请求');
    return false;
  }
  window.lastParamDispatchTimestamp = Date.now();
  
  try {
    // 初始化参数事件总线
    const bus = initParamEventBus();
    if (!bus) {
      console.error('参数事件总线不可用，无法分发参数插入事件');
      return false;
    }
    
    // 规范化参数ID，确保后续处理使用一致的格式
    let normalizedParam;
    if (typeof paramId === 'object') {
      normalizedParam = {...paramId}; // 创建新对象，避免引用原对象
      // 确保对象上有paramId属性
      if (!normalizedParam.paramId && normalizedParam.id) {
        normalizedParam.paramId = normalizedParam.id;
      }
    } else if (typeof paramId === 'string' && paramId.includes('{#param')) {
      const match = paramId.match(/{#param(\d+)#}/);
      if (match && match[1]) {
        normalizedParam = {
          paramId: match[1],
          text: paramId
        };
      } else {
        normalizedParam = { paramId: paramId };
      }
    } else {
      normalizedParam = { paramId: paramId };
    }
    
    // console.log('规范化后的参数信息:', normalizedParam);
    
    // 检查是否指定了文本元素ID，如果没有则尝试查找当前选中的元素
    if (!normalizedParam.activeTextElementId && window.TEXT_ELEMENT_REFS) {
      const selectedElement = window.TEXT_ELEMENT_REFS.find(item => 
        item.ref && item.ref.value && 
        item.ref.value.classList.contains('selected'));
      
      if (selectedElement) {
        normalizedParam.activeTextElementId = selectedElement.id;
        // console.log('自动设置activeTextElementId:', selectedElement.id);
      }
    }
    
    // 查找选中的文本元素
    let textElement = null;
    
    // 如果参数包含activeTextElementId，优先使用它
    if (normalizedParam.activeTextElementId) {
      if (window.TEXT_ELEMENT_REFS) {
        textElement = window.TEXT_ELEMENT_REFS.find(item => item.id === normalizedParam.activeTextElementId);
        if (textElement) {
          // console.log(`找到通过ID指定的文本元素: ${normalizedParam.activeTextElementId}`);
        }
      }
    }
    
    // 如果没有找到指定的文本元素，查找选中的元素
    if (!textElement && window.TEXT_ELEMENT_REFS) {
      const selectedElement = window.TEXT_ELEMENT_REFS.find(item => 
        item.ref && item.ref.value && 
        item.ref.value.classList.contains('selected'));
      
      if (selectedElement) {
        textElement = selectedElement;
        // console.log('找到选中的文本元素:', selectedElement.title || '未命名');
      }
    }
    
    // 如果找到了文本元素，直接调用它的插入参数方法
    if (textElement && typeof textElement.insertParam === 'function') {
      // console.log('直接调用文本元素的insertParam方法');
      return textElement.insertParam(normalizedParam);
    }
    
    // 如果存在全局插入函数，调用它
    if (typeof window.insertParamToCurrentElement === 'function') {
      // console.log('使用全局insertParamToCurrentElement函数');
      return window.insertParamToCurrentElement(normalizedParam);
    }
    
    // 最后尝试通过事件总线发送参数插入事件
    // console.log('通过事件总线发送参数插入事件');
    bus.emit('insert-param', normalizedParam);
    return true;
  } catch (error) {
    console.error('分发参数插入事件时出错:', error);
    return false;
  }
};

/**
 * 初始化参数事件总线
 * @returns {Object} - 参数事件总线对象
 */
export const initParamEventBus = () => {
  try {
    // 如果已经存在，直接返回
    if (paramEventBus) {
      return paramEventBus;
    }
    
    // 创建事件总线对象
    paramEventBus = {
      listeners: {},
      
      // 注册事件监听器
      on(event, callback) {
        if (!this.listeners[event]) {
          this.listeners[event] = [];
        }
        this.listeners[event].push(callback);
        return this;
      },
      
      // 移除事件监听器
      off(event, callback) {
        if (!this.listeners[event]) return this;
        
        if (!callback) {
          // 如果没有提供回调函数，移除所有该事件的监听器
          this.listeners[event] = [];
        } else {
          // 否则只移除指定的回调函数
          this.listeners[event] = this.listeners[event].filter(
            listener => listener !== callback
          );
        }
        return this;
      },
      
      // 触发事件
      emit(event, data) {
        // 防止事件短时间内重复触发
        const eventKey = `${event}_${JSON.stringify(data?.paramId || 'unknown')}`;
        const now = Date.now();
        
        // 检查是否是重复事件(1ms内同一个参数ID的事件)
        if (pendingEvents.has(eventKey)) {
          const lastTime = pendingEvents.get(eventKey);
          if (now - lastTime < 1) {
            // console.log(`跳过重复事件: ${eventKey}, 距上次触发仅 ${now - lastTime}ms`);
            return this;
          }
        }
        
        // 记录事件时间戳
        pendingEvents.set(eventKey, now);
        
        // 定期清理过期事件记录
        setTimeout(() => {
          pendingEvents.delete(eventKey);
        }, 500);
        
        // 触发事件
        if (!this.listeners[event]) return this;
        
        this.listeners[event].forEach(listener => {
          try {
            listener(data);
          } catch (error) {
            console.error(`执行事件 ${event} 监听器时出错:`, error);
          }
        });
        
        return this;
      }
    };
    
    // 注册到全局变量
    window.PARAM_EVENT_BUS = paramEventBus;
    
    // console.log('参数事件总线初始化完成');
    return paramEventBus;
  } catch (error) {
    console.error('初始化参数事件总线失败:', error);
    return null;
  }
};

// 导出全局获取事件总线的方法
export const getParamEventBus = () => {
  return paramEventBus || initParamEventBus();
};

// 在模块加载时自动初始化参数事件总线
initParamEventBus(); 