import axios from 'axios'
import envConfig from '../../env.config.js';

// 创建 axios 实例
const service = axios.create({
    baseURL: envConfig.apiBaseUrl, // 替换为API 基础 URL
    timeout: 5000, // 请求超时时间，单位为毫秒
})

// 请求拦截器
service.interceptors.request.use(
    config => {
        try {
            // 从 localStorage 获取 layuiAdmin 对应的值
            const storedData = localStorage.getItem('layuiAdmin');
            let token = null;
            if (storedData) {
                // 解析 JSON 字符串为 JavaScript 对象
                const parsedData = JSON.parse(storedData);
                // 获取 Authorization
                token = parsedData.Authorization;
            }
            // console.log('获取到的 token:', token); // 打印 token 查看是否获取成功
            if (token) {
                config.headers.Authorization = token;
            }
        } catch (error) {
            console.error('访问 localStorage 出错:', error);
        }

        if (config.data instanceof FormData) {
            config.headers['Content-Type'] = 'multipart/form-data';
        } else {
            config.headers['Content-Type'] = 'application/json';
        }
        return config;
    },
    error => {
        console.error('请求错误:', error);
        return Promise.reject(error);
    }
);


// 响应拦截器
service.interceptors.response.use(
    response => {
        // 对响应数据统一处理成功响应
        const res = response.data;
        if (res.code !== 0) {
            console.error('请求失败，响应数据:', res);
            ElMessage.error(res.msg || res.message || '请求失败，未知错误');
            return Promise.reject(new Error(res.msg || res.message || '请求失败，未知错误'));
        } else {
            return res;
        }
    },
    error => {
        console.error('响应错误:', error);
        if (error.response) {
            console.error('错误响应数据:', error.response.data);
            ElMessage.error(error.response.data.msg || error.response.data.message || '响应错误，请检查控制台日志');
        } else if (error.request) {
            ElMessage.error('没有收到服务器响应，请检查网络连接');
        } else {
            ElMessage.error(error.message || '请求设置错误，请检查控制台日志');
        }
        return Promise.reject(error);
    },
)

export default service
