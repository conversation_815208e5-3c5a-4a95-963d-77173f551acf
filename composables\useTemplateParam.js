/**
 * 模板参数管理组合式函数
 * 提供统一的参数插入、收集和光标处理功能
 */

import { ref, onMounted, onBeforeUnmount, computed, watch } from 'vue';
import { useParamService } from '@/services/ParamService';

/**
 * 模板参数管理
 * @param {Object} options 配置选项
 * @param {Function|HTMLElement} options.target 目标元素或获取目标元素的函数
 * @param {String|Function} options.panelId 面板ID或获取面板ID的函数
 * @param {Boolean} options.autoFocus 是否自动聚焦
 * @param {Function} options.onInserted 插入成功回调
 * @param {Function} options.onChange 内容变更回调
 * @returns {Object} 参数管理相关方法和状态
 */
export function useTemplateParam(options = {}) {
  // 参数服务实例
  const paramService = useParamService();
  
  // 状态管理
  const isActive = ref(false);
  const isFocused = ref(false);
  
  // 当前的参数ID列表
  const paramIds = ref([]);
  
  /**
   * 获取目标元素
   * @returns {HTMLElement|null} - 目标元素
   */
  const getTarget = () => {
    if (!options.target) return null;
    
    const el = options.target();
    
    if (!el) return null;
    
    // 如果是Vue组件实例，尝试获取其$el属性
    if (el.$el) return el.$el;
    
    return el;
  };
  
  /**
   * 获取面板ID
   * @returns {string} - 面板ID
   */
  const getPanelId = () => {
    if (!options.panelId) return '';
    
    const id = options.panelId();
    
    return id || '';
  };
  
  /**
   * 设置焦点到目标元素
   */
  const focus = () => {
    const target = getTarget();
    if (!target) return;
    
    try {
      // 确认元素类型并进行相应处理
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA') {
        target.focus();
        
        // 将光标移到末尾
        const length = target.value.length;
        target.setSelectionRange(length, length);
      } else if (target.getAttribute('contenteditable') === 'true') {
        target.focus();
        
        // 将光标移到内容末尾
        const selection = window.getSelection();
        const range = document.createRange();
        range.selectNodeContents(target);
        range.collapse(false); // 移到末尾
        selection.removeAllRanges();
        selection.addRange(range);
      }
      
      isFocused.value = true;
    } catch (error) {
      console.error('设置焦点失败:', error);
    }
  };
  
  /**
   * 在目标元素中插入参数
   * @param {Object} options - 插入选项
   * @param {string} options.paramId - 参数ID
   * @param {string} options.panelId - 面板ID
   * @returns {boolean} - 是否成功
   */
  const insertParam = async (options = {}) => {
    const el = getTarget();
    if (!el) return false;
    
    try {
      // 获取参数ID
      const paramId = options.paramId || paramService.getNextParamId(options.panelId || getPanelId());
      
      // 获取面板ID
      const panelId = options.panelId || getPanelId();
      
      // 确保元素获得焦点
      if (el.focus && typeof el.focus === 'function') {
        el.focus();
      }
      
      // 等待焦点生效
      await new Promise(resolve => setTimeout(resolve, 10));
      
      // 处理普通输入框和文本域
      if (el.tagName === 'INPUT' || el.tagName === 'TEXTAREA') {
        return insertToInputField(el, paramId, panelId);
      }
      
      // 处理可编辑元素
      if (el.getAttribute('contenteditable') === 'true') {
        return insertToContentEditable(el, paramId, panelId);
      }
      
      return false;
    } catch (error) {
      console.error('插入参数失败:', error);
      return false;
    }
  };
  
  /**
   * 插入参数到普通输入框或文本域
   * @param {HTMLElement} input - 输入框元素
   * @param {string} paramId - 参数ID
   * @param {string} panelId - 面板ID
   * @returns {boolean} - 是否成功
   */
  const insertToInputField = (input, paramId, panelId) => {
    try {
      // 确保输入框有焦点
      input.focus();
      
      // 获取当前选择范围
      const start = input.selectionStart || 0;
      const end = input.selectionEnd || 0;
      const currentValue = input.value || '';
      
      // 准备参数文本
      const paramText = `{#param${paramId}#}`;
      
      // 在光标位置插入参数
      const newValue = currentValue.substring(0, start) + paramText + currentValue.substring(end);
      
      // 更新输入框值
      input.value = newValue;
      
      // 将光标移动到参数后面
      const newPosition = start + paramText.length;
      input.setSelectionRange(newPosition, newPosition);
      
      // 触发输入事件
      input.dispatchEvent(new Event('input', { bubbles: true }));
      
      // 关联参数到面板
      if (panelId) {
        paramService.associateParamWithPanel(paramId, panelId);
      }
      
      // 触发内容变化
      emitChange();
      
      return true;
    } catch (error) {
      console.error('插入参数到输入框失败:', error);
      return false;
    }
  };
  
  /**
   * 插入参数到可编辑元素
   * @param {HTMLElement} element - 可编辑元素
   * @param {string} paramId - 参数ID
   * @param {string} panelId - 面板ID
   * @returns {boolean} - 是否成功
   */
  const insertToContentEditable = (element, paramId, panelId) => {
    try {
      // 确保元素有焦点
      element.focus();
      
      // 创建参数按钮元素
      const paramButton = document.createElement('input');
      paramButton.type = 'button';
      paramButton.className = 'j-btn param-input';
      paramButton.value = `{#param${paramId}#}`;
      paramButton.setAttribute('readonly', 'readonly');
      paramButton.setAttribute('unselectable', 'on');
      paramButton.setAttribute('data-param-id', paramId);
      
      if (panelId) {
        paramButton.setAttribute('data-panel-id', panelId);
        // 关联参数到面板
        paramService.associateParamWithPanel(paramId, panelId);
      }
      
      // 获取选区
      const selection = window.getSelection();
      
      // 关键修复：如果没有选区，创建一个新选区
      if (!selection.rangeCount) {
        const range = document.createRange();
        range.selectNodeContents(element);
        range.collapse(false); // 移动到末尾
        selection.removeAllRanges();
        selection.addRange(range);
      }
      
      // 获取当前选区
      const range = selection.getRangeAt(0);
      
      // 确保选区在目标元素内
      if (!element.contains(range.commonAncestorContainer)) {
        // 如果选区不在目标元素内，重新创建选区
        const newRange = document.createRange();
        newRange.selectNodeContents(element);
        newRange.collapse(false);
        selection.removeAllRanges();
        selection.addRange(newRange);
        range = selection.getRangeAt(0);
      }
      
      // 删除选中内容
      range.deleteContents();
      
      // 插入参数按钮
      range.insertNode(paramButton);
      
      // 插入零宽空格，便于光标定位
      const space = document.createTextNode('\u200B');
      range.setStartAfter(paramButton);
      range.setEndAfter(paramButton);
      range.insertNode(space);
      
      // 更新选区位置到参数后面
      range.setStartAfter(space);
      range.collapse(true);
      selection.removeAllRanges();
      selection.addRange(range);
      
      // 触发输入事件
      element.dispatchEvent(new Event('input', { bubbles: true }));
      
      // 触发内容变化
      emitChange();
      
      return true;
    } catch (error) {
      console.error('插入参数到可编辑元素失败:', error);
      return false;
    }
  };
  
  /**
   * 设置目标元素的内容
   * @param {string} content - 内容
   * @returns {boolean} - 是否成功
   */
  const setContent = (content) => {
    const el = getTarget();
    if (!el) return false;
    
    try {
      // 格式化内容
      const panelId = getPanelId();
      const formattedContent = paramService.formatContentWithParams(content, panelId);
      
      // 设置内容
      if (el.innerHTML !== formattedContent) {
        el.innerHTML = formattedContent;
      }
      
      // 提取参数ID
      extractParamIds();
      
      return true;
    } catch (error) {
      console.error('设置内容失败:', error);
      return false;
    }
  };
  
  /**
   * 从目标元素中提取所有参数ID
   * @returns {Array<string>} - 参数ID数组
   */
  const extractParamIds = () => {
    const el = getTarget();
    if (!el) return [];
    
    try {
      // 从元素中提取所有参数元素
      const paramElements = el.querySelectorAll('.j-btn[data-param-id], [data-param-id]');
      const ids = Array.from(paramElements).map(el => el.getAttribute('data-param-id')).filter(Boolean);
      
      // 从内容中提取参数标记
      const content = el.innerHTML || '';
      const paramRegex = /{#param(\d+)#}/g;
      const matches = content.match(paramRegex) || [];
      const matchIds = matches.map(match => {
        const m = match.match(/{#param(\d+)#}/);
        return m ? m[1] : null;
      }).filter(Boolean);
      
      // 合并去重
      const allIds = Array.from(new Set([...ids, ...matchIds]));
      
      // 更新参数ID列表
      paramIds.value = allIds;
      
      return allIds;
    } catch (error) {
      console.error('提取参数ID失败:', error);
      return [];
    }
  };
  
  /**
   * 触发内容变化事件
   */
  const emitChange = () => {
    if (!options.onChange) return;
    
    const el = getTarget();
    if (!el) return;
    
    // 从元素中提取文本和参数
    const value = paramService.extractTextWithParams(el.innerHTML || '');
    
    // 更新参数ID列表
    extractParamIds();
    
    // 触发变化回调
    options.onChange(value);
  };
  
  /**
   * 收集目标元素中的所有参数
   */
  const collectParams = () => {
    const target = getTarget();
    if (!target) return [];
    
    return paramService.collectParamsFromDOM(target);
  };
  
  /**
   * 处理全局参数插入事件
   * @param {Object} data 事件数据
   */
  const handleGlobalParamEvent = (data) => {
    if (!isActive.value) return;
    
    const target = getTarget();
    if (!target) return;
    
    // 如果没有指定目标或目标匹配
    if (!data.targetId || data.targetId === target.id) {
      insertParam({
        paramId: data.paramId,
        panelId: data.panelId || getPanelId()
      });
    }
  };
  
  // 在组件挂载时注册事件监听
  onMounted(() => {
    isActive.value = true;
    
    // 监听全局参数插入事件
    if (window.PARAM_EVENT_BUS) {
      window.PARAM_EVENT_BUS.on('param-insert-requested', handleGlobalParamEvent);
    }
    
    // 自动聚焦
    if (options.autoFocus) {
      setTimeout(focus, 100);
    }
  });
  
  // 在组件卸载前清理
  onBeforeUnmount(() => {
    isActive.value = false;
    
    // 移除事件监听
    if (window.PARAM_EVENT_BUS) {
      window.PARAM_EVENT_BUS.off('param-insert-requested', handleGlobalParamEvent);
    }
  });
  
  return {
    // 方法
    insertParam,        // 插入参数
    insertToInputField, // 插入参数到输入框
    insertToContentEditable, // 插入参数到可编辑元素
    focus,              // 设置焦点
    setContent,         // 设置内容
    collectParams,      // 收集参数
    extractParamIds,    // 提取参数ID
    emitChange,          // 触发内容变化
    
    // 状态
    isActive,           // 是否活跃
    isFocused,          // 是否有焦点
    
    // 计算属性
    targetElement: computed(() => getTarget()),
    currentPanelId: computed(() => getPanelId()),
    paramIds
  };
} 