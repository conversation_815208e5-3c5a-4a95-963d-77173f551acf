/**
 * 多商品模板处理器
 * 负责处理多商品模板的特殊逻辑和内容初始化
 */

import { ClickEventTypeConverter, ActionJsonGenerator } from '@/utils/clickEventManager.js';

export default class MultiProductTemplate {
  /**
   * 获取模板渲染器组件
   * @returns {string} 渲染器组件名称
   */
  static getRenderer() {
    return 'MultiProductTemplateRenderer';
  }

  /**
   * 初始化多商品模板内容
   * @param {Object} template 模板对象
   * @returns {Array} 初始化后的内容数组
   */
  static initializeContents(template) {
    console.log('MultiProductTemplate - 初始化多商品模板内容:', template);
    
    // 保证pages为数组格式
    if (typeof template.pages === 'string') {
      try {
        template.pages = JSON.parse(template.pages);
      } catch (e) {
        console.error('MultiProductTemplate - pages字段解析失败:', e);
        template.pages = [];
      }
    }

    if (!template) {
      console.warn('MultiProductTemplate - 模板对象为空');
      return [];
    }

    try {
      // 检查是否为多商品模板
      if (template.templateId === 13 || template.templateId === '13' || template.cardId === 'com.hbm.ecommerce') {
        console.log('MultiProductTemplate - 检测到多商品模板，尝试解析API数据');
        
        // 尝试从模板本身解析pages数据
        if (template.pages && Array.isArray(template.pages) && template.pages.length > 0) {
          const firstPage = template.pages[0];
          if (firstPage.contents && Array.isArray(firstPage.contents) && firstPage.contents.length > 0) {
            console.log('MultiProductTemplate - 发现API内容数据，开始解析');
            return this.parseApiData(template);
          }
        }
      }

      // 尝试解析pages数据
      let pagesData = null;
      if (template.pages) {
        try {
          pagesData = typeof template.pages === 'string' ? JSON.parse(template.pages) : template.pages;
          console.log('MultiProductTemplate - 解析的pages数据:', pagesData);
        } catch (error) {
          console.error('MultiProductTemplate - 解析pages数据失败:', error);
        }
      }

      // 如果有现有内容，则处理现有内容
      if (pagesData && pagesData.contents && Array.isArray(pagesData.contents)) {
        return this.processExistingContents(pagesData.contents);
      }
      
      // 如果是数组格式的pages，处理第一页的内容
      if (Array.isArray(pagesData) && pagesData.length > 0) {
        const firstPage = pagesData[0];
        if (firstPage.contents && Array.isArray(firstPage.contents)) {
          return this.processExistingContents(firstPage.contents);
        }
      }
      
      // 如果没有API数据，提供默认内容结构
      console.log('MultiProductTemplate - 没有找到有效的API内容，提供默认内容结构');
      return this.createDefaultContents();
    } catch (error) {
      console.error('MultiProductTemplate - 初始化内容失败:', error);
      return this.createDefaultContents();
    }
  }

  /**
   * 创建默认的多商品模板内容
   * @returns {Array} 默认内容数组
   */
  static createDefaultContents() {
    console.log('MultiProductTemplate - 创建默认内容结构');

    const defaultData = {
      headerTitle: '精选商品',
      headerSubtitle: '编辑文本，最多12个中文字',
      products: [
        {
          id: 1,
          image: '',
          title: '编辑文本，最多24个中文字',
          tag: '编辑文本',
          price: '100.00',
          buttonText: '立即购买',
          aimCurrencyDisplay: 1,
          imageClickEvent: this.getDefaultClickEvent(),
          buttonClickEvent: this.getDefaultClickEvent()
        },
        {
          id: 2,
          image: '',
          title: '编辑文本，最多24个中文字',
          tag: '编辑文本',
          price: '100.00',
          buttonText: '立即购买',
          aimCurrencyDisplay: 1,
          imageClickEvent: this.getDefaultClickEvent(),
          buttonClickEvent: this.getDefaultClickEvent()
        }
      ],
      productCount: 2,
      selectedProductIndex: 0
    };

    return this.generateContentsFromSettings(defaultData);
  }

  /**
   * 处理现有内容
   * @param {Array} contents 现有内容数组
   * @returns {Array} 处理后的内容数组
   */
  static processExistingContents(contents) {
    return contents.map((content, index) => ({
      ...content,
      id: content.id || content.contentId || `content_${index}`,
      isMultiProduct: true
    }));
  }

  /**
   * 从API数据解析内容
   * @param {Object} apiData API返回的数据
   * @returns {Array} 解析后的内容数组
   */
  static parseApiData(apiData) {
    console.log('MultiProductTemplate - 解析API数据:', apiData);
    
    try {
      // 防止无限递归 - 检查是否已经在解析过程中
      if (apiData._parsing) {
        console.warn('MultiProductTemplate - 检测到递归调用，返回空数组');
        return [];
      }
      
      // 标记正在解析
      apiData._parsing = true;
      
      let pages = null;
      
      // 检查API数据结构
      if (apiData.pages) {
        pages = Array.isArray(apiData.pages) ? apiData.pages : [apiData.pages];
      } else {
        console.warn('MultiProductTemplate - API数据格式不正确，没有pages字段');
        return [];
      }

      // 确保有至少一页数据
      if (pages.length === 0) {
        console.warn('MultiProductTemplate - 页面数据为空');
        return [];
      }

      // 获取第一页的内容
      const firstPage = pages[0];
      if (!firstPage.contents || !Array.isArray(firstPage.contents)) {
        console.warn('MultiProductTemplate - 页面内容为空');
        return [];
      }

      console.log('MultiProductTemplate - 找到页面内容，数量:', firstPage.contents.length);

      // 返回所有内容，包括隐藏的内容，让后端处理
      const parsedContents = firstPage.contents.map((content, index) => {
        // 添加日志输出
        if (content.type === 'text' && (content.positionNumber === 6 || content.positionNumber === 16 || content.positionNumber === 26)) {
          console.log(`MultiProductTemplate - parseApiData - 货币符号控制 positionNumber=${content.positionNumber}:`, {
            visible: content.visible,
            content: content.content
          });
        }
        
        return {
          id: content.contentId || `api_content_${index}`,
          type: content.type,
          content: content.content,
          positionNumber: content.positionNumber,
          isMultiProduct: true,
          isTextTitle: content.isTextTitle,
          visible: content.visible,
          pageLines: content.pageLines,
          pageLayout: content.pageLayout,
          src: content.type === 'image' ? (content.content || content.src) : undefined,
          actionJson: content.actionJson,
          ...content
        };
      });

      console.log('MultiProductTemplate - 解析后的API内容:', parsedContents.filter(content => 
        content.type === 'text' && [6, 16, 26].includes(content.positionNumber)
      ));
      
      // 清除解析标记
      delete apiData._parsing;
      
      // 验证解析后的内容是否有效
      if (this.validateApiContents(parsedContents)) {
        return parsedContents;
      } else {
        console.warn('MultiProductTemplate - 解析的API内容验证失败，返回空数组');
        return [];
      }

    } catch (error) {
      console.error('MultiProductTemplate - 解析API数据时发生错误:', error);
      // 清除解析标记
      if (apiData && typeof apiData === 'object') {
        delete apiData._parsing;
      }
      return [];
    }
  }

  /**
   * 验证API解析的内容是否有效
   * @param {Array} contents 解析后的内容数组
   * @returns {boolean} 是否有效
   */
  static validateApiContents(contents) {
    if (!Array.isArray(contents) || contents.length === 0) {
      return false;
    }

    // 检查是否有基本的多商品模板结构
    const hasText = contents.some(c => c.type === 'text');
    const hasImage = contents.some(c => c.type === 'image');
    
    return hasText && hasImage;
  }

  /**
   * 验证内容格式
   * @param {Array} contents 内容数组
   * @returns {boolean} 是否有效
   */
  static validateContents(contents) {
    if (!Array.isArray(contents) || contents.length === 0) {
      return false;
    }

    // 检查是否有必要的头部内容
    const hasHeader = contents.some(c => c.type === 'text' && !c.productId);
    
    // 检查是否有商品内容
    const hasProducts = contents.some(c => c.isMultiProduct && c.productId);

    return hasHeader && hasProducts;
  }

  /**
   * 更新设置
   * @param {Array} contents 当前内容
   * @param {Object} settings 新设置
   * @returns {Array} 更新后的内容
   */
  static updateSettings(contents, settings) {
    console.log('MultiProductTemplate - 更新设置:', settings);
    
    // 根据设置更新内容
    return contents.map(content => {
      if (settings.headerTitle && content.id === 'header_title') {
        return { ...content, content: settings.headerTitle };
      }
      if (settings.headerSubtitle && content.id === 'header_subtitle') {
        return { ...content, content: settings.headerSubtitle };
      }
      return content;
    });
  }

  /**
   * 获取默认点击事件配置
   * @returns {Object} 默认点击事件配置
   */
  static getDefaultClickEvent() {
    return {
      // 旧格式字段
      type: 'browser',
      url: '',
      phone: '',
      text: '',
      app: '',
      quick: '',
      email: '',
      schedule: '',
      popup: '',
      packageName: '',
      floorType: '0',
      // 新格式字段（校验函数需要）
      actionType: 'OPEN_BROWSER',
      actionUrl: '',
      actionPath: ''
    };
  }

  // 处理商品数据 - 从contents中提取商品信息，根据新的数据结构
  static handleProductsContent(contents) {
    console.log('MultiProductTemplate - handleProductsContent - 开始处理商品数据');
    
    // 创建positionNumber到内容的映射
    const contentMap = {};
    contents.forEach(item => {
      if (item && item.positionNumber) {
        contentMap[item.positionNumber] = item;
        // 记录关键位置的内容
        if ([6, 16, 26].includes(item.positionNumber)) {
          console.log(`货币符号控制 positionNumber=${item.positionNumber}:`, {
            visible: item.visible,
            content: item.content
          });
        }
      }
    });

    // 提取商品数据
    const products = [];
    const productGroups = [
      { start: 4 },  // 第一个商品从4开始
      { start: 14 }, // 第二个商品从14开始
      { start: 24 }  // 第三个商品从24开始
    ];

    productGroups.forEach((group, index) => {
      const start = group.start;
      console.log(`处理商品组 ${index + 1}，起始位置:`, start);
      
      const product = {
        id: `product-${index + 1}`,
        image: '',
        content: '',
        title: '',
        tag: '',
        price: '',
        buttonText: '',
        aimCurrencyDisplay: 1,
        imageClickEvent: { type: 'OPEN_BROWSER' },
        buttonClickEvent: { type: 'OPEN_BROWSER' },
        hidden: index >= 2 // 默认只显示前2个商品
      };

      // 商品图片
      if (contentMap[start] && contentMap[start].type === 'image') {
        product.image = contentMap[start].src || contentMap[start].content || '';
        product.content = contentMap[start].content || ''; // 添加content字段提取
        console.log(`MultiProductTemplate - 商品${index + 1}图片:`, product.image);
        console.log(`MultiProductTemplate - 商品${index + 1}图片描述:`, product.content); // 添加调试日志
        
        // 处理图片点击事件
        if (contentMap[start].actionJson) {
          const actionJson = contentMap[start].actionJson;
          const actionType = contentMap[start].actionType || 'OPEN_BROWSER';
          
          // 使用统一的转换方法
          product.imageClickEvent = ActionJsonGenerator.toClickEventSettings(actionType, actionJson);
        } else {
          product.imageClickEvent = this.getDefaultClickEvent();
        }
        
        console.log(`商品${index + 1}图片:`, product.image);
      } else {
        product.imageClickEvent = this.getDefaultClickEvent();
      }

      // 商品标题
      if (contentMap[start + 3] && contentMap[start + 3].type === 'text') {
        product.title = contentMap[start + 3].content || '';
        console.log(`商品${index + 1}标题:`, product.title);
      }

      // 商品标签
      if (contentMap[start + 4] && contentMap[start + 4].type === 'text') {
        product.tag = contentMap[start + 4].content || '';
        console.log(`商品${index + 1}标签:`, product.tag);
      }

      // 货币符号显示控制 - 使用商品价格位置的 visible 属性
      if (contentMap[start + 6] && contentMap[start + 6].type === 'text') {
        product.aimCurrencyDisplay = contentMap[start + 6].visible !== undefined ? contentMap[start + 6].visible : 1;
        console.log(`商品${index + 1}货币符号显示:`, product.aimCurrencyDisplay);
      }

      // 商品价格 - 使用实际金额位置的内容
      if (contentMap[start + 7] && contentMap[start + 7].type === 'text') {
        product.price = contentMap[start + 7].content || '';
        console.log(`商品${index + 1}价格:`, product.price);
      }

      // 按钮文本
      if (contentMap[start + 8] && contentMap[start + 8].type === 'text') {
        product.buttonText = contentMap[start + 8].content || '立即购买';
        console.log(`商品${index + 1}按钮文本:`, product.buttonText);
      }

      // 按钮点击事件
      if (contentMap[start + 9] && contentMap[start + 9].type === 'button') {
        // 处理按钮点击事件
        if (contentMap[start + 9].actionJson) {
          const actionJson = contentMap[start + 9].actionJson;
          const actionType = contentMap[start + 9].actionType || 'OPEN_BROWSER';
          
          // 使用统一的转换方法
          const clickEventSettings = ActionJsonGenerator.toClickEventSettings(actionType, actionJson);
          // 确保同时包含新旧格式字段
          product.buttonClickEvent = {
            ...clickEventSettings,
            // 旧格式字段
            type: ClickEventTypeConverter.toClickEventType(actionType),
            url: clickEventSettings.actionUrl || ''
          };
          product.actionJson = actionJson;
        } else {
          const defaultClickEvent = this.getDefaultClickEvent();
          // 默认点击事件已经包含新旧格式字段
          product.buttonClickEvent = defaultClickEvent;
          product.actionJson = ActionJsonGenerator.fromClickEventSettings(defaultClickEvent);
        }
        
        console.log(`商品${index + 1}按钮点击事件:`, product.buttonClickEvent);
      } else {
        const defaultClickEvent = this.getDefaultClickEvent();
        product.buttonClickEvent = defaultClickEvent;
        product.actionJson = ActionJsonGenerator.fromClickEventSettings(defaultClickEvent);
      }

      // 只有当商品有内容时才添加
      if (product.image || product.title || product.tag || product.price || product.buttonText || product.content) {
        products.push(product);
        console.log(`添加商品11111111${index + 1}完整数据:`, {
          id: product.id,
          image: product.image,
          content: product.content, // 加上这一行
          price: product.price,
          buttonText: product.buttonText,
          aimCurrencyDisplay: product.aimCurrencyDisplay
        });
      }
    });

    console.log('MultiProductTemplate - handleProductsContent - 处理完成，商品数据:', products);
    return products;
  }

  /**
   * 根据用户设置生成正确的多商品模板提交数据
   * @param {Object} settingsData 用户设置的数据
   * @returns {Array} 生成的内容数组
   */
  static generateSubmitData(settingsData) {
    console.log('MultiProductTemplate - 生成提交数据:', settingsData);
    
    if (!settingsData) {
      console.warn('MultiProductTemplate - 设置数据为空');
      return [];
    }
    
    const contents = [];
    
    // 1. 主标题 - positionNumber: 1
    contents.push({
      type: 'text',
      content: settingsData.headerTitle || '精选商品',
      positionNumber: 1,
      isTextTitle: 1,
    });
    
    // 2. 副标题 - positionNumber: 2  
    contents.push({
      type: 'text',
      content: settingsData.headerSubtitle || '为您推荐',
      positionNumber: 2,
      isTextTitle: 0,
    });
    
    // 3. 箭头按钮 - positionNumber: 3
    contents.push({
      type: 'button',
      content: '箭头 点击跳转',
      positionNumber: 3,
      isTextTitle: 0,
      visible: 0 // 箭头默认隐藏
    });
    
    // 4. 处理商品数据
    if (settingsData.products && Array.isArray(settingsData.products)) {
      const productCount = settingsData.productCount || 2;
      
      // 只处理非隐藏的商品
      const visibleProducts = settingsData.products.filter(product => !product.hidden);
      
      visibleProducts.forEach((product, index) => {
        const basePositionNumber = 4 + (index * 10); // 每个商品占用10个位置
        const pageLines = 3 + index; // 商品从第3行开始
        
        console.log(`生成第${index + 1}个商品数据，basePositionNumber: ${basePositionNumber}, pageLines: ${pageLines}`);
        
        // 商品图片 - positionNumber: 4, 14, 24
        const imageContent = {
          type: 'image',
          src: product.imageSrc || product.image || '',
          content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内',
          positionNumber: basePositionNumber,
          isTextTitle: 0,
        };
        
        // 添加图片点击事件
        // if (product.imageClickEvent && product.imageClickEvent.type !== 'OPEN_BROWSER' && product.imageClickEvent.type !== 'none') {
        //   const clickEvent = product.imageClickEvent;
          
        //   // 使用统一的转换器和生成器
        //   imageContent.actionType = ClickEventTypeConverter.toActionType(clickEvent.type);
        //   imageContent.actionJson = ActionJsonGenerator.fromClickEventSettings(clickEvent);
        // } else {
        //   // 默认情况使用标准的默认值
        //   imageContent.actionType = 'OPEN_BROWSER';
        //   imageContent.actionJson = ActionJsonGenerator.fromClickEventSettings(this.getDefaultClickEvent());
        // }
        
        contents.push(imageContent);
        
        // 隐藏优惠价格的粉红色背景 - positionNumber: 5, 15, 25
        contents.push({
          type: 'image',
          content: '隐藏优惠价格的粉红色背景',
          positionNumber: basePositionNumber + 1,
          isTextTitle: 0,
          visible: product.hidden ? 1 : 0
        });
        
        // 隐藏优惠价格（直接传递内容，不做特殊控制） - positionNumber: 6, 16, 26
        contents.push({
          type: 'text',
          content: '隐藏优惠价格',
          positionNumber: basePositionNumber + 2,
          isTextTitle: 0,
          visible: product.hidden ? 1 : 0
        });
        
        // 商品标题 - positionNumber: 7, 17, 27
        contents.push({
          type: 'text',
          content: product.titleText || product.title || '',
          positionNumber: basePositionNumber + 3,
          isTextTitle: 0,
        });
        
        // 商品标签 - positionNumber: 8, 18, 28
        contents.push({
          type: 'text',
          content: product.tagText || product.tag || '',
          positionNumber: basePositionNumber + 4,
          isTextTitle: 0,
        });
        
        // 商品标签2 - positionNumber: 9, 19, 29
        contents.push({
          type: 'text',
          content: '编辑文本',
          positionNumber: basePositionNumber + 5,
          isTextTitle: 0,
          visible: product.hidden ? 1 : 0
        });
        
        // 商品价格（使用 visible 控制货币符号显示） - positionNumber: 10, 20, 30
        contents.push({
          type: 'text',
          content: '货币单位',
          positionNumber: basePositionNumber + 6,
          isTextTitle: 0,
          visible: product.aimCurrencyDisplay !== undefined ? product.aimCurrencyDisplay : 1
        });
        
        // 实际金额 - positionNumber: 11, 21, 31
        contents.push({
          type: 'text',
          content: product.priceText || product.price || '',
          positionNumber: basePositionNumber + 7,
          isTextTitle: 0,
        });
        
        // 按钮文本 - positionNumber: 12, 22, 32
        contents.push({
          type: 'text',
          content: product.buttonText || '立即购买',
          positionNumber: basePositionNumber + 8,
          isTextTitle: 0,
        });
        
        // 按钮点击事件 - positionNumber: 13, 23, 33
        const buttonContent = {
          type: 'button',
          content: product.buttonText || '立即购买',
          positionNumber: basePositionNumber + 9,
          isTextTitle: 0,
        };
        
        // 添加按钮点击事件
        if (product.buttonClickEvent && product.buttonClickEvent.type !== 'OPEN_BROWSER' && product.buttonClickEvent.type !== 'none') {
          const clickEvent = product.buttonClickEvent;
          
          // 使用统一的转换器和生成器
          buttonContent.actionType = ClickEventTypeConverter.toActionType(clickEvent.type);
          buttonContent.actionJson = ActionJsonGenerator.fromClickEventSettings(clickEvent);
        } else {
          // 默认情况使用标准的默认值
          buttonContent.actionType = 'OPEN_BROWSER';
          buttonContent.actionJson = ActionJsonGenerator.fromClickEventSettings(this.getDefaultClickEvent());
        }
        
        // 如果有 actionJson 数据，直接使用
        if (product.actionJson) {
          buttonContent.actionJson = product.actionJson;
        }
        
        contents.push(buttonContent);
      });
    }
    
    console.log('MultiProductTemplate - 生成的提交数据:', contents);
    return contents;
  }
} 