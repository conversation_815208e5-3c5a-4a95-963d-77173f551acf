/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ButtonElement: typeof import('./../components/template/preview/elements/ButtonElement.vue')['default']
    CardVoucherSettings: typeof import('./../components/settings/CardVoucherSettings.vue')['default']
    CardVoucherTemplateRenderer: typeof import('./../components/template/preview/renderers/CardVoucherTemplateRenderer.vue')['default']
    CarouselElement: typeof import('./../components/template/preview/elements/CarouselElement.vue')['default']
    CarouselSettings: typeof import('./../components/settings/CarouselSettings.vue')['default']
    ClickEventEditor: typeof import('./../components/settings/ClickEventEditor.vue')['default']
    ClickEventSettings: typeof import('./../components/settings/ClickEventSettings.vue')['default']
    CloseConfirmDialog: typeof import('./../components/template/dialogs/CloseConfirmDialog.vue')['default']
    ContinueEditDialog: typeof import('./../components/template/dialogs/ContinueEditDialog.vue')['default']
    CouponProductSettings: typeof import('./../components/settings/CouponProductSettings.vue')['default']
    CouponProductTemplateRenderer: typeof import('./../components/template/preview/renderers/CouponProductTemplateRenderer.vue')['default']
    EcommerceSettings: typeof import('./../components/settings/EcommerceSettings.vue')['default']
    EcommerceTemplateRenderer: typeof import('./../components/template/preview/renderers/EcommerceTemplateRenderer.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCarousel: typeof import('element-plus/es')['ElCarousel']
    ElCarouselItem: typeof import('element-plus/es')['ElCarouselItem']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    FactoryList: typeof import('./../components/settings/FactoryList.vue')['default']
    HorizontalSwipeElement: typeof import('./../components/template/preview/elements/HorizontalSwipeElement.vue')['default']
    HorizontalSwipeSettings: typeof import('./../components/settings/HorizontalSwipeSettings.vue')['default']
    HorizontalSwipeTemplateRenderer: typeof import('./../components/template/preview/renderers/HorizontalSwipeTemplateRenderer.vue')['default']
    ImageElement: typeof import('./../components/template/preview/elements/ImageElement.vue')['default']
    ImageSettings: typeof import('./../components/settings/ImageSettings.vue')['default']
    LongTextSettings: typeof import('./../components/settings/LongTextSettings.vue')['default']
    LongTextTemplateRenderer: typeof import('./../components/template/preview/renderers/LongTextTemplateRenderer.vue')['default']
    MediaCategory: typeof import('./../components/MediaCategory.vue')['default']
    MediaSelectorDialog: typeof import('./../components/MediaSelectorDialog.vue')['default']
    MultiProductSettings: typeof import('./../components/settings/MultiProductSettings.vue')['default']
    MultiProductTemplateRenderer: typeof import('./../components/template/preview/renderers/MultiProductTemplateRenderer.vue')['default']
    MultiTextSettings: typeof import('./../components/settings/MultiTextSettings.vue')['default']
    MultiTextTemplateRenderer: typeof import('./../components/template/preview/renderers/MultiTextTemplateRenderer.vue')['default']
    NotificationSettings: typeof import('./../components/settings/NotificationSettings.vue')['default']
    NotificationTemplateRenderer: typeof import('./../components/template/preview/renderers/NotificationTemplateRenderer.vue')['default']
    OppoRedPacketSetting: typeof import('./../components/settings/OppoRedPacketSetting.vue')['default']
    ParamEditableInput: typeof import('./../components/common/ParamEditableInput.vue')['default']
    ParamManageDialog: typeof import('./../components/settings/ParamManageDialog.vue')['default']
    ParamOperations: typeof import('./../components/common/ParamOperations.vue')['default']
    ParamSettings: typeof import('./../components/settings/ParamSettings.vue')['default']
    PreviewSubmitDialog: typeof import('./../components/PreviewSubmitDialog.vue')['default']
    RedPacketTemplateRenderer: typeof import('./../components/template/preview/renderers/RedPacketTemplateRenderer.vue')['default']
    RichParamInput: typeof import('./../components/richtext/RichParamInput.vue')['default']
    SendLinkDialog: typeof import('./../components/sendLinkDialog.vue')['default']
    ShortLinkDialog: typeof import('./../components/ShortLinkDialog.vue')['default']
    StandardTemplateRenderer: typeof import('./../components/template/preview/renderers/StandardTemplateRenderer.vue')['default']
    TemplateCard: typeof import('./../components/template/list/TemplateCard.vue')['default']
    TemplateDetail: typeof import('./../components/TemplateDetail.vue')['default']
    TemplateEditor: typeof import('./../components/template/core/TemplateEditor.vue')['default']
    TemplateHeader: typeof import('./../components/template/core/TemplateHeader.vue')['default']
    TemplateList: typeof import('./../components/TemplateList.vue')['default']
    TemplatePreviewContainer: typeof import('./../components/template/preview/TemplatePreviewContainer.vue')['default']
    TemplatePreviewCore: typeof import('./../components/template/preview/TemplatePreviewCore.vue')['default']
    TemplateSettings: typeof import('./../components/template/core/TemplateSettings.vue')['default']
    TemplateSidebar: typeof import('./../components/template/core/TemplateSidebar.vue')['default']
    TemplateSwitchDialog: typeof import('./../components/template/dialogs/TemplateSwitchDialog.vue')['default']
    TextElement: typeof import('./../components/template/preview/elements/TextElement.vue')['default']
  }
}
