import { createApp } from 'vue';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import 'element-plus/theme-chalk/src/message-box.scss';
import 'element-plus/theme-chalk/src/message.scss';
import 'element-plus/theme-chalk/el-loading.css';

import zhCn from 'element-plus/es/locale/lang/zh-cn'
import App from './index.vue';
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
// import setupPlugins from '@/plugins';
import "@/styles/index.scss";
import { createPinia } from 'pinia';


// 创建Pinia实例
const pinia = createPinia();

// 创建Vue应用实例
const app = createApp(App);

// 使用插件
app.use(pinia);
app.use(ElementPlus, {
  locale: zhCn
});

// 注册所有Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

// 挂载应用
app.mount('#app');