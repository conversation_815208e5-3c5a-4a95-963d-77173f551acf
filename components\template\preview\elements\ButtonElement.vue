<template>
  <div
    :class="['preview-button', { 'selected': isSelected }]"
    @click="handleClick"
  >
    <button 
      class="button-element" 
      v-html="formattedContent"
    ></button>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';

const props = defineProps({
  content: {
    type: Object,
    required: true
  },
  isSelected: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:content', 'select']);

// 按钮元素引用
const buttonRef = ref(null);

// 格式化内容，支持参数显示
const formattedContent = computed(() => {
  if (!props.content || !props.content.content) {
    return getDefaultButtonText();
  }
  
  // 如果内容中已包含格式化的参数按钮元素，直接返回
  if (props.content.content.includes('<input type="button" class="j-btn param-input"')) {
    return props.content.content;
  }
  
  // 格式化参数文本，将 {#paramX#} 转换为按钮元素
  return props.content.content.replace(/{#param(\d+)#}/g, (match, paramId) => {
    return `<input type="button" class="j-btn param-input" value="${match}" data-param-id="${paramId}" readonly unselectable="on">`;
  });
});

// 获取默认按钮文本，根据模板类型判断
const getDefaultButtonText = () => {
  // 检查是否是红包模板
  if (window.TEMPLATE_IS_REDPACKET) {
    return '立即领取';
  }
  
  // 其他模板场景
  return '编辑按钮';
};

// 处理点击事件
const handleClick = (event) => {
  // 阻止默认行为但允许事件冒泡
  event.preventDefault();
  
  // 触发选择事件，打开按钮设置面板
  emit('select', props.content);
};

// 插入参数
const insertParam = (paramText) => {
  try {
    console.log('ButtonElement 开始处理参数插入:', paramText);
    
    // 获取参数ID
    let paramId = null;
    
    // 判断参数类型
    if (typeof paramText === 'object' && paramText.paramId) {
      // 如果是包含paramId的对象
      paramId = paramText.paramId;
      console.log('从参数对象中提取到参数ID:', paramId);
    } else if (typeof paramText === 'string' && paramText.includes('{#param')) {
      // 如果是参数文本格式
      const match = paramText.match(/{#param(\d+)#}/);
      if (match && match[1]) {
        paramId = match[1];
        console.log('从参数文本中提取到参数ID:', paramId);
      }
    } else if (typeof paramText === 'string') {
      // 如果是纯数字字符串，直接使用
      paramId = paramText.trim();
    }
    
    // 如果没有有效的参数ID，则从全局参数管理器获取
    if (!paramId || isNaN(parseInt(paramId))) {
      console.log('未找到有效的参数ID，将从全局参数管理器获取');
      
      // 尝试使用参数服务获取下一个ID
      try {
        const paramService = window.PARAM_SERVICE_INSTANCE;
        if (paramService && typeof paramService.getNextParamId === 'function') {
          paramId = paramService.getNextParamId(`button-element-${props.content.id}`);
          console.log('从参数服务获取ID:', paramId);
        } 
        // 如果没有参数服务实例，尝试全局参数管理器
        else if (window.GLOBAL_PARAM_MANAGER && typeof window.GLOBAL_PARAM_MANAGER.getNextId === 'function') {
          paramId = window.GLOBAL_PARAM_MANAGER.getNextId();
          console.log('从全局参数管理器获取ID:', paramId);
        } 
        // 如果都失败，使用全局函数
        else if (typeof window.getNextAvailableParamId === 'function') {
          paramId = window.getNextAvailableParamId();
          console.log('使用全局函数获取ID:', paramId);
        } else {
          // 默认使用1
          paramId = "1";
        }
      } catch (error) {
        console.error('获取参数ID失败:', error);
        // 默认使用1
        paramId = "1";
      }
    }
    
    // 检查当前按钮中参数数量，限制最多7个
    const content = props.content.content || '';
    const paramMatches = content.match(/{#param\d+#}/g) || [];
    if (paramMatches.length >= 7) {
      console.warn('按钮中最多只能插入7个参数，当前已达上限');
      return false;
    }
    
    // 创建新内容，替换或添加参数
    let newContent;
    if (!content) {
      // 如果没有内容，直接使用参数
      newContent = `{#param${paramId}#}`;
    } else {
      // 如果已有内容，添加参数
      newContent = `${content} {#param${paramId}#}`;
    }
    
    // 更新内容
    emit('update:content', { 
      ...props.content, 
      content: newContent 
    });
    
    // 记录参数使用
    try {
      // 尝试获取参数服务实例进行记录
      const paramService = window.PARAM_SERVICE_INSTANCE;
      if (paramService && typeof paramService.recordParamUsage === 'function') {
        paramService.recordParamUsage(paramId);
      }
      // 或者使用全局参数管理器
      else if (window.GLOBAL_PARAM_MANAGER && typeof window.GLOBAL_PARAM_MANAGER.recordParamUsage === 'function') {
        window.GLOBAL_PARAM_MANAGER.recordParamUsage(paramId);
      }
    } catch (error) {
      console.error('记录参数使用失败:', error);
    }
    
    console.log('参数插入成功:', paramId);
    return true;
  } catch (error) {
    console.error('插入参数失败:', error);
    return false;
  }
};

// 组件初始化
onMounted(() => {
  // 创建全局引用，便于通过设置面板访问
  if (!window.BUTTON_ELEMENT_REFS) {
    window.BUTTON_ELEMENT_REFS = [];
  }
  
  // 添加引用到全局数组
  window.BUTTON_ELEMENT_REFS.push({
    ref: buttonRef,
    id: props.content.id,
    insertParam: (paramText) => insertParam(paramText)
  });

  // 处理组件内参数，更新全局参数管理器
  collectParamsAndUpdate();
  
  // 监听参数插入事件
  if (window.PARAM_EVENT_BUS) {
    window.PARAM_EVENT_BUS.on('insert-param', (event) => {
      // 只有当前组件被选中时才处理参数插入
      if (props.isSelected) {
        console.log('ButtonElement 收到参数插入事件', event);
        
        // 获取参数ID
        let paramId = null;
        if (event && event.paramId) {
          paramId = event.paramId;
        } else if (event && event.param && event.param.id) {
          paramId = event.param.id;
        }
        
        if (paramId) {
          insertParam(paramId);
        }
      }
    });
  }
});

// 收集当前组件中的参数并更新全局参数管理器
const collectParamsAndUpdate = () => {
  try {
    // 尝试获取参数服务实例
    const paramService = window.PARAM_SERVICE_INSTANCE;
    if (paramService && typeof paramService.collectParamsFromDOM === 'function') {
      paramService.collectParamsFromDOM();
      return;
    }
    
    // 如果没有参数服务实例或方法不可用，使用全局参数管理器
    if (!window.GLOBAL_PARAM_MANAGER) return;
    
    // 查找内容中的所有参数
    if (props.content && props.content.content) {
      const paramRegex = /{#param(\d+)#}/g;
      let match;
      while ((match = paramRegex.exec(props.content.content)) !== null) {
        const paramId = parseInt(match[1]);
        if (!isNaN(paramId)) {
          // 将找到的参数ID添加到全局参数管理器
          window.GLOBAL_PARAM_MANAGER.recordParamUsage(paramId);
        }
      }
    }
  } catch (error) {
    console.error('收集参数并更新全局参数管理器时出错:', error);
  }
};

// 监听内容变化，更新参数管理
watch(() => props.content.content, (newContent) => {
  collectParamsAndUpdate();
}, { immediate: true });

// 组件卸载
onBeforeUnmount(() => {
  // 移除全局引用
  if (window.BUTTON_ELEMENT_REFS) {
    const index = window.BUTTON_ELEMENT_REFS.findIndex(item => 
      item.id === props.content.id);
    if (index !== -1) {
      window.BUTTON_ELEMENT_REFS.splice(index, 1);
    }
  }
});
</script>

<style scoped lang="scss">
.preview-button {
  margin: 10px 14px 20px;
  position: relative;
  text-align: center;
}

.button-element {
  width: 100%;
  height: 38px;
  background-color: #2878ff;
  color: white;
  border: none;
  border-radius: 18px;
  padding: 0 16px;
  font-size: 16px;
  cursor: pointer;
  outline: none;
  /* box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1); */
}

.selected .button-element {
  outline: 1px dashed #409eff;
  outline-offset: 2px;
}

:deep(input.j-btn), :deep(span.j-btn) {
  border: none !important;
  border-radius: 0 !important;
  background: none !important;
  color: inherit;
  margin: 0 3px;
  font-size: inherit;
  line-height: inherit;
  cursor: default;
  padding: 0;
  display: inline-block;
  vertical-align: baseline;
  height: auto;
  min-height: auto;
  font-weight: inherit;
}

:deep(input.j-btn:hover), 
:deep(input.j-btn:focus),
:deep(span.j-btn:hover),
:deep(span.j-btn:focus) {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
  background: none !important;
}
</style> 