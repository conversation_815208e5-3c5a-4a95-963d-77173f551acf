/**
 * 模板基类
 * 定义所有模板类型的通用方法和接口
 */

// 简单的UUID生成函数
function uuidv4() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

class BaseTemplate {
  /**
   * 根据模板数据初始化模板内容
   * @param {Object} template 模板数据
   * @returns {Array} 初始化后的模板内容数组
   */
  static initializeContents(template) {
    if (!template) return [];
    
    // 如果模板已经有pages属性，使用现有数据
    if (template.pages && Array.isArray(template.pages) && template.pages.length > 0) {
      const page = template.pages[0]; // 当前只处理第一页
      
      if (page.contents && Array.isArray(page.contents)) {
        // 给每个内容元素添加唯一ID和其他必要属性
        return page.contents.map(content => this.processContentItem(content));
      }
    }
    
    // 如果没有数据，返回空数组
    return [];
  }
  
  /**
   * 处理单个内容元素，给其添加必要的属性
   * @param {Object} content 内容元素
   * @returns {Object} 处理后的内容元素
   */
  static processContentItem(content) {
    // 添加唯一内容ID，如果没有的话
    if (!content.contentId) {
      content.contentId = uuidv4();
    }
    
    // 根据内容类型添加特定属性
    switch (content.type) {
      case 'text':
        // 文本内容需要编辑状态和默认值
        content.isEditing = false;
        content.editContent = content.content || '';
        break;
        
      case 'image':
      case 'background':
      case 'video':
        // 媒体内容需要保存原始默认源
        if (content.src && !content.defaultSrc) {
          content.defaultSrc = content.src;
        }
        break;
        
      case 'button':
        // 按钮需要点击事件类型和目标
        if (!content.clickEvent) {
          content.clickEvent = {
            type: 'OPEN_BROWSER',
            target: '',
            data: {}
          };
        }
        break;
      
      default:
        break;
    }
    
    return content;
  }
  
  /**
   * 校验模板内容的完整性
   * @param {Array} contents 模板内容数组
   * @returns {boolean} 是否通过验证
   */
  static validateContents(contents) {
    if (!Array.isArray(contents) || contents.length === 0) {
      return false;
    }
    
    // 每个内容必须有类型和内容ID
    return contents.every(content => {
      return content.type && content.contentId;
    });
  }
  
  /**
   * 将内容数组转换为API提交格式
   * @param {Array} contents 模板内容数组
   * @returns {Array} API格式的内容数组
   */
  static formatContentsForSubmit(contents) {
    if (!Array.isArray(contents)) return [];
    
    return contents.map(content => {
      // 创建内容的浅拷贝
      const formattedContent = { ...content };
      
      // 移除编辑器特定的属性
      delete formattedContent.isEditing;
      delete formattedContent.editContent;
      delete formattedContent.defaultSrc;
      
      // 对于文本类型，确保内容字段被更新
      if (formattedContent.type === 'text' && formattedContent.editContent !== undefined) {
        formattedContent.content = formattedContent.editContent;
      }
      
      return formattedContent;
    });
  }
}

export default BaseTemplate; 