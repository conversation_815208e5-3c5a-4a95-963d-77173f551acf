<template>
  <div class="media-category">
    <div v-for="(app, appIndex) in processedData" :key="appIndex" @mouseenter="showAddIcon(appIndex)" @mouseleave="hideAddIcon(appIndex)">
      <div class="parent-category flex-row-start">
        <el-checkbox 
          v-model="checkedAppNames[app.appName]" 
          :indeterminate="parentIndeterminate[app.appName]" 
          @change="handleAppCheckChange(app.appName, $event)" 
          @click.stop
        />
        <div class="parent-category-name" @click="toggleSubElements(appIndex)">
          {{ app.appName }}
        </div>
        <span v-show="isAddButtonVisible[app.appName]" class="add-icon" @click.stop="addData(app.appName, appIndex)">
          <el-icon><CirclePlus /></el-icon>
        </span>
      </div>
      <div v-show="isSubElementsVisible[appIndex]" class="sub-categories">
        <div 
          v-for="(sub, subIndex) in getValidDirList(app.dirNameList)" 
          :key="sub.dirId || subIndex"
          class="sub-category"
          @mouseenter="showDeleteIcon(app.appName, subIndex)" 
          @mouseleave="hideDeleteIcon(app.appName, subIndex)"
        >
          <el-checkbox
            v-model="checkedDirNames[app.appName][subIndex]"
            @change="handleSubCheckChange(app.appName, subIndex, $event)"
          />
          <div 
            v-if="!sub.isEditing" 
            class="sub-category-name"
            @dblclick="startEditing(app.appName, subIndex)"
          >
            {{ sub.dirName }}
          </div>
          <el-input 
            v-else
            v-model="sub.dirName" 
            :ref="el => setInputRef(app.appName, subIndex, el)"
            @blur="handleBlur(app.appName, subIndex, sub.dirId, sub.originalDirName)"
            @keyup.enter="handleBlur(app.appName, subIndex, sub.dirId, sub.originalDirName)"
          />
          <span v-show="isDeleteIconVisible[app.appName][subIndex]" class="delete-icon" @click="deleteItem(app.appName, sub, subIndex)">
            <el-icon><Remove /></el-icon>
          </span>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import api from '@/api/index';
import mockMediaDirList from '@/api/mediaDirList.json';
import mockGetTemBasicList from '@/api/getTemBasicList0.json';

const props = defineProps({
  dirType: {
    type: Number,
    default: 2
  },
  filterByAppKey: {
    type: Boolean,
    default: false
  },
  filterAppKey: {
    type: String,
    default: ''
  },
  subType: {
    type: String,
    default: ''
  }
});

// 声明 update-template-params 事件
const emit = defineEmits(['category-change', 'update-app-key-dir-id', 'update-templates', 'update-template-params']);

const processedData = ref([]);
const checkedAppNames = ref({});
const checkedDirNames = ref({});
const isSubElementsVisible = ref([]);
const isAddButtonVisible = ref({});
const isDeleteIconVisible = ref({});
const inputRefs = ref({});
const parentIndeterminate = ref({});
const templates = ref([]);
// 新增状态，标记是否已经发起过请求
const hasFetched = ref(false); 
let mediaListApiLoaded = false; // 防止重复加载

// 当前和上一次选中的appName，用于比较是否切换了appName
const currentSelectedAppName = ref('');
const previousSelectedAppName = ref('');

// 点击分类名称，展开子元素
const toggleSubElements = async (index) => {
  // 只切换当前点击的分类的展开状态，不影响其他分类
  const wasVisible = isSubElementsVisible.value[index];
  isSubElementsVisible.value[index] = !wasVisible;

  // 如果是展开操作，检查是否只有一个子分类，如果是则自动选中
  if (!wasVisible && isSubElementsVisible.value[index]) {
    const app = processedData.value[index];
    if (app) {
      const validDirList = getValidDirList(app.dirNameList);

      // 如果这个父分类只有一个子分类，自动选中它
      if (validDirList.length === 1) {
        console.log(`展开父分类 "${app.appName}" 发现只有一个子分类，自动选中:`, validDirList[0].dirName);

        // 清空其他父分类的选中状态
        processedData.value.forEach(otherApp => {
          if (otherApp.appName !== app.appName) {
            checkedAppNames.value[otherApp.appName] = false;
            if (checkedDirNames.value[otherApp.appName]) {
              Object.keys(checkedDirNames.value[otherApp.appName]).forEach(idx => {
                checkedDirNames.value[otherApp.appName][idx] = false;
              });
            }
            parentIndeterminate.value[otherApp.appName] = false;
          }
        });

        // 选中这个唯一的子分类
        if (!checkedDirNames.value[app.appName]) {
          checkedDirNames.value[app.appName] = {};
        }
        checkedDirNames.value[app.appName][0] = true;

        // 更新父级勾选框状态
        updateParentCheckbox(app.appName);

        // 设置当前选中的appName
        currentSelectedAppName.value = app.appName;

        // 发送分类变化事件
        emitCategoryChange();

        // 获取子分类信息
        const selectedSub = validDirList[0];
        const subTypeValue = props.subType || selectedSub.subType || '';

        // 更新appKey和dirId
        emit('update-app-key-dir-id', app.appKey, selectedSub.dirId.toString(), subTypeValue);

        // 判断是否为基础版式
        const isBaseTemplate = parseInt(selectedSub.dirId) < 0 ||
                               selectedSub.dirId.toString().includes('base') ||
                               selectedSub.dirId.toString().includes('template');

        console.log('展开自动选中子分类 - 判断是否为基础版式:', isBaseTemplate, 'dirId:', selectedSub.dirId);

        // 获取模板列表数据
        const params = {
          isBase: isBaseTemplate ? 1 : 0,
          appKey: app.appKey,
          dirIds: selectedSub.dirId.toString(),
          page: 1,
          limit: 10,
        };

        try {
          const res = await api.getTemBasicList(params);
          console.log('展开自动选中子分类 - 获取模板列表成功:', res);

          if (res.data && Array.isArray(res.data.list)) {
            const processedTemplates = res.data.list.map(template => {
              if (!template.pages) {
                template.pages = [];
              }
              if (template.channels && template.channels.length > 0 && template.channels[0].factoryinfos) {
                template.factoryInfos = template.channels[0].factoryinfos;
              }
              return template;
            });

            // 更新模板列表
            emit('update-templates', {
              list: processedTemplates,
              total: res.data.total || 0,
              hasSubCategories: true
            }, true);

            // 提取动态参数
            const allParams = new Set();
            processedTemplates.forEach(template => {
              if (template.pages && Array.isArray(template.pages)) {
                template.pages.forEach(page => {
                  if (page.contents && Array.isArray(page.contents)) {
                    page.contents.forEach(content => {
                      if (content.content) {
                        const matches = content.content.match(/\{#(.*?)#\}/g);
                        if (matches) {
                          matches.forEach(match => {
                            const paramName = match.replace(/\{#|#\}/g, '');
                            allParams.add(paramName);
                          });
                        }
                      }
                    });
                  }
                });
              }
            });
            emit('update-template-params', Array.from(allParams).map(name => ({ name })));
          }
        } catch (error) {
          console.error('展开自动选中子分类 - 获取模板列表失败:', error);
          emit('update-templates', {
            list: [],
            total: 0,
            hasSubCategories: true
          }, true);
          emit('update-template-params', []);
        }
      }
    }
  }
};



// 获取分类列表
const getMediaDirList = async () => {
  if (mediaListApiLoaded) return;
  
  mediaListApiLoaded = true;
  const params = {
    numType: 1,
    dirType: props.dirType
  };
  
  try {
    console.log('获取分类列表，filterByAppKey:', props.filterByAppKey, 'filterAppKey:', props.filterAppKey);
    // 强制清空hasFetched状态和所有选中项
    hasFetched.value = false;
    templates.value = [];
    currentSelectedAppName.value = '';
    previousSelectedAppName.value = '';
    
    // 清空所有选中状态
    Object.keys(checkedAppNames.value).forEach(key => {
      checkedAppNames.value[key] = false;
    });
    
    Object.keys(checkedDirNames.value).forEach(appName => {
      Object.keys(checkedDirNames.value[appName] || {}).forEach(subIndex => {
        checkedDirNames.value[appName][subIndex] = false;
      });
    });
    
    // 清空emit之前的旧数据
    emit('update-templates', [], true);
    emit('update-template-params', []);
    
    const res = await api.getMediaDirList(params);
    const originalData = res.data;

    // const res = mockMediaDirList;
    // const originalData = res.data;


    // 如果设置了按appKey过滤，且传入了appKey，则只保留对应appKey的分类
    let filteredData = originalData;
    if (props.filterByAppKey && props.filterAppKey) {
      filteredData = originalData.filter(item => item.appKey === props.filterAppKey);
      console.log('按appKey过滤后的分类数据, 原始数量:', originalData.length, '过滤后数量:', filteredData.length, 'appKey:', props.filterAppKey);
    }
    
    // 首次清空之前的所有分类数据
    processedData.value = [];
    checkedAppNames.value = {};
    checkedDirNames.value = {};
    isSubElementsVisible.value = [];
    isAddButtonVisible.value = {};
    isDeleteIconVisible.value = {};
    parentIndeterminate.value = {};
    inputRefs.value = {};
    
    // 处理数据
    const processed = filteredData.reduce((acc, item) => {
      const existingApp = acc.find((app) => app.appName === item.appName);
      if (existingApp) {
        existingApp.dirNameList.push({
          dirName: item.dirName,
          dirId: item.dirId,
          originalDirName: item.dirName,
          subType: item.subType || '', // 添加subType
        });
      } else {
        acc.push({
          appName: item.appName,
          channelTypes: item.channelTypes,
          appKey: item.appKey,
          dirId: item.dirId,
          dirNameList: [
            {
              dirName: item.dirName,
              dirId: item.dirId,
              originalDirName: item.dirName,
              subType: item.subType || '', // 添加subType
            },
          ],
        });
      }
      return acc;
    }, []);
    
    processedData.value = processed;
    
    // 初始化状态
    processed.forEach((app, index) => {
      checkedAppNames.value[app.appName] = false;
      checkedDirNames.value[app.appName] = {};
      isSubElementsVisible.value[index] = false;
      isAddButtonVisible.value[app.appName] = false;
      isDeleteIconVisible.value[app.appName] = {};
      parentIndeterminate.value[app.appName] = false;
      app.dirNameList.forEach((_, subIndex) => {
        checkedDirNames.value[app.appName][subIndex] = false;
        isDeleteIconVisible.value[app.appName][subIndex] = false;
      });
      inputRefs.value[app.appName] = {};
    });
    // 只在需要时更新appKey（不触发数据请求）
    if (processed.length > 0) {
      const firstApp = processed[0];
      console.log('更新appKey信息，但不自动选择分类或请求数据, appKey:', firstApp.appKey);
      // 不传递dirId，防止触发数据请求
      emit('update-app-key-dir-id', firstApp.appKey, '');
    } else {
      console.log('过滤后没有可用的分类数据');
      // 清空模板列表
      templates.value = [];
      hasFetched.value = false;
      emit('update-templates', [], true);
    }
  } catch (error) {
    console.error("获取分类列表失败:", error);
  } finally {
    mediaListApiLoaded = false;
  }
};

// 处理应用勾选变化
const handleAppCheckChange = async (appName, checked) => {
  console.log('处理父级分类勾选变化，appName:', appName, 'checked:', checked);
  
  // 把其他父级勾选状态全部设为false
  processedData.value.forEach(app => {
    if (app.appName !== appName) {
      checkedAppNames.value[app.appName] = false;
      if (checkedDirNames.value[app.appName]) {
        Object.keys(checkedDirNames.value[app.appName]).forEach(subIndex => {
          checkedDirNames.value[app.appName][subIndex] = false;
        });
      }
      parentIndeterminate.value[app.appName] = false;
    }
  });

  const app = processedData.value.find(app => app.appName === appName);
  if (app) {
    // 如果是切换到新的appName，进行状态重置
    const isNewAppName = currentSelectedAppName.value !== appName;
    if (isNewAppName) {
      console.log('切换到新的appName(父分类)，重置状态');
      previousSelectedAppName.value = currentSelectedAppName.value;
      currentSelectedAppName.value = checked ? appName : '';
      
      // 清空模板列表和重置状态
      templates.value = [];
      emit('update-templates', [], true);
      emit('update-template-params', []);
      hasFetched.value = false;
    } else if (!checked) {
      // 如果是当前选中的appName被取消选中，清空当前选中的appName
      currentSelectedAppName.value = '';
    }
    
    // 获取有效的子分类列表
    const validDirList = getValidDirList(app.dirNameList);
    
    // 设置所有子级勾选框状态与父级一致
    validDirList.forEach((_, index) => {
      if (!checkedDirNames.value[appName]) {
        checkedDirNames.value[appName] = {};
      }
      checkedDirNames.value[appName][index] = checked;
    });
    
    // 更新父级勾选框的状态
    updateParentCheckbox(appName);
    
    // 发送分类变化事件
    emitCategoryChange();
    
    if (checked) {
      // 展开当前分类
      const index = processedData.value.findIndex(app => app.appName === appName);
      if (index !== -1) {
        isSubElementsVisible.value[index] = true;
      }
      
      // 检查是否有有效的子分类
      const validDirIds = validDirList
        .filter(dir => dir.dirId !== null && dir.dirId !== undefined)
        .map(dir => dir.dirId);
      
      // 使用props中的subType值
      const subTypeValue = props.subType || '';
      
      // 更新appKey和dirId
      emit('update-app-key-dir-id', app.appKey, validDirIds.length > 0 ? validDirIds.join(',') : '-1', subTypeValue);
      
      // 判断是否为基础版式：检查dirId是否为负数或特殊标识
      const isBaseTemplate = validDirIds.some(dirId => {
        const numericDirId = parseInt(dirId);
        return numericDirId < 0 || dirId.toString().includes('base') || dirId.toString().includes('template');
      });
      
      console.log('判断是否为基础版式:', isBaseTemplate, '选中的dirIds:', validDirIds);
      
      // 获取模板列表数据
      const params = {
        isBase: isBaseTemplate ? 1 : 0, // 根据版式类型动态设置isBase
        appKey: app.appKey,
        dirIds: validDirIds.length > 0 ? validDirIds.join(',') : '-1',
        page: 1,
        limit: 10,
      };
      
      try {
        const res = await api.getTemBasicList(params);
        // const res = mockGetTemBasicList;
        console.log('获取模板列表成功:', res);
        if (res.data && Array.isArray(res.data.list)) {
          const processedTemplates = res.data.list.map(template => {
            if (!template.pages) {
              template.pages = [];
            }
            if (template.channels && template.channels.length > 0 && template.channels[0].factoryinfos) {
              template.factoryInfos = template.channels[0].factoryinfos;
            }
            return template;
          });
          
          // 更新模板列表和总数
          emit('update-templates', {
            list: processedTemplates,
            total: res.data.total || 0
          }, true);

          // 提取动态参数
          const allParams = new Set();
          processedTemplates.forEach(template => {
            if (template.pages && Array.isArray(template.pages)) {
              template.pages.forEach(page => {
                if (page.contents && Array.isArray(page.contents)) {
                  page.contents.forEach(content => {
                    if (content.content) {
                      const matches = content.content.match(/\{#(.*?)#\}/g);
                      if (matches) {
                        matches.forEach(match => {
                          const paramName = match.replace(/\{#|#\}/g, '');
                          allParams.add(paramName);
                        });
                      }
                    }
                  });
                }
              });
            }
          });
          emit('update-template-params', Array.from(allParams).map(name => ({ name })));
        }
      } catch (error) {
        console.error('获取模板列表失败:', error);
        emit('update-templates', [], true);
        emit('update-template-params', []);
      }
    } else {
      // 如果取消选中，清空模板列表
      templates.value = [];
      emit('update-templates', [], true);
      emit('update-template-params', []);
    }
  }
};

// 更新父级勾选框状态
const updateParentCheckbox = (appName) => {
  const app = processedData.value.find(app => app.appName === appName);
  if (app) {
    const validDirList = getValidDirList(app.dirNameList);
    const checkedCount = validDirList.filter((_, index) => 
      checkedDirNames.value[appName][index]
    ).length;
    
    // 只有当有子分类时才设置父级勾选状态
    if (validDirList.length > 0) {
      checkedAppNames.value[appName] = checkedCount === validDirList.length;
      parentIndeterminate.value[appName] = checkedCount > 0 && checkedCount < validDirList.length;
    } else {
      // 如果没有子分类，父级勾选状态跟随用户操作
      parentIndeterminate.value[appName] = false;
    }
  }
};

// 处理子分类勾选变化
const handleSubCheckChange = async (appName, subIndex, checked) => {
  console.log('处理子分类勾选变化，appName:', appName, 'subIndex:', subIndex, 'checked:', checked);
  
  const app = processedData.value.find(app => app.appName === appName);
  if (!app) return;
  
  // 确保 checkedDirNames[appName] 存在
  if (!checkedDirNames.value[appName]) {
    checkedDirNames.value[appName] = {};
  }
  
  // 如果是选中操作，先清空其他父级的选中状态
  if (checked) {
    processedData.value.forEach(otherApp => {
      if (otherApp.appName !== appName) {
        // 清空其他父级的选中状态
        checkedAppNames.value[otherApp.appName] = false;
        if (checkedDirNames.value[otherApp.appName]) {
          Object.keys(checkedDirNames.value[otherApp.appName]).forEach(idx => {
            checkedDirNames.value[otherApp.appName][idx] = false;
          });
        }
        // 更新其他父级的状态
        updateParentCheckbox(otherApp.appName);
      }
    });
  }
  
  // 设置当前子分类的选中状态
  checkedDirNames.value[appName][subIndex] = checked;
  
  // 更新父级勾选框状态
  updateParentCheckbox(appName);
  
  // 发送分类变化事件
  emitCategoryChange();
  
  // 获取当前父级下所有选中的子分类
  const selectedSubs = app.dirNameList.filter((_, index) => checkedDirNames.value[app.appName][index]);
  
  if (selectedSubs.length > 0) {
    // 收集所有选中子分类的dirIds
    const dirIds = selectedSubs.map(sub => sub.dirId).join(',');
    
    // 使用props中的subType值
    const subTypeValue = props.subType || '';
    
    // 更新appKey和dirId，添加subType参数
    emit('update-app-key-dir-id', app.appKey, dirIds, subTypeValue);
    
    // 判断是否为基础版式：检查dirId是否为负数或特殊标识
    const selectedDirIds = selectedSubs.map(sub => sub.dirId);
    const isBaseTemplate = selectedDirIds.some(dirId => {
      const numericDirId = parseInt(dirId);
      return numericDirId < 0 || dirId.toString().includes('base') || dirId.toString().includes('template');
    });
    
    console.log('子分类选择变化 - 判断是否为基础版式:', isBaseTemplate, '选中的dirIds:', selectedDirIds);
    
    // 获取模板列表数据
    const params = {
      isBase: isBaseTemplate ? 1 : 0, // 根据版式类型动态设置isBase
      appKey: app.appKey,
      dirIds: dirIds,
      page: 1,
      limit: 10,
    };
    
    try {
      const res = await api.getTemBasicList(params);
      // const res = mockGetTemBasicList;
      const processedTemplates = [];
      if (res.data && Array.isArray(res.data.list)) {
        processedTemplates.push(...res.data.list.map(template => {
          if (!template.pages) {
            template.pages = [];
          }
          if (template.channels && template.channels.length > 0 && template.channels[0].factoryinfos) {
            template.factoryInfos = template.channels[0].factoryinfos;
          }
          return template;
        }));
      }
      
      emit('update-templates', {
        list: processedTemplates,
        total: res.data?.total || 0,
        hasSubCategories: true
      }, true);
    } catch (error) {
      console.error('获取模板列表失败:', error);
      emit('update-templates', {
        list: [],
        total: 0,
        hasSubCategories: true
      }, true);
    }
  } else {
    // 取消选中时，清空相关状态
    emit('update-app-key-dir-id', '', '', '');
    emit('update-templates', {
      list: [],
      total: 0,
      hasSubCategories: app.dirNameList.length > 0
    }, true);
    emit('update-template-params', []);
  }
};

// 发送分类变化事件
const emitCategoryChange = () => {
  const selectedCategories = [];
  
  processedData.value.forEach(app => {
    // 如果父级被选中，添加所有有效的子分类
    if (checkedAppNames.value[app.appName]) {
      const validDirList = getValidDirList(app.dirNameList);
      validDirList.forEach(sub => {
        selectedCategories.push({
          appKey: app.appKey,
          dirId: sub.dirId
        });
      });
    } else {
      // 否则只添加被选中的子分类
      const validDirList = getValidDirList(app.dirNameList);
      validDirList.forEach((sub, index) => {
        if (checkedDirNames.value[app.appName]?.[index]) {
          selectedCategories.push({
            appKey: app.appKey,
            dirId: sub.dirId
          });
        }
      });
    }
  });
  
  console.log('发送分类变化事件，选中的分类数量:', selectedCategories.length);
  emit('category-change', selectedCategories);
};

// 新增分类
const addData = async (appName, index) => {
  const app = processedData.value.find((a) => a.appName === appName);
  if (app) {
    const randomNumber = Math.floor(Math.random() * 10000);
    const newDirName = `新分类_${randomNumber}`;

    // 定义接口参数
    const params = {
      appKey: app.appKey,
      dirName: newDirName,
      dirType: props.dirType,
      dirMediaType: 1,
    };

    try {
      // 调用接口
      const res = await api.updateDir(params);
      console.log("接口响应:", res);

      // 从返回值中获取 dirId
      const dirId = res.data;

      const newDir = {
        dirName: newDirName,
        dirId: dirId,
        originalDirName: newDirName,
        isEditing: false
      };

      // 确保父级分类展开
      const appIndex = processedData.value.findIndex(a => a.appName === appName);
      if (appIndex !== -1) {
        isSubElementsVisible.value[appIndex] = true;
      }

      // 将新分类添加到 dirNameList 的开头
      app.dirNameList.unshift(newDir);

      // 初始化新分类的状态
      if (!checkedDirNames.value[app.appName]) {
        checkedDirNames.value[app.appName] = {};
      }
      
      // 将所有现有的选中状态向后移动一位
      const newCheckedState = {};
      Object.keys(checkedDirNames.value[app.appName]).forEach(key => {
        newCheckedState[parseInt(key) + 1] = checkedDirNames.value[app.appName][key];
      });
      // 新增的分类默认为未选中
      newCheckedState[0] = false;
      checkedDirNames.value[app.appName] = newCheckedState;

      // 更新父级勾选框状态
      updateParentCheckbox(appName);

      // 如果是在当前选中的父级下新增
      if (currentSelectedAppName.value === appName) {
        // 获取当前父级下所有选中的子分类
        const selectedSubs = app.dirNameList.filter((_, index) => checkedDirNames.value[app.appName][index]);
        
        if (selectedSubs.length > 0) {
          // 如果有选中的子分类，更新模板列表
          const dirIds = selectedSubs.map(sub => sub.dirId).join(',');
          emit('update-app-key-dir-id', app.appKey, dirIds, selectedSubs[0].subType || '');
          
          // 判断是否为基础版式：检查dirId是否为负数或特殊标识
          const selectedDirIds = selectedSubs.map(sub => sub.dirId);
          const isBaseTemplate = selectedDirIds.some(dirId => {
            const numericDirId = parseInt(dirId);
            return numericDirId < 0 || dirId.toString().includes('base') || dirId.toString().includes('template');
          });
          
          console.log('删除分类后 - 判断是否为基础版式:', isBaseTemplate, '选中的dirIds:', selectedDirIds);
          
          // 获取模板列表数据
          const params = {
            isBase: isBaseTemplate ? 1 : 0, // 根据版式类型动态设置isBase
            appKey: app.appKey,
            dirIds: dirIds,
            page: 1,
            limit: 10,
          };
          
          try {
            const res = await api.getTemBasicList(params);
            // const res = mockGetTemBasicList
            if (res.data && Array.isArray(res.data.list)) {
              const processedTemplates = res.data.list.map(template => {
                if (!template.pages) {
                  template.pages = [];
                }
                if (template.channels && template.channels.length > 0 && template.channels[0].factoryinfos) {
                  template.factoryInfos = template.channels[0].factoryinfos;
                }
                return template;
              });
              
              emit('update-templates', {
                list: processedTemplates,
                total: res.data.total || 0,
                hasSubCategories: true
              }, true);
            }
          } catch (error) {
            console.error('获取模板列表失败:', error);
          }
        }
      }

      ElMessage.success("新增分类成功");

      // 等待DOM更新后聚焦到新添加的输入框
      nextTick(() => {
        if (inputRefs.value[appName]?.[0]) {
          inputRefs.value[appName][0].focus();
        }
      });

    } catch (error) {
      console.error("接口调用失败:", error);
      ElMessage.error("新增分类失败");
    }
  }
};

// 修改分类名称
const updateDirName = async (appName, subIndex, dirId, originalDirName) => {
  const app = processedData.value.find(app => app.appName === appName);
  if (app) {
    const newDirName = app.dirNameList[subIndex].dirName.trim();
    // 如果新名称为空，直接返回
    if (!newDirName) {
      ElMessage.warning('分类名称不能为空');
      app.dirNameList[subIndex].dirName = originalDirName;
      return;
    }
    
    const params = {
      appKey: app.appKey,
      dirName: newDirName,
      dirType: props.dirType,
      dirMediaType: 1,
      dirId: dirId
    };
    
    try {
      const res = await api.updateDir(params);
      if (res.code === 0) {
        app.dirNameList[subIndex].originalDirName = newDirName;
        ElMessage.success('更新分类名称成功');
      } else {
        // 更新失败时恢复原名称
        app.dirNameList[subIndex].dirName = originalDirName;
        ElMessage.error('更新分类名称失败');
      }
    } catch (error) {
      console.error("更新分类名称失败:", error);
      // 发生错误时恢复原名称
      app.dirNameList[subIndex].dirName = originalDirName;
      ElMessage.error('更新分类名称失败');
    }
  }
};

// 删除分类
const deleteItem = async (appName, sub, subIndex) => {
  const app = processedData.value.find(app => app.appName === appName);
  if (app) {
    const params = {
      dirId: sub.dirId,
      dirName: sub.dirName,
      dirType: props.dirType,
      dirMediaType: 1
    };
    try {
      const res = await api.deleteDir(params);
      if (res.code === 0) {
        // 删除成功后，更新状态
        app.dirNameList.splice(subIndex, 1);
        
        // 重新整理选中状态的索引
        if (checkedDirNames.value[appName]) {
          const newCheckedState = {};
          let newIndex = 0;
          
          // 遍历原有的选中状态，跳过被删除的索引
          Object.keys(checkedDirNames.value[appName])
            .sort((a, b) => parseInt(a) - parseInt(b))
            .forEach(oldIndex => {
              if (parseInt(oldIndex) !== subIndex) {
                if (parseInt(oldIndex) > subIndex) {
                  // 如果索引大于被删除的索引，向前移动一位
                  newCheckedState[newIndex] = checkedDirNames.value[appName][oldIndex];
                } else {
                  // 如果索引小于被删除的索引，保持不变
                  newCheckedState[newIndex] = checkedDirNames.value[appName][oldIndex];
                }
                newIndex++;
              }
            });
          
          checkedDirNames.value[appName] = newCheckedState;
        }
        
        // 更新父级勾选框状态
        updateParentCheckbox(appName);
        
        // 发送分类变化事件
        emitCategoryChange();
        
        // 如果是在当前选中的父级下删除
        if (currentSelectedAppName.value === appName) {
          // 获取当前父级下所有选中的子分类
          const selectedSubs = app.dirNameList.filter((_, index) => checkedDirNames.value[appName][index]);
          
          if (selectedSubs.length > 0) {
            // 如果还有选中的子分类，更新模板列表
            const dirIds = selectedSubs.map(sub => sub.dirId).join(',');
            emit('update-app-key-dir-id', app.appKey, dirIds, selectedSubs[0].subType || '');
            
            // 判断是否为基础版式：检查dirId是否为负数或特殊标识
            const selectedDirIds = selectedSubs.map(sub => sub.dirId);
            const isBaseTemplate = selectedDirIds.some(dirId => {
              const numericDirId = parseInt(dirId);
              return numericDirId < 0 || dirId.toString().includes('base') || dirId.toString().includes('template');
            });
            
            console.log('删除分类后 - 判断是否为基础版式:', isBaseTemplate, '选中的dirIds:', selectedDirIds);
            
            // 获取模板列表数据
            const params = {
              isBase: isBaseTemplate ? 1 : 0, // 根据版式类型动态设置isBase
              appKey: app.appKey,
              dirIds: dirIds,
              page: 1,
              limit: 10,
            };
            
            try {
              const res = await api.getTemBasicList(params);
              // const res = mockGetTemBasicList
              if (res.data && Array.isArray(res.data.list)) {
                const processedTemplates = res.data.list.map(template => {
                  if (!template.pages) {
                    template.pages = [];
                  }
                  if (template.channels && template.channels.length > 0 && template.channels[0].factoryinfos) {
                    template.factoryInfos = template.channels[0].factoryinfos;
                  }
                  return template;
                });
                
                emit('update-templates', {
                  list: processedTemplates,
                  total: res.data.total || 0,
                  hasSubCategories: true
                }, true);
              }
            } catch (error) {
              console.error('获取模板列表失败:', error);
            }
          } else if (app.dirNameList.length === 0) {
            // 如果当前父级没有任何子分类了
            emit('update-app-key-dir-id', '', '');
            emit('update-templates', {
              list: [],
              total: 0,
              hasSubCategories: false
            }, true);
          } else {
            // 如果有子分类但没有选中的
            emit('update-templates', {
              list: [],
              total: 0,
              hasSubCategories: true
            }, true);
          }
        }
        
        ElMessage.success('删除成功');
      } else {
        ElMessage.error('删除失败');
      }
    } catch (error) {
      console.error("删除失败:", error);
      ElMessage.error('删除失败');
    }
  }
};

// 过滤无效的目录项
const getValidDirList = (dirList) => {
  // 增加空值检查
  if (!dirList) {
    return [];
  }
  // 只过滤掉没有dirId的项，保留正在编辑的项
  return dirList.filter(dir => dir.dirId || dir.isEditing);
};

// 显示添加图标
const showAddIcon = (index) => {
  const app = processedData.value[index];
  if (app) {
    isAddButtonVisible.value[app.appName] = true;
  }
};

// 隐藏添加图标
const hideAddIcon = (index) => {
  const app = processedData.value[index];
  if (app) {
    isAddButtonVisible.value[app.appName] = false;
  }
};

// 显示删除图标
const showDeleteIcon = (appName, subIndex) => {
  isDeleteIconVisible.value[appName][subIndex] = true;
};

// 隐藏删除图标
const hideDeleteIcon = (appName, subIndex) => {
  isDeleteIconVisible.value[appName][subIndex] = false;
};

// 在组件挂载时初始化
onMounted(() => {
  getMediaDirList();
});

// 添加watch来监听filterAppKey的变化
watch(() => props.filterAppKey, (newAppKey, oldAppKey) => {
  if (newAppKey !== oldAppKey || props.filterByAppKey) {
    console.log('filterAppKey发生变化:', oldAppKey, '->', newAppKey);
    // 当appKey变化时，重置所有状态并重新加载分类数据
    processedData.value = [];
    checkedAppNames.value = {};
    checkedDirNames.value = {};
    isSubElementsVisible.value = [];
    isAddButtonVisible.value = {};
    isDeleteIconVisible.value = {};
    parentIndeterminate.value = {};
    inputRefs.value = {};
    
    // 重置当前选中状态
    currentSelectedAppName.value = '';
    previousSelectedAppName.value = '';
    
    // 清空模板列表
    templates.value = [];
    hasFetched.value = false;
    
    // 触发数据更新
    emit('update-templates', [], true);
    emit('update-template-params', []);
    
    // 重新加载分类数据
    mediaListApiLoaded = false;
    getMediaDirList();
  }
}, { immediate: false });

// 在script setup部分添加以下方法
const inputRef = ref(null);

const setInputRef = (appName, subIndex, el) => {
  if (!inputRefs.value[appName]) {
    inputRefs.value[appName] = {};
  }
  inputRefs.value[appName][subIndex] = el;
};

const startEditing = (appName, subIndex) => {
  const app = processedData.value.find(a => a.appName === appName);
  if (app && app.dirNameList[subIndex]) {
    app.dirNameList[subIndex].isEditing = true;
    // 等待DOM更新后聚焦输入框
    nextTick(() => {
      if (inputRefs.value[appName]?.[subIndex]) {
        inputRefs.value[appName][subIndex].focus();
      }
    });
  }
};

const handleBlur = (appName, subIndex, dirId, originalDirName) => {
  const app = processedData.value.find(a => a.appName === appName);
  if (app && app.dirNameList[subIndex]) {
    const newDirName = app.dirNameList[subIndex].dirName;
    
    // 如果是空值，恢复原来的名称并提示
    if (!newDirName || newDirName.trim() === '') {
      ElMessage.warning('分类名称不能为空');
      app.dirNameList[subIndex].dirName = originalDirName;
    } else if (newDirName !== originalDirName) {
      // 只有当名称发生变化且不为空时才调用更新接口
      updateDirName(appName, subIndex, dirId, originalDirName);
    }
    
    // 退出编辑模式
    app.dirNameList[subIndex].isEditing = false;
  }
};
</script>

<style scoped lang="scss">
.media-category {
  .parent-category {
    padding:3px 8px;
    border-radius: 4px;
    margin-bottom: 8px;
    &:hover, &.active {
      background: #e8eefc;
    }
    :deep(.el-checkbox) {
      margin-right: 8px;
    }
    
    .parent-category-name {
      flex: 1;
      font-size: 13px;
      cursor: pointer;
      color: #333;
    }
    
    .add-icon {
      color: #409eff;
      margin:6px 0 0 8px;
      cursor: pointer;
      font-size: 16px;
    }
  }
  
  .sub-categories {
    padding-left: 24px;
    margin-bottom: 16px;
    
    .sub-category {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      :deep(.el-checkbox) {
        margin-right: 8px;
      }
      
      .sub-category-name {
        flex: 1;
        padding: 0 5px;
        font-size: 13px;
        color: #333;
        cursor: pointer;
        height: 32px;
        line-height: 32px;
        border-radius: 4px;
        
        &:hover {
          background-color: #f5f7fa;
        }
      }
      
      :deep(.el-input) {
        flex: 1;
        margin-right: 8px;
      }
      
      .delete-icon {
        color: #f56c6c;
        cursor: pointer;
        margin-top: 5px;
        font-size: 16px;
      }
    }
  }
}
.no-data-placeholder {
  text-align: center;
  color: #999;
  padding: 20px;
  font-size: 14px;
  display: block; /* 确保元素正常显示 */
  visibility: visible; /* 确保元素可见 */
}
</style> 