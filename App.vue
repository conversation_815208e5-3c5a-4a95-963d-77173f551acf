<script setup>
</script>

<template>
  <div class="app-container">
    <header class="app-header">
      <div class="logo-container">
        <h1>模板管理系统</h1>
      </div>
    </header>
    
  </div>
</template>

<style scoped>
.app-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background-color: #fff;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  height: 2em;
  margin-right: 12px;
}

.nav-container {
  display: flex;
  gap: 20px;
}

.nav-link {
  color: #333;
  text-decoration: none;
  font-size: 16px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.nav-link:hover {
  background-color: #f5f5f5;
}

.nav-link.router-link-active {
  color: #409eff;
  font-weight: bold;
}

.app-content {
  flex: 1;
  padding: 20px;
  background-color: #f5f7fa;
}
</style>
