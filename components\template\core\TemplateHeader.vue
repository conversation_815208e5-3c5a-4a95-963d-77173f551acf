<template>
  <div class="template-header">
    <h3 class="template-title">{{ productTitle }}</h3>
    <div class="template-name-editor">
      <el-input 
        type="text" 
        v-model="localTemplateName" 
        placeholder="请输入模板名称"
      />
      <el-icon class="edit-icon"><EditPen /></el-icon>
    </div>
    <div class="template-actions">
      <!-- <el-button type="primary" @click="handleSave">保存</el-button> -->
      <el-button type="primary" @click="handleSubmit">提交</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { EditPen } from '@element-plus/icons-vue';

// 定义props
const props = defineProps({
  templateName: {
    type: String,
    default: ''
  },
  productName: {
    type: String,
    default: '5G阅信'
  }
});

// 定义事件
const emit = defineEmits([
  'update:template-name',
  'save',
  'submit',
  'close'
]);

// 本地模板名称
const localTemplateName = ref('');

// 监听props的变化同步到本地
watch(() => props.templateName, (newVal) => {
  localTemplateName.value = newVal || '';
}, { immediate: true });

// 监听本地值变化并同步到父组件
watch(localTemplateName, (newVal) => {
  if (newVal !== props.templateName) {
    emit('update:template-name', newVal);
  }
});

// 产品标题
const productTitle = computed(() => {
  return props.productName || '5G阅信';
});

// 处理保存按钮点击
const handleSave = () => {
  console.log('保存按钮点击，当前模板名称:', localTemplateName.value);
  emit('save');
};

// 处理提交按钮点击
const handleSubmit = () => {
  console.log('提交按钮点击，当前模板名称:', localTemplateName.value);
  emit('submit');
};

// 处理关闭按钮点击
const handleClose = () => {
  emit('close');
};
</script>

<style scoped lang="scss">
.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
  background-color: #fff;
  border-bottom: 1px solid #eaeaea;
}

.template-title {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.template-name-editor {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 300px;
}

.template-actions {
  display: flex;
  gap: 10px;
}
</style> 