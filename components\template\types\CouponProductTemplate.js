import { ActionJsonGenerator, ClickEventTypeConverter } from '../../../utils/clickEventManager.js';

/**
 * 券+商品模板处理器
 * 处理 com.hbm.ecommerceCouponVertical.v2 模板
 */
export default class CouponProductTemplate {
  /**
   * 初始化模板内容
   * @param {Object} template 模板数据
   * @returns {Array} 初始化的内容数组
   */
  static initializeContents(template) {
    console.log('CouponProductTemplate - 初始化模板内容:', template);
    
    // 解析pages数据
    const pages = Array.isArray(template.pages) ? 
                  template.pages : 
                  (typeof template.pages === 'string' ? JSON.parse(template.pages) : []);
    
    if (!pages || pages.length === 0) {
      console.warn('CouponProductTemplate - 没有找到pages数据');
      return [];
    }
    
    // 获取第一个页面的contents
    const contents = pages[0]?.contents || [];
    console.log('CouponProductTemplate - 原始contents:', contents);
    
    // 为每个content添加必要的属性
    const initializedContents = contents.map(content => ({
      ...content,
      contentId: content.contentId || this.generateUniqueId(),
      userId: template.userId,
      templateId: template.templateId
    }));
    
    console.log('CouponProductTemplate - 初始化后的contents:', initializedContents);
    return initializedContents;
  }

  /**
   * 处理券+商品内容数据
   * @param {Array} contents 模板内容数组
   * @returns {Object} 处理后的券+商品数据
   */
  static handleCouponProductContent(contents) {
    console.log('CouponProductTemplate - 处理券+商品内容数据，输入contents:', contents);
    
    if (!contents || !Array.isArray(contents)) {
      console.warn('CouponProductTemplate - 输入内容无效');
      return {
        headerProduct: {},
        coupon: {},
        products: []
      };
    }
    
    // 创建positionNumber到内容的映射
    const contentMap = {};
    contents.forEach(item => {
      if (item && item.positionNumber) {
        contentMap[item.positionNumber] = item;
      }
    });
    
    const result = {
      headerProduct: {
        image: '',
        title: '',
        description: '',
        imageClickEvent: { actionType: 'OPEN_BROWSER', actionUrl: '' }
      },
      coupon: {
        amount: '',
        title: '',
        description: '',
        buttonText: '',
        buttonClickEvent: { actionType: 'OPEN_BROWSER', actionUrl: '' },
        aimCurrencyDisplay: 1
      },
      products: []
    };
    
    // 处理头部商品图片
    if (contentMap[1] && contentMap[1].type === 'image') {
      result.headerProduct.image = contentMap[1].src || '';
      result.headerProduct.content = contentMap[1].content || '';
      if (contentMap[1].actionJson) {
        result.headerProduct.imageClickEvent = this.parseActionJson(contentMap[1].actionJson);
      }
    }
    
    // 处理头部标题
    if (contentMap[2] && contentMap[2].type === 'text') {
      result.headerProduct.title = contentMap[2].content || '';
    }
    
    // 处理头部描述
    if (contentMap[3] && contentMap[3].type === 'text') {
      result.headerProduct.description = contentMap[3].content || '';
    }
    
    // 处理券信息
    if (contentMap[4] && contentMap[4].type === 'text') {
      result.coupon.amount = contentMap[4].content || '';
      result.coupon.aimCurrencyDisplay = contentMap[4].aimCurrencyDisplay !== undefined ? 
        contentMap[4].aimCurrencyDisplay : 1;
      console.log('处理券金额数据:', contentMap[4]);
    }
    
    if (contentMap[5] && contentMap[5].type === 'text') {
      result.coupon.title = contentMap[5].content || '';
    }
    
    if (contentMap[6] && contentMap[6].type === 'text') {
      result.coupon.description = contentMap[6].content || '';
    }
    
    if (contentMap[7] && contentMap[7].type === 'button') {
      result.coupon.buttonText = contentMap[7].content || '';
      if (contentMap[7].actionJson) {
        result.coupon.buttonClickEvent = this.parseActionJson(contentMap[7].actionJson);
      }
    }
    
    // 处理商品列表
    const productConfigs = [
      { start: 8 },  // 第一个商品从8开始
      { start: 13 }, // 第二个商品从13开始
      { start: 18 }  // 第三个商品从18开始
    ];
    
    productConfigs.forEach((config, index) => {
      const product = {
        id: `product-${index + 1}`,
        image: '',
        content: '', // 新增图片描述字段
        title: '',
        tag: '',
        price: '',
        buttonText: '',
        imageClickEvent: { actionType: 'OPEN_BROWSER', actionUrl: '' },
        buttonClickEvent: { actionType: 'OPEN_BROWSER', actionUrl: '' },
        actionJson: { target: '' },
        aimCurrencyDisplay: 1
      };
      
      const start = config.start;
      
      // 商品图片
      if (contentMap[start] && contentMap[start].type === 'image') {
        product.image = contentMap[start].src || '';
        product.content = contentMap[start].content || '';
        if (contentMap[start].actionJson) {
          product.imageClickEvent = this.parseActionJson(contentMap[start].actionJson);
        }
      }
      
      // 商品标题
      if (contentMap[start + 1] && contentMap[start + 1].type === 'text') {
        product.title = contentMap[start + 1].content || '';
      }
      
      // 商品标签
      if (contentMap[start + 2] && contentMap[start + 2].type === 'text') {
        product.tag = contentMap[start + 2].content || '';
      }
      
      // 商品价格
      if (contentMap[start + 3] && contentMap[start + 3].type === 'text') {
        product.price = contentMap[start + 3].content || '';
        product.aimCurrencyDisplay = contentMap[start + 3].aimCurrencyDisplay !== undefined ? 
          contentMap[start + 3].aimCurrencyDisplay : 1;
      }
      
      // 商品按钮
      if (contentMap[start + 4] && contentMap[start + 4].type === 'button') {
        product.buttonText = contentMap[start + 4].content || '';
        if (contentMap[start + 4].actionJson) {
          product.buttonClickEvent = this.parseActionJson(contentMap[start + 4].actionJson);
        }
      }
      
      // 只有当商品有内容时才添加
      if (product.image || product.title || product.price || product.buttonText) {
        result.products.push(product);
      }
    });
    
    console.log('CouponProductTemplate - 券+商品数据处理完成:', result);
    return result;
  }

  /**
   * 解析actionJson数据
   * @param {string|Object} actionJson 动作配置
   * @returns {Object} 解析后的点击事件对象
   */
  static parseActionJson(actionJson) {
    if (!actionJson) return { actionType: 'OPEN_BROWSER', actionUrl: '' };
    
    try {
      // 如果是字符串，尝试解析
      const actionData = typeof actionJson === 'string' ? 
        (actionJson.startsWith('{') ? JSON.parse(actionJson) : { target: '' }) : 
        actionJson;
      
      // 如果是逗号分隔的动作类型列表，返回默认配置
      if (typeof actionJson === 'string' && actionJson.includes(',')) {
        return { actionType: 'OPEN_BROWSER', actionUrl: '' };
      }
      
      // 使用 ActionJsonGenerator.fromClickEvent 方法处理
      // 首先构建一个标准的 clickEvent 对象
      const clickEvent = {
        type: actionData.type || 'browser',
        target: actionData.target || '',
        url: actionData.url || '',
        app: actionData.app || '',
        packageName: actionData.packageName || '',
        floorType: actionData.floorType || '0'
      };
      
      // 使用 ActionJsonGenerator.fromClickEvent 方法
      const result = ActionJsonGenerator.fromClickEvent(clickEvent);
      
      // 如果返回的是 { target: '' }，说明没有有效数据
      if (result && result.target === '') {
        return { actionType: 'OPEN_BROWSER', actionUrl: '' };
      }
      
      // 将 actionJson 格式转换为 clickEvent 格式
      const clickEventType = clickEvent.type;
      const actionType = ClickEventTypeConverter.toActionType(clickEventType);
      
      // 构建标准的点击事件对象，使用与 clickEventManager.js 一致的字段名称
      const standardClickEvent = {
        actionType: actionType,  // 使用 actionType 而不是 type
        actionUrl: result.target || ''
      };
      
      // 如果是APP类型，添加额外字段
      if (clickEventType === 'app') {
        standardClickEvent.packageName = result.packageName || '';
        standardClickEvent.floorType = result.floorType || '0';
      }
      
      return standardClickEvent;
    } catch (error) {
      console.error('解析actionJson失败:', error);
      return { actionType: 'OPEN_BROWSER', actionUrl: '' };
    }
  }

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  static generateUniqueId() {
    return 'content_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 验证券+商品数据
   * @param {Object} data 券+商品数据
   * @returns {boolean} 验证结果
   */
  static validateCouponProductData(data) {
    if (!data) return false;
    
    // 验证券信息
    if (!data.coupon || !data.coupon.amount || !data.coupon.buttonText) {
      return false;
    }
    
    // 验证商品信息（至少要有一个商品）
    if (!data.products || !Array.isArray(data.products) || data.products.length === 0) {
      return false;
    }
    
    // 验证每个商品的基本信息
    for (const product of data.products) {
      if (!product.hidden && (!product.price || !product.buttonText)) {
        return false;
      }
    }
    
    return true;
  }
} 