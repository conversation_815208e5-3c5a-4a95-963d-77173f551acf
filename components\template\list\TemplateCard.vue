<template>
  <div class="grid-content ep-bg-purple" 
    @mouseenter="handleMouseEnter" 
    @mouseleave="handleMouseLeave">
    <div class="template-item">
      <TemplatePreviewContainer
        :contents="templateContents"
        :template-type="templateType"
        :size="'small'"
        :is-card="true"
        :editable="false"
        :usage="'list'"
        :get-media-url="getContentSrc"
      />

      <div 
        :class="['temp-state', stateClass]"
        :data-state="template.state"
      >
        {{ stateText }}
      </div>
      
      <div class="template-actions" v-show="isHovered">
        <div class="top">
          <el-button @click="generateShortLink">生成短链</el-button>
          <el-button @click="viewDetails">查看详情</el-button>
        </div>
        <div class="bottom">
          <el-button @click="editTemplate"> 
            <el-icon><Edit /></el-icon>编辑
          </el-button>
          <el-button @click="sendTemplate"> 
            <el-icon><Position /></el-icon>发送
          </el-button>
          <el-button @click="deleteTemplate"> <el-icon><Delete /></el-icon>删除</el-button>
        </div>
      </div>
    </div>
    <div class="template-info">
      <p class="text-hidden">名称：{{ template.templateName }}</p>
      <div class="template-id flex-row-between">
        <p class="text-hidden">ID：{{ template.templateId }} </p>
        <el-switch 
          :model-value="template.tempIsEnabled"
          @update:model-value="handleSwitchChange"
          class="switch-cont" 
        />
      </div>
    </div>
    <div class="template-factory">
      <div class="factory-icon" v-for="factory in factoryInfos" :key="factory">
        <img :src="getFactoryIcon(factory)" />
        {{ getFactoryName(factory) }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, provide } from 'vue';
import { getMediaUrl } from '@/utils/mediaUtils';
import { getFactoryIcon, getFactoryName, parseFactoryInfos } from '@/utils/factoryUtils';
import TemplatePreviewContainer from '@/components/template/preview/TemplatePreviewContainer.vue';
import TemplateFactory from '@/factories/TemplateFactory.js';

// 定义状态映射对象
const STATE_MAP = {
  '-1': '草稿',
  '-2': '一审驳回',
  '-3': '二审驳回',
  '0': '无效',
  '1': '有效',
  '2': '一审通过',
  '3': '待提交',
  '4': '待审核',
  '5': '已审核',
  '6': '已驳回',
  '7': '已启用',
  '8': '已禁用',
  '99': '删除'
};


const props = defineProps({
  template: {
    type: Object,
    required: true
  }
});

const emit = defineEmits([
  'create', 
  'edit', 
  'delete', 
  'select', 
  'page-change', 
  'size-change',
  'generate-short-link',
  'view-details',
  'edit-template', 
  'send-template',
  'delete-template',
  'switch-change'
]);

// 为子组件提供模板信息
provide('currentTemplate', computed(() => props.template));

// 为通知类模板提供正确的设置
const notificationSettings = computed(() => {
  if (templateType.value === 'notification') {
    // 计算实际的参数对数量
    const textContents = templateContents.value.filter(c => c.type === 'text' && c.positionNumber > 2);
    const actualParamCount = Math.ceil(textContents.length / 2);
    
    // 计算实际的按钮数量
    const buttonContents = templateContents.value.filter(c => c.type === 'button');
    const actualButtonCount = buttonContents.length;
    
    // 判断是否为增强通知类
    const isEnhanced = props.template.templateName?.includes('增强') || false;
    
    
    return {
      // 增强通知类：第一对参数固定显示，所以maxVisibleParams是actualParamCount-1
      // 一般通知类：所有参数对都应该显示，所以maxVisibleParams是actualParamCount
      maxVisibleParams: isEnhanced ? Math.max(actualParamCount - 1, 2) : Math.max(actualParamCount, 2),
      maxVisibleButtons: Math.max(actualButtonCount, 2) // 最少2个按钮
    };
  }
  return {
    maxVisibleParams: 2,
    maxVisibleButtons: 2
  };
});

provide('notificationSettings', notificationSettings);

// 为多图文模板提供正确的设置
const multiTextSettings = computed(() => {
  if (templateType.value === 'multitext') {
    // 计算实际的图文对数量
    // 多图文模板的图片位置：3, 5, 7... (奇数位置从3开始)
    const imageContents = templateContents.value.filter(c => c.type === 'image' && c.positionNumber >= 3);
    const actualPairCount = imageContents.length;
    
    return {
      pairCount: Math.max(actualPairCount, 1) // 最少1对图文
    };
  }
  return {
    pairCount: 1
  };
});

provide('multiTextSettings', multiTextSettings);

const isHovered = ref(false);

// 计算属性：模板类型
const templateType = computed(() => {
  if (!props.template) return 'standard';
  
  // 完全基于cardId进行精确映射，与TemplateFactory.js保持一致
  const cardIdToTypeMap = {
    // 红包模板
    'com.hbm.redpacket': 'redpacket',
      
    // 图文模板
    'com.hbm.imageandtext': 'imageandtext',

    // 多图文模板
    'com.hbm.standardimageandtext': 'multitext',
    
    // 长文本模板
    'com.hbm.pureText': 'longtext',

    //  图片轮播16:9模板
    'com.hbm.carouselImageSixteenToNine': 'carousel',
    // 图片轮播1:1模板
    'com.hbm.carouselQuareImage': 'carousel',
    //图片轮播48:65模板
    'com.hbm.carouselVerticalImage': 'carousel',
    
    // 横滑模板
    'com.hbm.carouse': 'horizontalswipe',
    
    // 视频图文模板
    'com.hbm.videoimageandtext': 'videoimageandtext',

    // 图文视频模板
    'com.hbm.videoimageandtext2': 'videoimageandtext',
    
    // 视频模板
    'com.hbm.video': 'video',
    
    // 电商模板
    'com.hbm.ecImageAndText': 'ecommerce',

    // 多商品模板
    'com.hbm.ecommerce': 'multiproduct',

    // "券+商品(竖版)"
    'com.hbm.ecommerceCouponVertical.v2': 'couponproduct',

    // 单卡券
    'com.hbm.cardVoucher': 'cardvoucher',
    
    // 通知模板
    'com.hbm.notification': 'notification',

  };
  
  // 优先使用cardId进行精确匹配
  if (props.template.cardId && cardIdToTypeMap[props.template.cardId]) {
    return cardIdToTypeMap[props.template.cardId];
  }
  
  // 检查templateId为13的特殊情况（多商品模板）
  if (props.template.templateId === 13 || props.template.templateId === '13') {
    return 'multiproduct';
  }
  
  // 如果cardId没有匹配到，但有明确的tplType，则使用它
  if (props.template.tplType && typeof props.template.tplType === 'string') {
    return props.template.tplType.toLowerCase();
  }
  
  // 默认返回标准模板
  return 'standard';
});

// 计算属性：模板内容
const templateContents = computed(() => {
  console.log('template:', props.template);
  
  if (!props.template) {
    return [];
  }
  
  let pages = props.template.pages;
  let contents = props.template.contents;
  
  console.log('原始 pages:', pages);
  
  // 处理 pages 字段（如果是字符串则解析为数组）
  if (typeof pages === 'string') {
    try {
      pages = JSON.parse(pages);
      console.log('解析后的 pages:', pages);
    } catch (e) {
      console.error('解析 pages 字段失败:', e);
      pages = [];
    }
  }
  
  // 处理 contents 字段（如果是字符串则解析为数组）
  if (typeof contents === 'string') {
    try {
      contents = JSON.parse(contents);
    } catch (e) {
      console.error('解析 contents 字段失败:', e);
      contents = [];
    }
  }
  
  // 确保 pages 和 contents 是数组
  if (!Array.isArray(pages)) {
    pages = [];
  }
  if (!Array.isArray(contents)) {
    contents = [];
  }
  
  // 过滤掉无效的内容项
  contents = contents.filter(item => {
    if (!item || typeof item !== 'object') {
      console.warn('过滤掉无效的内容项:', item);
      return false;
    }
    return true;
  });
  
  // 判断是否为横滑模板
  const isHorizontalSwipe = templateType.value === 'horizontalswipe';
  
  if (isHorizontalSwipe && pages.length > 0) {
    console.log('处理横滑模板，pages 数量:', pages.length);
    
    // 为横滑模板创建特殊的内容结构
    let allPageContents = [];
    
    pages.forEach((page, pageIndex) => {
      console.log(`处理第 ${pageIndex + 1} 页:`, page);
      
      let pageContents = page.contents || [];
      if (typeof pageContents === 'string') {
        try {
          pageContents = JSON.parse(pageContents);
        } catch (e) {
          console.error('解析页面内容失败:', e);
          pageContents = [];
        }
      }
      
      if (Array.isArray(pageContents)) {
        // 为每个内容项添加页面索引信息
        pageContents.forEach((content, contentIndex) => {
          if (content && typeof content === 'object') {
            allPageContents.push({
              ...content,
              pageIndex: pageIndex,
              originalPageIndex: pageIndex,
              isFromPages: true
            });
          }
        });
      }
    });
    
    
    return allPageContents;
  }
  
  // 处理其他类型的模板
  if (pages && pages.length > 0) {
    console.log('使用第一页的内容:', pages[0]);
    let firstPageContents = pages[0].contents || [];
    
    if (typeof firstPageContents === 'string') {
      try {
        firstPageContents = JSON.parse(firstPageContents);
      } catch (e) {
        console.error('解析第一页内容失败:', e);
        firstPageContents = [];
      }
    }
    
    // 如果是轮播模板，需要特殊处理图片内容
    if (templateType.value === 'carousel') {
      console.log('轮播模板特殊处理');
      
      // 分离图片和非图片内容
      const imageContents = firstPageContents.filter(item => 
        item && (item.type === 'image' || item.type === 'background' || item.type === 'video')
      );
      const nonImageContents = firstPageContents.filter(item => 
        item && item.type !== 'image' && item.type !== 'background' && item.type !== 'video'
      );
      
      console.log('图片内容:', imageContents);
      console.log('非图片内容:', nonImageContents);
      
      // 如果有图片内容，创建轮播内容
      if (imageContents.length > 0) {
        // 确保每个图片内容都有有效的src
        const processedImages = imageContents.map(img => ({
          ...img,
          src: img.src || img.content || '/aim_files/aim_defult/defaultImg48_65.jpg'
        })).sort((a, b) => {
          const posA = parseInt(a.positionNumber) || 0;
          const posB = parseInt(b.positionNumber) || 0;
          return posA - posB;
        });
        
        const carouselContent = {
          contentId: 'carousel-' + Date.now(),
          type: 'image',
          isCarousel: true,
          carouselImages: processedImages,
          currentImageIndex: 0,
          templateId: props.template.templateId,
          userId: props.template.userId
        };
        
        // 将轮播内容放在最前面
        const finalContents = [carouselContent, ...nonImageContents];
        console.log('轮播模板最终内容:', finalContents);
        return finalContents;
      }
      
      return [...nonImageContents];
    }
    
    return Array.isArray(firstPageContents) ? firstPageContents : [];
  }
  
  // 如果没有 pages，使用 contents
  console.log('使用 contents 字段');
  return contents;
});


// 计算属性：状态类
const stateClass = computed(() => {
  return getStateClass(props.template.state);
});

// 计算属性：状态文本
const stateText = computed(() => {
  return STATE_MAP[props.template.state] || props.template.state;
});

// 计算属性：厂商信息
const factoryInfos = computed(() => {
  return parseFactoryInfos(props.template);
});

// 获取图片URL
const getContentSrc = (src) => {
  if (!src) return '';
  return getMediaUrl(src);
};

// 计算状态对应的样式类
const getStateClass = (state) => {
  const strState = String(state); 
  if (strState === '0' || strState === '6' || strState === '8') {
    return 'red-bg';
  } else if (strState === '1' || strState === '5' || strState === '7') {
    return 'blue-bg';
  } 
  return 'gray-bg';
};

// 鼠标进入事件
const handleMouseEnter = () => {
  isHovered.value = true;
};

// 鼠标离开事件
const handleMouseLeave = () => {
  isHovered.value = false;
};

// 事件处理函数
const generateShortLink = () => {
  if (props.template.state === 7) {
    // 状态为已启用触发生成短链事件
    emit('generate-short-link', props.template);
  } else {
    // 其他状态弹出提示
    ElMessage.warning('模板审核通过后可生成短链');
  }
};

const viewDetails = () => {
  emit('view-details', props.template);
};

const editTemplate = () => {
  if (props.template.state === -1) {
    // 状态为草稿时，触发编辑事件
    emit('edit', props.template);
  } else {
    // 其他状态弹出提示
    ElMessage.warning('提交至运营商侧模板禁止修改');
  }
};

const sendTemplate = () => {
  if (props.template.state === 7) {
    // 状态为已启用时，触发发送事件
    emit('send-template', props.template);
  } else {
    // 其他状态弹出提示
    ElMessage.warning('模板生成短链后可发送');
  }
};

const deleteTemplate = () => {
  emit('delete', props.template);
};

const handleSwitchChange = (value) => {
  emit('switch-change', props.template, value);
};
</script>

<style scoped lang="scss">
.grid-content {
  border-radius: 5px;
  overflow: hidden;
  background: #fff;
  position: relative;
  margin-bottom: 22px;
  height: 346px;
  width: 244px;
  transform: translateX(-50%);
  left: 50%;
  border: 1px solid #dcdfe6;
}

.template-item {
  position: relative;
  overflow: hidden;
  height: 220px;
  img{
    width: 100%;
    display: block;
  }
  // 调整预览容器样式
  .template-preview-container {
    width: 100%;
    height: 220px;
    overflow: hidden;
    position: relative;
  }
  
  .temp-state {
    position: absolute;
    top: 0;
    right: 0;
    height: 28px;
    line-height: 28px;
    text-align: center;
    color: #fff;
    border-radius: 0 4px 0 4px;
    font-size: 12px;
    padding: 0 18px;
    z-index: 10; // 确保状态标签在最上层
  }
  
  .red-bg {
    background-color: #f5222d;
  }

  .blue-bg {
    background-color: #1890ff;
  }

  .gray-bg {
    background-color: #76838f;
  }
}

.template-info {
  font-size: 14px;
  color: #666;
  padding: 10px 0 10px;
  margin: 0 10px 6px;
  border-bottom: 1px solid #e9e9ec;
  .template-id {
    height: 21px;
    line-height: 21px;
  }
}

.template-factory {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 3px 5px 0 10px;
  .factory-icon {
    display: flex;
    font-size: 12px;
    color: #666;
    width: 25%;
    margin-bottom: 5px;
  }
  img {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    margin-right: 2px;
  }
}

.template-actions {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 10px;
  display: flex;
  flex-direction: column;
  z-index: 20;
  
  .top {
    margin-top: 10px;
    .el-button {
      margin-bottom: 5px;
      background: none;
      color: #fff;
      border-radius: 20px;
      display: block;
      margin: 10px auto 0;
      padding: 10px 22px;
      border-color: #fff;
    }
    .el-button.is-disabled {
      color: hsla(0, 0%, 100%, .6);
      border-color: hsla(0, 0%, 100%, .6);
    }
  }
  
  .bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-around;
    padding: 10px;
    .el-button {
      color: #fff;
      display: flex;
      align-items: center;
      flex-direction: column;
      font-size: 14px;
      background: none;
      border: none;
      
      :deep(.el-icon) {
        font-size: 18px;
        margin-bottom: 5px;
        display: block;
      }
    }
    
    :deep(.el-button>span) {
      display: flex;
      flex-direction: column;
    }
    
    .el-button.is-disabled {
      color: hsla(0, 0%, 100%, .6);
    }
  }
}

.text-hidden {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.flex-row-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style> 