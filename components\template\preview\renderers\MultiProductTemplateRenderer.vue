<template>
  <div 
    class="multi-product-template" 
    @click="handleMultiProductClick"
    :style="{ '--bg-image': `url(${getMediaUrl('/aim_files/aim_defult/goodsBg.png')})` }"
  >
    <!-- 顶部标题区域 -->
    <div class="multi-product-header">
      <div class="multi-product-title">
        <span class="title-text">{{ displayData.headerTitle }}</span>
      </div>
      <div class="multi-product-subtitle isSign">{{ displayData.headerSubtitle }}</div>
    </div>

    <!-- 商品列表区域 -->
    <div class="multi-product-list">
      <div 
        v-for="(product, index) in visibleProducts" 
        :key="index"
        class="multi-product-item"
        @click="handleProductClick(product, index, $event)"
      >
        <!-- 商品图片 -->
        <div class="product-image-section">
          <img 
            :src="getImageSrc(product.image)"
            :alt="product.title || `商品${index + 1}`"
            class="product-image"
          />
        </div>

        <!-- 商品信息 -->
        <div class="product-info-section">
          <!-- 商品标题 -->
          <div class="product-title">{{ product.title }}</div>
          
          <div>
            <!-- 商品标签 -->
            <div v-if="product.tag" class="product-tag">{{ product.tag }}</div>
            <!-- 商品价格和按钮 -->
            <div class="product-price-section">
              <div class="price-wrapper">
                <span v-if="product.price" class="product-price">
                  <template v-if="product.aimCurrencyDisplay === 1">￥</template>{{ product.price }}
                </span>
              </div>
                <button 
                  class="product-button"
                  @click="handleProductButtonClick(product, index, $event)"
                >
                  {{ product.buttonText || '立即购买' }}
                </button>
            </div>
          </div>
          
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { getMediaUrl } from '@/utils/mediaUtils';

const props = defineProps({
  contents: {
    type: Array,
    default: () => []
  },
  multiProductDisplayData: {
    type: Object,
    default: null
  },
  usage: {
    type: String,
    default: 'editor'
  },
  getMediaUrl: {
    type: Function,
    default: (src) => src
  }
});

const emit = defineEmits(['select-content', 'update:content']);

// 获取图片源地址
const getImageSrc = (image) => {
  if (!image) {
    return getMediaUrl('/aim_files/aim_defult/defaultImg2.jpg');
  }
  
  if (typeof image === 'object' && image.src) {
    return getMediaUrl(image.src);
  }
  
  if (typeof image === 'string') {
    return getMediaUrl(image);
  }
  
  return getMediaUrl('/aim_files/aim_defult/defaultImg2.jpg');
};

// 处理多商品模板点击事件
const handleMultiProductClick = (event) => {
  event.preventDefault();
  event.stopPropagation();
  
  // 构建安全的数据结构
  const currentData = {
    headerTitle: displayData.value?.headerTitle || '',
    headerSubtitle: displayData.value?.headerSubtitle || '',
    products: displayData.value?.products?.map((p, index) => ({
      ...p,
      hidden: index >= 2 // 强制设置第三个及以后的商品为隐藏
    })) || [],
    selectedProductIndex: 0,
    productCount: 2 // 强制设置为2
  };

  console.log('MultiProductTemplateRenderer - handleMultiProductClick - 发送数据:', currentData);
  
  emit('select-content', {
    contentId: 'multi-product-settings',
    type: 'multi-product-settings',
    isMultiProductSettings: true,
    currentData
  });
};

// 处理单个商品点击事件
const handleProductClick = (product, index, event) => {
  event.preventDefault();
  event.stopPropagation();
  
  // 构建安全的数据结构
  const currentData = {
    headerTitle: displayData.value?.headerTitle || '',
    headerSubtitle: displayData.value?.headerSubtitle || '',
    products: displayData.value?.products?.map((p, i) => ({
      ...p,
      hidden: i >= 2 // 强制设置第三个及以后的商品为隐藏
    })) || [],
    selectedProductIndex: index,
    productCount: 2 // 强制设置为2
  };

  console.log('MultiProductTemplateRenderer - handleProductClick - 发送数据:', {
    index,
    currentData
  });
  
  emit('select-content', {
    contentId: 'multi-product-settings',
    type: 'multi-product-settings',
    isMultiProductSettings: true,
    currentData
  });
};

// 处理商品按钮点击事件
const handleProductButtonClick = (product, index, event) => {
  event.preventDefault();
  event.stopPropagation();
  
  // 构建安全的数据结构
  const currentData = {
    headerTitle: displayData.value?.headerTitle || '',
    headerSubtitle: displayData.value?.headerSubtitle || '',
    products: displayData.value?.products?.map((p, i) => ({
      ...p,
      hidden: i >= 2 // 强制设置第三个及以后的商品为隐藏
    })) || [],
    selectedProductIndex: index,
    selectedElement: 'button',
    productCount: 2 // 强制设置为2
  };

  console.log('MultiProductTemplateRenderer - handleProductButtonClick - 发送数据:', {
    index,
    currentData
  });
  
  emit('select-content', {
    contentId: 'multi-product-settings',
    type: 'multi-product-settings',
    isMultiProductSettings: true,
    currentData
  });
};

// 处理文本内容
const handleTextContent = (contents, positionNumber) => {
  const content = contents.find(item => 
    item.type === 'text' && 
    item.positionNumber === positionNumber
  );
  return content ? content.content : '';
};

// 计算显示数据
const displayData = computed(() => {
  
  // 如果有父组件传递的数据，需要确保数据格式正确
  if (props.multiProductDisplayData) {
    const data = props.multiProductDisplayData;
    
    // 确保products数组格式正确
    const products = Array.isArray(data.products) 
      ? data.products.map((p, index) => {
          // 检查price是否是按钮文本
          const price = p.price === '立即购买' ? '' : p.price;
          // 检查buttonText是否包含"_按钮"后缀
          const buttonText = (p.buttonText || '').replace('_按钮', '');
          
          // 根据productCount决定是否隐藏
          const shouldBeHidden = index >= (data.productCount || 2);
          
          return {
            id: p.id || `product-${index + 1}`,
            image: p.image || '',
            title: p.title || '',
            content: p.content || '',
            tag: p.tag || '',
            price: price || '',
            buttonText: buttonText || '立即购买',
            aimCurrencyDisplay: p.aimCurrencyDisplay ?? 1,
            buttonClickEvent: p.buttonClickEvent || { type: 'OPEN_BROWSER' },
            actionJson: p.actionJson || { target: '' },
            hidden: shouldBeHidden // 使用计算得到的隐藏状态
          };
        })
      : [];

    console.log('MultiProductTemplateRenderer - 处理后的商品数据:', products);

    return {
      headerTitle: data.headerTitle || '',
      headerSubtitle: data.headerSubtitle || '',
      products,
      selectedProductIndex: data.selectedProductIndex || 0,
      productCount: data.productCount || 2 // 默认显示2个商品
    };
  }
  
  // 默认数据结构
  if (!props.contents || props.contents.length === 0) {
    console.log('MultiProductTemplateRenderer - 没有contents数据，返回默认数据');
    return {
      headerTitle: '',
      headerSubtitle: '',
      products: [],
      selectedProductIndex: 0,
      productCount: 2
    };
  }

  // 创建positionNumber到内容的映射
  const contentMap = {};
  props.contents.forEach(item => {
    if (item && item.positionNumber) {
      contentMap[item.positionNumber] = item;
    }
  });

  // 提取标题和副标题
  // 根据MultiProductTemplate.js的generateSubmitData方法：
  // positionNumber 1: 主标题
  // positionNumber 2: 副标题
  const headerTitle = contentMap[1]?.content || '';
  const headerSubtitle = contentMap[2]?.content || '';

  // 处理商品数据
  const products = [];
  const productGroups = [
    { 
      image: 4,
      title: 7,
      tag: 8,
      currency: 6,
      price: 10,
      actualPrice: 11,
      buttonText: 12,
      buttonClick: 13
    },
    { 
      image: 14,
      title: 17,
      tag: 18,
      currency: 16,
      price: 20,
      actualPrice: 21,
      buttonText: 22,
      buttonClick: 23
    },
    { 
      image: 24,
      title: 27,
      tag: 28,
      currency: 26,
      price: 30,
      actualPrice: 31,
      buttonText: 32,
      buttonClick: 33
    }
  ];

  productGroups.forEach((positions, index) => {
    const product = {
      id: `product-${index + 1}`,
      image: '',
      title: '',
      tag: '',
      price: '',
      buttonText: '',
      aimCurrencyDisplay: 1,
      buttonClickEvent: { type: 'OPEN_BROWSER' },
      actionJson: { target: '' },
      hidden: index >= 2 // 第三个商品默认隐藏
    };

    // 商品图片
    if (contentMap[positions.image] && contentMap[positions.image].type === 'image') {
      product.image = contentMap[positions.image].src || contentMap[positions.image].content || '';
    }

    // 商品标题
    if (contentMap[positions.title] && contentMap[positions.title].type === 'text') {
      product.title = contentMap[positions.title].content || '';
    }

    // 商品标签
    if (contentMap[positions.tag] && contentMap[positions.tag].type === 'text') {
      product.tag = contentMap[positions.tag].content || '';
    }

    // 货币符号显示控制 - 使用 price 位置的 visible 属性
    if (contentMap[positions.price] && contentMap[positions.price].type === 'text') {
      product.aimCurrencyDisplay = contentMap[positions.price].visible !== undefined ? contentMap[positions.price].visible : 1;
    }

    // 商品价格 - 使用 actualPrice 位置的内容作为实际价格
    if (contentMap[positions.actualPrice] && contentMap[positions.actualPrice].type === 'text') {
      product.price = contentMap[positions.actualPrice].content || '';
    }

    // 按钮文本
    if (contentMap[positions.buttonText] && contentMap[positions.buttonText].type === 'text') {
      product.buttonText = contentMap[positions.buttonText].content || '立即购买';
    }

    // 按钮点击事件
    if (contentMap[positions.buttonClick] && contentMap[positions.buttonClick].type === 'button') {
      product.buttonClickEvent = {
        type: contentMap[positions.buttonClick].actionType || 'none',
        url: contentMap[positions.buttonClick].actionJson?.target || ''
      };
      product.actionJson = contentMap[positions.buttonClick].actionJson || { target: '' };
    }

    products.push(product);
  });

  return {
    headerTitle,
    headerSubtitle,
    products,
    selectedProductIndex: 0,
    productCount: 2 // 默认显示2个商品
  };
});


// 计算可见商品列表
const visibleProducts = computed(() => {
  if (!displayData.value?.products || !Array.isArray(displayData.value.products)) {
    return [];
  }
  
  const filtered = displayData.value.products.filter(product => !product.hidden);
  console.log('MultiProductTemplateRenderer - 可见商品:', 
    filtered.map(p => ({
      price: p.price,
      buttonText: p.buttonText,
      aimCurrencyDisplay: p.aimCurrencyDisplay
    }))
  );
  
  return filtered;
});
</script>

<style scoped lang="scss">
.multi-product-template {
  background: #ffdada var(--bg-image) no-repeat top center;
  background-size: 100%;
  padding: 16px 8px;
}

.multi-product-header {
  text-align: center;
  margin-bottom: 20px;
}

.multi-product-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  justify-content: center;
  align-items: center;
}

.multi-product-subtitle {
  font-size: 14px;
  color: #fff;
  text-align: center;
  line-height: 24px;
  height: 24px;
  background-color: initial;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: border-box;
  margin-bottom: 12px;
  position: relative;
  &:before {
    content: "【国泰海通】";
    color: #faad14;
    font-weight: normal;
    position: absolute;
    top: 0;
    left: 0;
    text-indent: 0;
  }
}
.isSign {
  position: relative;
}
.multi-product-subtitle.isSign {
    text-indent: 0;
}
.multi-product-subtitle.isSign::before {
  white-space: nowrap;
  line-height: normal;
  text-indent: 0;
  line-height: inherit;
  border-radius: 4px;
  display: inline;
  position: static;
  padding: 0;
}
.multi-product-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.multi-product-item {
  display: flex;
  align-items: flex-start;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  
  &:hover {
    background: #f0f1f2;
  }
}

.product-image-section {
  flex-shrink: 0;
  width: 106px;
  height: 106px;
  margin-right: 8px;
}

.product-image {
  width: 100%;
  height: 100%;
  // object-fit: cover;
  border-radius: 8px;
}

.product-info-section {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-title {
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  word-break: break-all;
  line-height: 18px;
  margin-bottom: 2px;
  height: 36px;
  font-size: 14px;
  color: #333;
}

.product-tag {
  display: inline-block;
  border: 1px solid #fa2a2d;
  color: #fa2a2d;
  border-radius: 3px;
  font-size: 12px;
  padding: 0 3px;
  max-width: 84px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.product-price-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.price-wrapper {
  flex: 1;
  min-width: 0;
  margin-right: 8px;
  height: 22px;
}

.product-price {
  font-size: 18px;
  color: #fa2a2d;
  display: inline-block;
  max-width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.product-button {
  flex-shrink: 0;
  width: 80px;
  height: 30px;
  line-height: 30px;
  background-color: #fa2a2d;
  border-radius: 30px;
  text-align: center;
  color: #fff;
  font-size: 14px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  border: none;
  cursor: pointer;
  padding: 0 8px;
}
</style> 