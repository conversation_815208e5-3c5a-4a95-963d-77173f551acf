<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>点击事件缓存测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .switch-btn {
            background-color: #28a745;
        }
        .switch-btn:hover {
            background-color: #1e7e34;
        }
        .clear-btn {
            background-color: #dc3545;
        }
        .clear-btn:hover {
            background-color: #c82333;
        }
        .cache-display {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>点击事件缓存测试</h1>
        <p>此测试页面用于验证点击事件设置在切换面板时是否能正确保存和恢复用户输入的内容。</p>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <!-- 图片设置面板 -->
        <div class="test-section" id="image-panel">
            <h3>图片设置面板</h3>
            <div class="form-group">
                <label>事件类型:</label>
                <select id="image-event-type">
                    <option value="OPEN_BROWSER">打开浏览器</option>
                    <option value="COPY_PARAMETER">复制内容</option>
                    <option value="OPEN_EMAIL">跳转邮箱</option>
                    <option value="OPEN_SCHEDULE">跳转日程</option>
                    <option value="OPEN_POPUP">打开弹窗</option>
                </select>
            </div>
            
            <!-- 复制内容字段 -->
            <div id="image-copy-fields" style="display: none;">
                <div class="form-group">
                    <label>复制类型:</label>
                    <select id="image-copy-type">
                        <option value="1">复制参数</option>
                        <option value="2">固定内容</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>固定内容:</label>
                    <textarea id="image-fixed-content" placeholder="请输入要复制的固定内容"></textarea>
                </div>
            </div>
            
            <!-- 邮箱字段 -->
            <div id="image-email-fields" style="display: none;">
                <div class="form-group">
                    <label>邮箱地址:</label>
                    <input type="email" id="image-email-address" placeholder="请输入邮箱地址">
                </div>
                <div class="form-group">
                    <label>邮件主题:</label>
                    <input type="text" id="image-email-subject" placeholder="请输入邮件主题">
                </div>
                <div class="form-group">
                    <label>邮件内容:</label>
                    <textarea id="image-email-body" placeholder="请输入邮件内容"></textarea>
                </div>
            </div>
            
            <!-- 日程字段 -->
            <div id="image-schedule-fields" style="display: none;">
                <div class="form-group">
                    <label>日程标题:</label>
                    <input type="text" id="image-schedule-title" placeholder="请输入日程标题">
                </div>
                <div class="form-group">
                    <label>日程内容:</label>
                    <textarea id="image-schedule-content" placeholder="请输入日程内容"></textarea>
                </div>
            </div>
            
            <!-- 弹窗字段 -->
            <div id="image-popup-fields" style="display: none;">
                <div class="form-group">
                    <label>弹窗标题:</label>
                    <input type="text" id="image-popup-title" placeholder="请输入弹窗标题">
                </div>
                <div class="form-group">
                    <label>弹窗内容:</label>
                    <textarea id="image-popup-content" placeholder="请输入弹窗内容"></textarea>
                </div>
                <div class="form-group">
                    <label>按钮文字:</label>
                    <input type="text" id="image-popup-button" placeholder="请输入按钮文字">
                </div>
            </div>
        </div>
        
        <!-- 按钮设置面板 -->
        <div class="test-section" id="button-panel">
            <h3>按钮设置面板</h3>
            <div class="form-group">
                <label>事件类型:</label>
                <select id="button-event-type">
                    <option value="OPEN_BROWSER">打开浏览器</option>
                    <option value="COPY_PARAMETER">复制内容</option>
                    <option value="OPEN_EMAIL">跳转邮箱</option>
                    <option value="OPEN_SCHEDULE">跳转日程</option>
                    <option value="OPEN_POPUP">打开弹窗</option>
                </select>
            </div>
            
            <!-- 复制内容字段 -->
            <div id="button-copy-fields" style="display: none;">
                <div class="form-group">
                    <label>复制类型:</label>
                    <select id="button-copy-type">
                        <option value="1">复制参数</option>
                        <option value="2">固定内容</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>固定内容:</label>
                    <textarea id="button-fixed-content" placeholder="请输入要复制的固定内容"></textarea>
                </div>
            </div>
            
            <!-- 邮箱字段 -->
            <div id="button-email-fields" style="display: none;">
                <div class="form-group">
                    <label>邮箱地址:</label>
                    <input type="email" id="button-email-address" placeholder="请输入邮箱地址">
                </div>
                <div class="form-group">
                    <label>邮件主题:</label>
                    <input type="text" id="button-email-subject" placeholder="请输入邮件主题">
                </div>
                <div class="form-group">
                    <label>邮件内容:</label>
                    <textarea id="button-email-body" placeholder="请输入邮件内容"></textarea>
                </div>
            </div>
            
            <!-- 日程字段 -->
            <div id="button-schedule-fields" style="display: none;">
                <div class="form-group">
                    <label>日程标题:</label>
                    <input type="text" id="button-schedule-title" placeholder="请输入日程标题">
                </div>
                <div class="form-group">
                    <label>日程内容:</label>
                    <textarea id="button-schedule-content" placeholder="请输入日程内容"></textarea>
                </div>
            </div>
            
            <!-- 弹窗字段 -->
            <div id="button-popup-fields" style="display: none;">
                <div class="form-group">
                    <label>弹窗标题:</label>
                    <input type="text" id="button-popup-title" placeholder="请输入弹窗标题">
                </div>
                <div class="form-group">
                    <label>弹窗内容:</label>
                    <textarea id="button-popup-content" placeholder="请输入弹窗内容"></textarea>
                </div>
                <div class="form-group">
                    <label>按钮文字:</label>
                    <input type="text" id="button-popup-button" placeholder="请输入按钮文字">
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="test-section">
            <h3>测试操作</h3>
            <button onclick="switchToImagePanel()" class="switch-btn">切换到图片设置</button>
            <button onclick="switchToButtonPanel()" class="switch-btn">切换到按钮设置</button>
            <button onclick="saveCurrentData()">保存当前数据</button>
            <button onclick="loadSavedData()">加载保存的数据</button>
            <button onclick="clearAllData()" class="clear-btn">清空所有数据</button>
            <button onclick="runTest()">运行自动测试</button>
        </div>
        
        <!-- 缓存显示 -->
        <div class="test-section">
            <h3>缓存数据</h3>
            <div id="cache-display" class="cache-display">缓存数据将在这里显示...</div>
            <button onclick="updateCacheDisplay()">刷新缓存显示</button>
        </div>
    </div>

    <script>
        // 模拟缓存对象
        window.CLICK_EVENT_CACHE = {};
        
        // 模拟模板数据
        window.TEMPLATE_DIALOG_DATA = {
            selectedTemplate: { cardId: 'test-template-001' },
            content: { type: 'test-content' }
        };
        
        // 当前活动面板
        let currentPanel = 'image';
        
        // 缓存键生成函数（模拟真实的缓存键生成逻辑）
        function getCacheKey(contentType, contentId) {
            const currentTemplateCardId = window.TEMPLATE_DIALOG_DATA?.selectedTemplate?.cardId || 'default';
            const contentCategory = window.TEMPLATE_DIALOG_DATA?.content?.type || 'default';
            return `${currentTemplateCardId}_${contentCategory}_${contentType}_${contentId}`;
        }
        
        // 显示状态消息
        function showStatus(message, type = 'success') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            
            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 3000);
        }
        
        // 显示/隐藏字段组
        function toggleFields(panel, eventType) {
            const prefix = panel;
            
            // 隐藏所有字段组
            document.getElementById(`${prefix}-copy-fields`).style.display = 'none';
            document.getElementById(`${prefix}-email-fields`).style.display = 'none';
            document.getElementById(`${prefix}-schedule-fields`).style.display = 'none';
            document.getElementById(`${prefix}-popup-fields`).style.display = 'none';
            
            // 显示对应的字段组
            switch(eventType) {
                case 'COPY_PARAMETER':
                    document.getElementById(`${prefix}-copy-fields`).style.display = 'block';
                    break;
                case 'OPEN_EMAIL':
                    document.getElementById(`${prefix}-email-fields`).style.display = 'block';
                    break;
                case 'OPEN_SCHEDULE':
                    document.getElementById(`${prefix}-schedule-fields`).style.display = 'block';
                    break;
                case 'OPEN_POPUP':
                    document.getElementById(`${prefix}-popup-fields`).style.display = 'block';
                    break;
            }
        }
        
        // 保存面板数据到缓存
        function savePanelData(panel) {
            const eventType = document.getElementById(`${panel}-event-type`).value;
            const cacheKey = getCacheKey(panel, 'settings');
            
            const data = {
                actionType: eventType,
                _lastUpdated: Date.now()
            };
            
            // 根据事件类型保存对应字段
            switch(eventType) {
                case 'COPY_PARAMETER':
                    data.copyType = document.getElementById(`${panel}-copy-type`).value;
                    data.fixedContent = document.getElementById(`${panel}-fixed-content`).value;
                    break;
                case 'OPEN_EMAIL':
                    data.emailAddress = document.getElementById(`${panel}-email-address`).value;
                    data.emailSubject = document.getElementById(`${panel}-email-subject`).value;
                    data.emailBody = document.getElementById(`${panel}-email-body`).value;
                    break;
                case 'OPEN_SCHEDULE':
                    data.scheduleTitle = document.getElementById(`${panel}-schedule-title`).value;
                    data.scheduleContent = document.getElementById(`${panel}-schedule-content`).value;
                    break;
                case 'OPEN_POPUP':
                    data.popupTitle = document.getElementById(`${panel}-popup-title`).value;
                    data.popupContent = document.getElementById(`${panel}-popup-content`).value;
                    data.popupButtonText = document.getElementById(`${panel}-popup-button`).value;
                    break;
            }
            
            window.CLICK_EVENT_CACHE[cacheKey] = data;
            console.log(`保存 ${panel} 面板数据:`, data);
        }
        
        // 从缓存加载面板数据
        function loadPanelData(panel) {
            const cacheKey = getCacheKey(panel, 'settings');
            const data = window.CLICK_EVENT_CACHE[cacheKey];
            
            if (!data) {
                console.log(`${panel} 面板没有缓存数据`);
                return;
            }
            
            console.log(`加载 ${panel} 面板数据:`, data);
            
            // 设置事件类型
            document.getElementById(`${panel}-event-type`).value = data.actionType || 'OPEN_BROWSER';
            toggleFields(panel, data.actionType);
            
            // 根据事件类型加载对应字段
            switch(data.actionType) {
                case 'COPY_PARAMETER':
                    if (data.copyType) document.getElementById(`${panel}-copy-type`).value = data.copyType;
                    if (data.fixedContent) document.getElementById(`${panel}-fixed-content`).value = data.fixedContent;
                    break;
                case 'OPEN_EMAIL':
                    if (data.emailAddress) document.getElementById(`${panel}-email-address`).value = data.emailAddress;
                    if (data.emailSubject) document.getElementById(`${panel}-email-subject`).value = data.emailSubject;
                    if (data.emailBody) document.getElementById(`${panel}-email-body`).value = data.emailBody;
                    break;
                case 'OPEN_SCHEDULE':
                    if (data.scheduleTitle) document.getElementById(`${panel}-schedule-title`).value = data.scheduleTitle;
                    if (data.scheduleContent) document.getElementById(`${panel}-schedule-content`).value = data.scheduleContent;
                    break;
                case 'OPEN_POPUP':
                    if (data.popupTitle) document.getElementById(`${panel}-popup-title`).value = data.popupTitle;
                    if (data.popupContent) document.getElementById(`${panel}-popup-content`).value = data.popupContent;
                    if (data.popupButtonText) document.getElementById(`${panel}-popup-button`).value = data.popupButtonText;
                    break;
            }
        }
        
        // 切换到图片面板
        function switchToImagePanel() {
            // 保存当前面板数据
            savePanelData(currentPanel);
            
            // 切换面板
            currentPanel = 'image';
            document.getElementById('image-panel').style.border = '2px solid #007bff';
            document.getElementById('button-panel').style.border = '1px solid #ddd';
            
            // 加载图片面板数据
            loadPanelData('image');
            
            showStatus('已切换到图片设置面板');
            updateCacheDisplay();
        }
        
        // 切换到按钮面板
        function switchToButtonPanel() {
            // 保存当前面板数据
            savePanelData(currentPanel);
            
            // 切换面板
            currentPanel = 'button';
            document.getElementById('button-panel').style.border = '2px solid #007bff';
            document.getElementById('image-panel').style.border = '1px solid #ddd';
            
            // 加载按钮面板数据
            loadPanelData('button');
            
            showStatus('已切换到按钮设置面板');
            updateCacheDisplay();
        }
        
        // 保存当前数据
        function saveCurrentData() {
            savePanelData(currentPanel);
            showStatus('当前数据已保存');
            updateCacheDisplay();
        }
        
        // 加载保存的数据
        function loadSavedData() {
            loadPanelData(currentPanel);
            showStatus('已加载保存的数据');
        }
        
        // 清空所有数据
        function clearAllData() {
            window.CLICK_EVENT_CACHE = {};
            
            // 清空所有输入框
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                if (input.type === 'select-one') {
                    input.selectedIndex = 0;
                } else {
                    input.value = '';
                }
            });
            
            // 隐藏所有字段组
            toggleFields('image', 'OPEN_BROWSER');
            toggleFields('button', 'OPEN_BROWSER');
            
            showStatus('所有数据已清空');
            updateCacheDisplay();
        }
        
        // 更新缓存显示
        function updateCacheDisplay() {
            const cacheDisplay = document.getElementById('cache-display');
            cacheDisplay.textContent = JSON.stringify(window.CLICK_EVENT_CACHE, null, 2);
        }
        
        // 运行自动测试
        function runTest() {
            showStatus('开始运行自动测试...', 'success');
            
            // 测试步骤1：在图片面板设置复制内容
            switchToImagePanel();
            document.getElementById('image-event-type').value = 'COPY_PARAMETER';
            toggleFields('image', 'COPY_PARAMETER');
            document.getElementById('image-copy-type').value = '2';
            document.getElementById('image-fixed-content').value = '这是图片的复制内容测试';
            
            setTimeout(() => {
                // 测试步骤2：切换到按钮面板设置跳转日程
                switchToButtonPanel();
                document.getElementById('button-event-type').value = 'OPEN_SCHEDULE';
                toggleFields('button', 'OPEN_SCHEDULE');
                document.getElementById('button-schedule-title').value = '重要会议';
                document.getElementById('button-schedule-content').value = '这是按钮的日程内容测试';
                
                setTimeout(() => {
                    // 测试步骤3：切换回图片面板，检查数据是否保留
                    switchToImagePanel();
                    
                    const imageFixedContent = document.getElementById('image-fixed-content').value;
                    if (imageFixedContent === '这是图片的复制内容测试') {
                        showStatus('✅ 测试通过：图片面板的复制内容已正确保留', 'success');
                    } else {
                        showStatus('❌ 测试失败：图片面板的复制内容丢失', 'error');
                    }
                    
                    setTimeout(() => {
                        // 测试步骤4：切换回按钮面板，检查数据是否保留
                        switchToButtonPanel();
                        
                        const buttonScheduleTitle = document.getElementById('button-schedule-title').value;
                        if (buttonScheduleTitle === '重要会议') {
                            showStatus('✅ 测试通过：按钮面板的日程标题已正确保留', 'success');
                        } else {
                            showStatus('❌ 测试失败：按钮面板的日程标题丢失', 'error');
                        }
                    }, 1000);
                }, 1000);
            }, 1000);
        }
        
        // 初始化事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 为事件类型选择器添加监听器
            document.getElementById('image-event-type').addEventListener('change', function() {
                toggleFields('image', this.value);
            });
            
            document.getElementById('button-event-type').addEventListener('change', function() {
                toggleFields('button', this.value);
            });
            
            // 为所有输入框添加自动保存监听器
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    // 延迟保存，避免频繁触发
                    clearTimeout(this.saveTimeout);
                    this.saveTimeout = setTimeout(() => {
                        savePanelData(currentPanel);
                        updateCacheDisplay();
                    }, 500);
                });
            });
            
            // 初始化显示
            switchToImagePanel();
            updateCacheDisplay();
        });
    </script>
</body>
</html>
