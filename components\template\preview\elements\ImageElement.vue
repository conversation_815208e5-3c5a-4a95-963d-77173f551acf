<template>
  <!-- 轮播图模式 - 根据模板类型和内容属性判断 -->
  <CarouselElement
    v-if="shouldShowCarousel"
    :content="props.content"
    :is-selected="isSelected"
    :usage="usage"
    :get-media-url="getMediaUrl"
    :class="[
      { 'selected': isSelected },
      customClass
    ]"
    @select="handleClick"
    @imageClick="handleImageClick"
  />
  
  <!-- 普通图片模式 -->
  <div 
    v-else
    :class="[
      elementClass,
      { 'selected': isSelected },
      customClass
    ]"
    @click="handleClick"
  >
    <!-- 视频显示 -->
    <video 
      v-if="props.content.type === 'video' && hasRealVideo"
      :src="imageSrc" 
      controls
      controlsList="nodownload"
      class="video-display"
      preload="metadata"
      @error="handleVideoError"
    ></video>
    
    <!-- 视频默认缩略图显示 -->
    <div v-else-if="props.content.type === 'video' && !hasRealVideo" class="video-placeholder">
      <img 
        :src="imageSrc" 
        :alt="props.content.content || '默认视频缩略图'"
        @error="handleImageError"
      />
      <div class="video-placeholder-icon">
        ▶
      </div>
    </div>
    
    <!-- 普通图片显示 -->
    <img 
      v-else
      :src="imageSrc" 
      :alt="props.content.content || '图片'"
      @error="handleImageError"
    />
  </div>
</template>

<script setup>
import { computed, inject } from 'vue';
import { getMediaUrl } from '@/utils/mediaUtils';
import templateFactory from '@/factories/TemplateFactory.js';
import CarouselElement from './CarouselElement.vue';

const props = defineProps({
  content: {
    type: Object,
    required: true
  },
  isSelected: {
    type: Boolean,
    default: false
  },
  usage: {
    type: String,
    default: 'editor'
  },
  getMediaUrl: {
    type: Function,
    default: (src) => src
  },
  customClass: {
    type: [String, Array],
    default: ''
  }
});

const emit = defineEmits(['select', 'imageClick']);

// 注入当前模板信息
const currentTemplate = inject('currentTemplate', null);

// 判断是否是轮播图模板
const isCarouselTemplate = computed(() => {
  if (!currentTemplate || !currentTemplate.value) {
    console.log('ImageElement - 没有模板数据');
    return false;
  }
  
  const template = currentTemplate.value;
  const result = template.cardId === 'com.hbm.carouselImageSixteenToNine' || 
                template.cardId === 'com.hbm.carouselQuareImage' || 
                template.cardId === 'com.hbm.carouselVerticalImage';
                
  console.log('ImageElement - 轮播图模板判断:', {
    模板数据: template,
    cardId: template.cardId,
    templateName: template.templateName,
    判断结果: result
  });
  
  return result;
});

// 计算元素类名
const elementClass = computed(() => {
  if (!props.content) return '';
  
  switch (props.content.type) {
    case 'image':
      return window.TEMPLATE_IS_REDPACKET ? 'preview-image circle-image' : 'preview-image';
    case 'background':
      return 'preview-background';
    case 'video':
      return 'preview-image';
    default:
      return 'preview-image';
  }
});

// 计算图片源
const imageSrc = computed(() => {
  if (!props.content || !props.content.src) {
    // 返回默认图片
    return props.getMediaUrl('/aim_files/aim_defult/defaultImg.jpg');
  }
  
  return props.getMediaUrl(props.content.src);
});

// 判断是否有实际视频
const hasRealVideo = computed(() => {
  if (props.content.type !== 'video') return false;
  
  // 检查是否有有效的视频源
  if (!props.content.src) return false;
  
  // 检查是否不是默认的视频缩略图
  if (props.content.src === props.content.defaultSrc) return false;
  
  // 检查是否不是默认图片路径
  if (props.content.src.includes('defaultVideo.jpg') || 
      props.content.src.includes('defaultImg.jpg') ||
      props.content.src.includes('default-image')) {
    return false;
  }
  
  // 如果src路径包含视频文件扩展名，则认为是真实视频
  const videoExtensions = ['.mp4', '.mov', '.avi', '.wmv', '.flv', '.webm', '.mkv'];
  const hasVideoExtension = videoExtensions.some(ext => 
    props.content.src.toLowerCase().includes(ext)
  );
  
  return hasVideoExtension;
});

// 处理点击事件
const handleClick = () => {
  emit('select', props.content);
};

// 处理轮播图中单张图片点击事件
const handleImageClick = (data) => {
  console.log('ImageElement收到图片点击事件:', data);
  emit('imageClick', data);
};

// 处理图片加载错误事件
const handleImageError = () => {
  console.error('图片加载失败');
};

// 处理视频加载错误事件
const handleVideoError = () => {
  console.error('视频加载失败');
};

// 判断是否应该显示轮播图
const shouldShowCarousel = computed(() => {
  // 如果不是轮播图模板，不显示轮播图
  if (!isCarouselTemplate.value) {
    console.log('ImageElement - 不是轮播图模板，显示普通图片');
    return false;
  }
  
  // 如果是轮播图模板，且内容是图片类型，就显示轮播图
  if (props.content && (props.content.type === 'image' || props.content.type === 'background' || props.content.type === 'video')) {
    console.log('ImageElement - 是轮播图模板且内容是图片类型，显示轮播图:', {
      内容类型: props.content.type,
      内容数据: props.content
    });
    return true;
  }
  
  console.log('ImageElement - 不满足轮播图显示条件');
  return false;
});
</script>

<style scoped lang="scss">
.preview-image {
  width: 100%;
  height: auto;
  position: relative;
  overflow: hidden;
  display: block;
  margin-bottom: 10px;
}

.preview-image img {
  width: 100%;
  height: 100%;
  display: block;
  // object-fit: cover;
  max-height: 192px;
  max-width: 342px;
}

.preview-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.preview-background img {
  width: 100%;
  height: 100%;
}

.video-display {
  width: 100%;
  height: auto;
  max-height: 192px;
  object-fit: contain;
  background-color: #000;
  display: block;
}

.video-placeholder {
  width: 100%;
  height: 192px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.video-placeholder img {
  width: 100%;
  height: 100%;
}

.video-placeholder-icon {
  width: 46px;
  height: 46px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  font-size: 20px;
  color: white;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}


.selected {
  border: 1px dashed #409eff;
  border-radius: 8px 8px 0 0;
}
</style> 