<template>
  <div 
    class="template-preview-container"
    :class="[
      { 'is-card': isCard },
      { 'is-card-redpacket': isCard && templateType === 'redpacket' },
      { 'is-card-notification': isCard && templateType === 'notification' },
      { 'is-card-multitext': isCard && templateType === 'multitext' },
      { 'is-card-longtext': isCard && templateType === 'longtext' },
      { 'is-card-imageandtext': isCard && templateType === 'imageandtext' },
      { 'is-card-carousel': isCard && templateType === 'carousel' },
      { 'is-card-ecommerce': isCard && templateType === 'ecommerce' },
      { 'is-card-multiproduct': isCard && templateType === 'multiproduct' },
      { 'is-card-couponproduct': isCard && templateType === 'couponproduct' },
      { 'is-card-horizontalswipe': isCard && templateType === 'horizontalswipe' },
      { 'is-card-cardvoucher': isCard && templateType === 'cardvoucher' },
      { 'is-editing': editable && selectedContent && !isCard },
      sizeClass
    ]"
    :data-card-id="currentTemplate?.cardId"
    @click="handleContainerClick"
  >
    <TemplatePreviewCore
      :contents="contents"
      :selected-content="selectedContent"
      :template-type="templateType"
      :editable="editable && !isCard"
      :usage="usage"
      :get-media-url="getMediaUrl"
      :ecommerce-display-data="ecommerceDisplayData"
      :multi-product-display-data="multiProductDisplayData"
      :coupon-product-display-data="couponProductDisplayData"
      @select-content="handleSelectContent"
      @update:content="handleUpdateContent"
    />
  </div>
</template>

<script setup>
import { computed, inject } from 'vue';
import TemplatePreviewCore from './TemplatePreviewCore.vue';

const props = defineProps({
  contents: {
    type: Array,
    default: () => []
  },
  selectedContent: {
    type: Object,
    default: null
  },
  templateType: {
    type: String,
    default: 'standard'
  },
  size: {
    type: String,
    default: 'normal' // 可选值: small, normal, large
  },
  isCard: {
    type: Boolean,
    default: false
  },
  editable: {
    type: Boolean,
    default: false
  },
  usage: {
    type: String,
    default: 'editor', // 可选值: 'editor' (编辑器预览), 'list' (模板列表卡片)
    validator: (value) => ['editor', 'list'].includes(value)
  },
  getMediaUrl: {
    type: Function,
    default: (src) => src
  },
  // 新增：电商模板显示数据
  ecommerceDisplayData: {
    type: Object,
    default: null
  },
  // 新增：多商品模板显示数据
  multiProductDisplayData: {
    type: Object,
    default: null
  },
  // 新增：券+商品模板显示数据
  couponProductDisplayData: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['select-content', 'update:content', 'container-click']);

// 注入当前模板信息
const currentTemplate = inject('currentTemplate', null);

// 处理容器点击，当用户点击容器但未点击内容元素时触发
const handleContainerClick = (event) => {
  // 检查点击的目标是否是可编辑的内容元素
  const isContentElement = event.target.closest('[contenteditable="true"]') || 
                          event.target.closest('.preview-title') ||
                          event.target.closest('.preview-desc') ||
                          event.target.closest('.preview-name') ||
                          event.target.closest('.red-packet-title') ||
                          event.target.closest('.red-packet-name') ||
                          event.target.closest('.red-packet-desc') ||
                          event.target.closest('.button-element') ||
                          event.target.closest('.image-element') ||
                          event.target.closest('.preview-image') ||
                          event.target.closest('.param-input') ||
                          event.target.closest('.j-btn');
  
  // 如果没有点击到内容元素，说明点击了预览区域的空白部分
  if (!isContentElement) {
    console.log('点击了预览容器的空白区域');
    
    // 为红包模板设置特殊标记，防止文本元素自动获得焦点
    if (props.templateType === 'redpacket') {
      window._isClearingSelection = true;
      setTimeout(() => {
        window._isClearingSelection = false;
      }, 100);
    }
    
    // 清除选中内容
    if (props.selectedContent) {
      emit('select-content', null);
    }
    
    // 对于通知类模板，点击预览区域空白时触发container-click事件
    // 这样可以让父组件知道需要显示通知设置面板
    if (props.templateType === 'notification') {
      console.log('通知类模板预览区域空白点击，触发container-click');
      emit('container-click');
    }
    // 对于长文本模板，点击预览区域空白时也触发container-click事件
    else if (props.templateType === 'longtext') {
      console.log('长文本模板预览区域空白点击，触发container-click');
      emit('container-click');
    }
    // 对于横滑模板，点击预览区域空白时也触发container-click事件
    else if (props.templateType === 'horizontalswipe') {
      console.log('横滑模板预览区域空白点击，触发container-click');
      emit('container-click');
    }
    // 对于电商模板，点击预览区域空白时也触发container-click事件
    else if (props.templateType === 'ecommerce') {
      console.log('电商模板预览区域空白点击，触发container-click');
      emit('container-click');
    }
    // 对于单卡券模板，点击预览区域空白时也触发container-click事件
    else if (props.templateType === 'cardvoucher') {
      console.log('单卡券模板预览区域空白点击，触发container-click');
      emit('container-click');
    }
    // 对于券+商品模板，点击预览区域空白时也触发container-click事件
    else if (props.templateType === 'couponproduct') {
      console.log('券+商品模板预览区域空白点击，触发container-click');
      emit('container-click');
    }
    // 对于其他模板（包括红包模板），不触发container-click事件，避免关闭设置面板
  }
};

// 处理内容选择
const handleSelectContent = (content) => {
  // 如果是卡片模式，不触发内容选择
  if (props.isCard) return;
  
  emit('select-content', content);
};

// 处理内容更新
const handleUpdateContent = (newContent) => {
  // 如果是卡片模式，不触发内容更新
  if (props.isCard) return;
  
  emit('update:content', newContent);
};

// 计算容器尺寸类名
const sizeClass = computed(() => {
  switch (props.size) {
    case 'small':
      return 'size-small';
    case 'large':
      return 'size-large';
    case 'normal':
    default:
      return 'size-normal';
  }
});
</script>

<style scoped lang="scss">
.template-preview-container {
  width: 100%;
  background-color: #ffffff;
  border-radius: 10px;
  // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
}
.size-normal {
	width: 342px;
}

.size-large {
  min-height: 600px;
}
.template-preview-container.is-card {
  max-width: 300px;
  cursor: pointer;
  border-radius: 0;
  
  // 禁止在卡片模式下选择文本
  :deep(*) {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    pointer-events: none !important;
  }

  .template-preview-core{
    margin-left: 0px;
    margin-right: 0px;
    width: 340px;
    transform-origin: 0px 0px;
    opacity: 1;
    transform: scale(0.71345);
  }
  .preview-desc{
    text-indent: 0 !important;
  }
}
.template-preview-container[data-card-id="com.hbm.videoimageandtext2"],
.template-preview-container[data-card-id="com.hbm.videoimageandtext"]
{
   :deep(.preview-image){
      margin-bottom: 0;
    }
}
// 红包样式特殊处理
.template-preview-container.is-card-redpacket {
  background-color: #F73050;

  :deep(.red-packet-name) {
    text-align: center;
    width: 100%;
    height: 24px;
    text-indent: 0;
    font-weight: 700;
    overflow: hidden;
    &:before {
      display: none; 
    }
  }
}
.template-preview-container[data-card-id="com.hbm.carouse"].size-normal{
  width: 438px;
}
</style> 
