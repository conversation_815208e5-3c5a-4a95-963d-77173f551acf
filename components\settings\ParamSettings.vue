<template>
  <div class="setting-container">
    <div class="setting-header" @click="toggleContent">
      <div class="setting-title">参数设置</div>
      <el-icon class="arrow-icon" :class="{ 'is-rotate': !isContentVisible }"><ArrowDown /></el-icon>
    </div>
    <div class="setting-buttons" v-show="isContentVisible">
      <ParamOperations
        source="param-settings"
        @insert-param="handleInsertParam"
        @manage-param="$emit('manage-param', $event.originalEvent)"
      />
    </div>
  </div>
</template>

<script setup>
import { ArrowDown } from '@element-plus/icons-vue';
import { ref } from 'vue';
import ParamOperations from '../common/ParamOperations.vue';
import { initParamEventBus } from '@/utils/templateEvents';

const emit = defineEmits(['insert-param', 'manage-param']);

const isContentVisible = ref(true);
const isProcessing = ref(false);

const toggleContent = () => {
  isContentVisible.value = !isContentVisible.value;
};

// 处理参数插入事件
const handleInsertParam = (event) => {
  console.log('ParamSettings收到参数插入事件:', event);
  
  try {
    if (isProcessing.value) {
      console.log('处理中，跳过重复请求');
      return;
    }
    
    isProcessing.value = true;
    
    // 初始化参数ID
    let paramId = null;
    
    // 优先从事件对象中获取参数ID
    if (event && event.paramId) {
      paramId = event.paramId;
    } else if (event && event.param && event.param.id) {
      paramId = event.param.id;
    }
    
    // 如果没有参数ID，通过全局参数管理器获取
    if (!paramId) {
      console.log('未找到参数ID，将从全局参数管理器获取');
      
      // 首先尝试使用全局参数管理器
      if (window.GLOBAL_PARAM_MANAGER && typeof window.GLOBAL_PARAM_MANAGER.getNextId === 'function') {
        try {
          paramId = window.GLOBAL_PARAM_MANAGER.getNextId();
          console.log('从全局参数管理器获取ID:', paramId);
        } catch (error) {
          console.error('从全局参数管理器获取ID失败:', error);
        }
      }
      
      // 然后尝试使用全局函数
      if (!paramId && typeof window.getNextAvailableParamId === 'function') {
        try {
          paramId = window.getNextAvailableParamId();
          console.log('使用全局函数获取ID:', paramId);
        } catch (error) {
          console.error('使用全局函数获取ID失败:', error);
        }
      }
      
      // 如果仍然没有ID，生成一个新ID
      if (!paramId) {
        try {
          // 从所有元素中获取最大ID
          const allParamElements = document.querySelectorAll('[data-param-id], input.j-btn');
          let maxId = 0;
          
          allParamElements.forEach(el => {
            const id = parseInt(el.getAttribute('data-param-id') || 0);
            if (!isNaN(id) && id > maxId) {
              maxId = id;
            }
          });
          
          // 生成新ID
          paramId = (maxId + 1).toString();
          console.log('生成新参数ID:', paramId);
        } catch (error) {
          console.error('生成参数ID失败:', error);
          paramId = "1"; // 默认ID
        }
      }
    }
    
    // 确保参数ID有效
    if (!paramId || isNaN(parseInt(paramId))) {
      paramId = "1";
      console.warn('无法获取有效的参数ID，使用默认值: 1');
    }
    
    console.log('准备发送参数数据，ID为:', paramId);
    
    // 准备要发送的参数数据
    const paramData = {
      source: 'param-settings',
      event: event,
      timestamp: Date.now(),
      paramId: paramId // 确保paramId在顶层
    };
    
    // 确保记录参数使用情况
    if (window.GLOBAL_PARAM_MANAGER && typeof window.GLOBAL_PARAM_MANAGER.recordParamUsage === 'function') {
      try {
        window.GLOBAL_PARAM_MANAGER.recordParamUsage(paramId);
      } catch (error) {
        console.error('记录参数使用情况时出错:', error);
      }
    }
    
    // 使用全局事件总线发送参数插入事件
    const paramEventBus = initParamEventBus();
    if (paramEventBus) {
      console.log('通过全局参数事件总线发送参数插入事件:', paramData);
      paramEventBus.emit('insert-param', paramData);
    } else {
      // 只有在全局事件总线不可用时，才使用组件事件
      console.log('全局参数事件总线不可用，使用组件事件:', paramData);
      emit('insert-param', paramData);
    }
  } catch (error) {
    console.error('处理参数插入事件时出错:', error);
  } finally {
    // 重置处理状态
    setTimeout(() => {
      isProcessing.value = false;
    }, 300);
  }
};
</script>

<style scoped lang="scss">
.setting-container {
  margin-bottom: 15px;

  .setting-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f2f2f2;
    padding: 10px 14px;
    cursor: pointer;

    .setting-title {
      font-size: 14px;
      font-weight: bold;
      color: #333;
    }

    .arrow-icon {
      font-size: 16px;
      color: #666;
      transition: transform 0.3s;
      
      &.is-rotate {
        transform: rotate(-180deg);
      }
    }
  }

  .setting-buttons {
    display: flex;
    padding: 15px;
    gap: 10px;
    background-color: #fff;
  }
}
</style> 