<template>
  <div class="template-preview-core">
    <component 
      :is="rendererComponent"
      :contents="contents"
      :selected-content="selectedContent"
      :editable="editable"
      :usage="usage"
      :get-media-url="getMediaUrl"
      :ecommerce-display-data="ecommerceDisplayData"
      :multi-product-display-data="multiProductDisplayData"
      :coupon-product-display-data="couponProductDisplayData"
      @select-content="handleSelectContent"
      @update:content="handleUpdateContent"
      @show-ecommerce-settings="handleShowEcommerceSettings"
    />
  </div>
</template>

<script setup>
import { computed, inject } from 'vue';
import StandardTemplateRenderer from './renderers/StandardTemplateRenderer.vue';
import RedPacketTemplateRenderer from './renderers/RedPacketTemplateRenderer.vue';
import NotificationTemplateRenderer from './renderers/NotificationTemplateRenderer.vue';
import MultiTextTemplateRenderer from './renderers/MultiTextTemplateRenderer.vue';
import LongTextTemplateRenderer from './renderers/LongTextTemplateRenderer.vue';
import EcommerceTemplateRenderer from './renderers/EcommerceTemplateRenderer.vue';
import HorizontalSwipeTemplateRenderer from './renderers/HorizontalSwipeTemplateRenderer.vue';
import MultiProductTemplateRenderer from './renderers/MultiProductTemplateRenderer.vue';
import CouponProductTemplateRenderer from './renderers/CouponProductTemplateRenderer.vue';
import CardVoucherTemplateRenderer from './renderers/CardVoucherTemplateRenderer.vue';

const props = defineProps({
  contents: {
    type: Array,
    default: () => []
  },
  selectedContent: {
    type: Object,
    default: null
  },
  templateType: {
    type: String,
    default: 'standard' // 可选值: standard, redpacket, notification, multitext, longtext, ecommerce, horizontalswipe, multiproduct
  },
  editable: {
    type: Boolean,
    default: false
  },
  usage: {
    type: String,
    default: 'editor' // 可选值: 'editor', 'list'
  },
  getMediaUrl: {
    type: Function,
    default: (src) => src
  },
  // 新增：电商模板显示数据
  ecommerceDisplayData: {
    type: Object,
    default: null
  },
  // 新增：多商品模板显示数据
  multiProductDisplayData: {
    type: Object,
    default: null
  },
  // 新增：券+商品模板显示数据
  couponProductDisplayData: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['select-content', 'update:content']);

// 注入通知设置，如果没有则使用默认值
const notificationSettings = inject('notificationSettings', {
  maxVisibleParams: 2,
  maxVisibleButtons: 2
});

// 注入多图文设置，如果没有则使用默认值
const multiTextSettings = inject('multiTextSettings', {
  pairCount: 1
});

// 注入长文本设置，如果没有则使用默认值
const longTextSettings = inject('longTextSettings', {
  showTitle: true,
  showDescription: true
});

// 根据模板类型选择渲染器组件
const rendererComponent = computed(() => {
  console.log('=== rendererComponent 调试信息 ===');
  console.log('props.templateType:', props.templateType);
  
  let selectedRenderer;
  
  switch (props.templateType) {
    case 'redpacket':
      selectedRenderer = RedPacketTemplateRenderer;
      break;
    case 'notification':
      selectedRenderer = NotificationTemplateRenderer;
      break;
    case 'multitext':
      selectedRenderer = MultiTextTemplateRenderer;
      break;
    case 'longtext':
      selectedRenderer = LongTextTemplateRenderer;
      break;
    case 'ecommerce':
      selectedRenderer = EcommerceTemplateRenderer;
      break;
    case 'multiproduct':
      selectedRenderer = MultiProductTemplateRenderer;
      break;
    case 'couponproduct':
      selectedRenderer = CouponProductTemplateRenderer;
      break;
    case 'cardvoucher':
      selectedRenderer = CardVoucherTemplateRenderer;
      break;
    case 'horizontalswipe':
      selectedRenderer = HorizontalSwipeTemplateRenderer;
      break;
    case 'standard':
    default:
      selectedRenderer = StandardTemplateRenderer;
      break;
  }
  
  console.log('选择的渲染器组件:', selectedRenderer);
  
  return selectedRenderer;
});

// 处理内容选择
const handleSelectContent = (content) => {
  emit('select-content', content);
};

// 处理内容更新
const handleUpdateContent = (content) => {
  emit('update:content', content);
};

// 处理电商设置显示
const handleShowEcommerceSettings = (settingsData) => {
  console.log('TemplatePreviewCore - 显示电商设置:', settingsData);
  emit('select-content', settingsData);
};
</script>

<style scoped lang="scss">
.template-preview-core {
  width: 100%;
  height: 100%;
  position: relative;
}
</style> 