/**
 * 模板工厂
 * 负责管理和创建不同类型的模板处理器
 * 完全基于 cardId 进行精确映射，不再依赖文字内容推测
 */

import StandardTemplate from '@/components/template/types/StandardTemplate.js'
import RedPacketTemplate from '@/components/template/types/RedPacketTemplate.js'
import NotificationTemplate from '@/components/template/types/NotificationTemplate.js'
import MultiTextTemplate from '@/components/template/types/MultiTextTemplate.js'
import LongTextTemplate from '@/components/template/types/LongTextTemplate.js'
import EcommerceTemplate from '@/components/template/types/EcommerceTemplate.js'
import HorizontalSwipeTemplate from '@/components/template/types/HorizontalSwipeTemplate.js'
import MultiProductTemplate from '@/components/template/types/MultiProductTemplate.js'
import CardVoucherTemplate from '@/components/template/types/CardVoucherTemplate.js'
import CouponProductTemplate from '@/components/template/types/CouponProductTemplate.js'

class TemplateFactory {
  constructor() {
    // 轮播图模板的cardId列表
    this.CAROUSEL_CARD_IDS = [
      'com.hbm.carouselImageSixteenToNine',
      'com.hbm.carouselQuareImage', 
      'com.hbm.carouselVerticalImage'
    ];
    
    // 横滑模板的cardId列表
    this.HORIZONTAL_SWIPE_CARD_IDS = [
      'com.hbm.carouse'
    ];
    
    // 模板类型注册表
    this.templateTypes = {
      'standard': StandardTemplate, // 标准模板使用标准模板处理器
      'redpacket': RedPacketTemplate, // 红包模板使用红包模板处理器 
      'notification': NotificationTemplate, // 通知模板使用通知模板处理器
      'multitext': MultiTextTemplate, // 多图文模板使用多图文模板处理器
      'longtext': LongTextTemplate, // 长文本模板使用长文本模板处理器
      'ecommerce': EcommerceTemplate, // 电商模板使用电商模板处理器
      'horizontalswipe': HorizontalSwipeTemplate, // 新增横滑模板
      'multiproduct': MultiProductTemplate, // 新增多商品模板
      'cardvoucher': CardVoucherTemplate, // 新增单卡券模板
      'couponproduct': CouponProductTemplate, // 新增券+商品模板
      'imageandtext': StandardTemplate, // 图文模板使用标准模板处理器
      'carousel': StandardTemplate, // 轮播图模板使用标准模板处理器
      'videoimageandtext': StandardTemplate, // 视频图文模板使用标准模板处理器
      'video': StandardTemplate, // 视频模板使用标准模板处理器
    }
  }

  /**
   * 获取模板处理器
   * @param {string} type 模板类型
   * @returns {Object} 模板处理器
   */
  getTemplateHandler(type) {
    const typeStr = (type && typeof type === 'string') ? type.toString().toLowerCase() : 'standard';
    return this.templateTypes[typeStr] || this.templateTypes['standard'];
  }

  /**
   * 根据模板cardId精确检测模板类型
   * @param {Object} template 模板数据
   * @returns {string} 模板类型
   */
  detectTemplateType(template) {
    console.log('传入的template:', template);
    
    if (!template) {
      console.log('template为空，返回standard');
      return 'standard';
    }
    
    // 完全基于cardId进行精确映射
    const cardIdToTypeMap = {
      // 红包模板
      'com.hbm.redpacket': 'redpacket',
      
      // 图文模板
      'com.hbm.imageandtext': 'imageandtext',

      // 多图文模板
      'com.hbm.standardimageandtext': 'multitext',
      
      // 长文本模板
      'com.hbm.pureText': 'longtext',

      //  图片轮播16:9模板
      'com.hbm.carouselImageSixteenToNine': 'carousel',
      // 图片轮播1:1模板
      'com.hbm.carouselQuareImage': 'carousel',
      //图片轮播48:65模板
      'com.hbm.carouselVerticalImage': 'carousel',
      
      // 横滑模板
      'com.hbm.carouse': 'horizontalswipe',
      
      // 视频图文模板
      'com.hbm.videoimageandtext': 'videoimageandtext',

      // 图文视频模板
      'com.hbm.videoimageandtext2': 'videoimageandtext',
      
      // 视频模板
      'com.hbm.video': 'video',
      
      // 电商模板
      'com.hbm.ecImageAndText': 'ecommerce',

      // 多商品模板
      'com.hbm.ecommerce': 'multiproduct',

      // 券+商品(竖版)模板
      'com.hbm.ecommerceCouponVertical.v2': 'couponproduct',

      // 单卡券
      'com.hbm.cardVoucher': 'cardvoucher',
      
      // 通知模板
      'com.hbm.notification': 'notification',

    };
    
    // 优先使用cardId进行精确匹配
    if (template.cardId && cardIdToTypeMap[template.cardId]) {
      const matchedType = cardIdToTypeMap[template.cardId];
      console.log('cardId匹配成功，返回类型:', matchedType);
      return matchedType;
    }
    
    console.log('cardId未匹配到任何类型');
    
    // 检查templateId为13的特殊情况（根据用户提到的API中templateId: 13是多商品模板）
    if (template.templateId === 13 || template.templateId === '13') {
      return 'multiproduct';
    }
    
    // 如果cardId没有匹配到，但有明确的templateType元数据，则使用它
    if (template.templateType && typeof template.templateType === 'string') {
      return template.templateType.toLowerCase();
    }
    return 'standard';
  }

  /**
   * 判断是否是轮播图模板（基于cardId精确匹配）
   * @param {Object} template 模板数据
   * @returns {boolean} 是否轮播图模板
   */
  isCarouselTemplate(template) {
    if (!template) return false;
    return this.CAROUSEL_CARD_IDS.includes(template.cardId);
  }

  /**
   * 判断是否是横滑模板（基于cardId精确匹配）
   * @param {Object} template 模板数据
   * @returns {boolean} 是否横滑模板
   */
  isHorizontalSwipeTemplate(template) {
    if (!template) return false;
    return this.HORIZONTAL_SWIPE_CARD_IDS.includes(template.cardId);
  }

  /**
   * 创建模板处理器实例
   * @param {string} type 模板类型
   * @returns {Object} 模板处理器实例
   */
  createProcessor(type) {
    const HandlerClass = this.getTemplateHandler(type);
    if (HandlerClass) {
      if (typeof HandlerClass === 'function' && HandlerClass.prototype && HandlerClass.prototype.constructor === HandlerClass) {
        return new HandlerClass();
      } else if (typeof HandlerClass === 'object') {
        return HandlerClass;
      }
    }
    return null;
  }

  /**
   * 注册新模板类型
   * @param {string} type 模板类型
   * @param {Object} handler 模板处理器
   */
  registerTemplateType(type, handler) {
    if (type && typeof type === 'string') {
      this.templateTypes[type.toLowerCase()] = handler;
    }
  }
}

// 创建单例
export default new TemplateFactory() 